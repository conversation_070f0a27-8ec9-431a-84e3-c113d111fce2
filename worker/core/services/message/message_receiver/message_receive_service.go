package message_receiver

import (
	"context"
	"errors"
	"fmt"
	"log/slog"
	"slices"
	"time"

	"github.com/goforj/godump"
	"github.com/google/uuid"
	"gorm.io/gorm"

	"digisac-go/worker/config"
	"digisac-go/worker/core/adapters/adapter_types"
	"digisac-go/worker/core/adapters/base_waba_adapter"
	"digisac-go/worker/core/http/payloads"
	"digisac-go/worker/core/managers/service_manager"
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/repositories"
	"digisac-go/worker/core/services/campaign"
	"digisac-go/worker/core/services/contact/contact_service"
	"digisac-go/worker/core/services/crypto"
	queueDispatcher "digisac-go/worker/core/services/dispatcher/queue"
	"digisac-go/worker/core/services/evaluation"
	"digisac-go/worker/core/services/event"
	fileService "digisac-go/worker/core/services/file"
	storageService "digisac-go/worker/core/services/storage"
	ticketService "digisac-go/worker/core/services/ticket"
	"digisac-go/worker/core/utils/concurrency"
	lockutils "digisac-go/worker/core/utils/lock_utils"
)

type ReceiveService struct {
	accountCryptor         *crypto.AccountCryptor
	messageRepository      repositories.MessageRepository
	contactRepository      repositories.ContactRepository
	serviceRepository      repositories.ServiceRepository
	ticketRepository       repositories.TicketRepository
	fileRepository         repositories.FileRepository
	templateRepository     repositories.WhatsappBusinessTemplateRepository
	ticketService          *ticketService.TicketService
	adapterManager         *service_manager.ServiceManager
	queueDispatcher        *queueDispatcher.QueueJobsDispatcherService
	config                 *config.Config
	storageService         *storageService.StorageService
	evaluationService      *evaluation.EvaluationService
	distributedLockFactory concurrency.DistributedLockFactory
	campaignService        *campaign.CampaignService
	contactService         *contact_service.ContactService
	eventService           *event.EventService
	fileService            *fileService.FileService
}

func NewReceiveService(
	accountCryptor *crypto.AccountCryptor,
	messageRepository repositories.MessageRepository,
	contactRepository repositories.ContactRepository,
	serviceRepository repositories.ServiceRepository,
	ticketRepository repositories.TicketRepository,
	fileRepository repositories.FileRepository,
	templateRepository repositories.WhatsappBusinessTemplateRepository,
	ticketService *ticketService.TicketService,
	adapterManager *service_manager.ServiceManager,
	queueDispatcher *queueDispatcher.QueueJobsDispatcherService,
	config *config.Config,
	storageService *storageService.StorageService,
	evaluationService *evaluation.EvaluationService,
	distributedLockFactory concurrency.DistributedLockFactory,
	campaignService *campaign.CampaignService,
	contactService *contact_service.ContactService,
	eventService *event.EventService,
	fileService *fileService.FileService,
) *ReceiveService {
	return &ReceiveService{
		accountCryptor:         accountCryptor,
		messageRepository:      messageRepository,
		contactRepository:      contactRepository,
		serviceRepository:      serviceRepository,
		ticketRepository:       ticketRepository,
		fileRepository:         fileRepository,
		templateRepository:     templateRepository,
		ticketService:          ticketService,
		adapterManager:         adapterManager,
		queueDispatcher:        queueDispatcher,
		config:                 config,
		storageService:         storageService,
		evaluationService:      evaluationService,
		distributedLockFactory: distributedLockFactory,
		campaignService:        campaignService,
		contactService:         contactService,
		eventService:           eventService,
		fileService:            fileService,
	}
}

func (m *ReceiveService) receiveReaction(
	ctx context.Context,
	builtWebhook *adapter_types.BuiltWebhook,
	contact *models.Contact,
	isFromMe bool,
	fromId uuid.UUID,
	serviceId uuid.UUID,
	accountId uuid.UUID,
	transaction repositories.Option,
	etx *event.Etx,
) (err error) {
	etx2 := m.eventService.BeginTransaction(etx)

	// Busca pela mensagem pai
	message, err := m.messageRepository.FindOne(ctx, transaction, repositories.WithQueryStruct(map[string]interface{}{
		"serviceId": serviceId,
		"accountId": accountId,
	}),
		repositories.WithQuery(func(d *gorm.DB) *gorm.DB {
			if builtWebhook.Message.WhatsappId != "" {
				return d.Where(`"idFromService" = ? or data->>'whatsappMessageId' = ?`, builtWebhook.Message.Id, builtWebhook.Message.WhatsappId)
			}
			return d.Where(`"idFromService" = ?`, builtWebhook.Message.Id)
		}))

	if err != nil {
		slog.ErrorContext(ctx, "Failed to find parent message",
			slog.String("messageId", builtWebhook.Message.Id),
			slog.Bool("isFromMe", builtWebhook.Reaction.IsFromMe),
			slog.String("serviceId", serviceId.String()),
			slog.String("accountId", accountId.String()),
			slog.String("error", err.Error()))

		return fmt.Errorf("failed to find parent message: %w", err)
	}

	// Busca reações anteriores
	createdReactionMessages, err := m.messageRepository.FindMany(ctx, transaction, repositories.WithQueryStruct(map[string]interface{}{
		"reactionParentMessageId": message.Id,
		"type":                    "reaction",
		"contactId":               contact.Id,
		"isFromMe":                isFromMe,
		"fromId":                  fromId,
		"serviceId":               serviceId,
		"accountId":               accountId,
	}))

	if err != nil && err.Error() != "record not found" {
		slog.ErrorContext(ctx, "Failed to find reaction messages",
			slog.String("messageId", message.Id.String()),
			slog.String("contactId", contact.Id.String()),
			slog.String("serviceId", serviceId.String()),
			slog.String("accountId", accountId.String()),
			slog.String("error", err.Error()))
		return fmt.Errorf("failed to find reaction messages: %w", err)
	}

	var lastReactionMessage *models.Message
	var removedReactions []*models.Message

	for _, createdReactionMessage := range createdReactionMessages {
		// Reação removida pois está sem texto
		if createdReactionMessage.Text == "" {
			removedReactions = append(removedReactions, createdReactionMessage)
		} else {
			text, err := m.accountCryptor.DecryptTextForAccount(ctx, contact.Account, createdReactionMessage.Text, false)
			if err != nil {
				slog.ErrorContext(ctx, "Failed to decrypt reaction message text",
					slog.String("messageId", createdReactionMessage.Id.String()),
					slog.String("accountId", contact.Account.Id.String()),
					slog.String("error", err.Error()))
				return fmt.Errorf("failed to decrypt reaction message text: %w", err)
			}

			var existsReaction bool
			// Busca reações que não estão nas novas, então foi removida
			for _, newReaction := range builtWebhook.Reaction.NewReactions {
				if newReaction.Emoji == text {
					existsReaction = true
				}
			}

			if !existsReaction {
				removedReactions = append(removedReactions, createdReactionMessage)
			}
		}
	}

	// Cria reações não existentes
	for _, newReaction := range builtWebhook.Reaction.NewReactions {
		var existingReaction *models.Message

		// Busca a nova reação nas reações criadas
		for _, createdReactionMessage := range createdReactionMessages {
			text, err := m.accountCryptor.DecryptTextForAccount(ctx, contact.Account, createdReactionMessage.Text, false)
			if err != nil {
				slog.ErrorContext(ctx, "Failed to decrypt reaction text",
					slog.String("messageId", createdReactionMessage.Id.String()),
					slog.String("accountId", contact.Account.Id.String()),
					slog.String("error", err.Error()))
				return fmt.Errorf("failed to decrypt reaction text: %w", err)
			}

			if text == newReaction.Emoji {
				existingReaction = createdReactionMessage
				break
			}
		}

		// Reação já existe
		if existingReaction != nil {
			fmt.Printf("Reaction message already exists %s", existingReaction.IdFromService)
			return nil
		}

		// Reação não foi encontrada, precisa criar uma nova
		text := ""

		if newReaction.Emoji != "" {
			text, err = m.accountCryptor.EncryptTextForAccount(ctx, contact.Account, newReaction.Emoji, false)
			if err != nil {
				slog.ErrorContext(ctx, "Failed to encrypt reaction emoji",
					slog.String("accountId", contact.Account.Id.String()),
					slog.String("emoji", newReaction.Emoji),
					slog.String("error", err.Error()))
				return fmt.Errorf("failed to encrypt reaction emoji: %w", err)
			}
		}

		// Se existe alguma removida, apenas atualiza o texto dela para não duplicar registros.
		if len(removedReactions) > 0 {
			lastRemovedReaction := removedReactions[len(removedReactions)-1]
			removedReactions = slices.Delete(removedReactions, len(removedReactions)-1, len(removedReactions))

			err = m.messageRepository.UpdateByIdInPlace(ctx, lastRemovedReaction.Id, lastRemovedReaction, func() {
				lastRemovedReaction.Text = text
				lastRemovedReaction.Timestamp = builtWebhook.Reaction.Timestamp
			}, transaction)

			if err != nil {
				slog.ErrorContext(ctx, "Failed to update removed reaction",
					slog.String("messageId", lastRemovedReaction.Id.String()),
					slog.String("error", err.Error()))
				return fmt.Errorf("failed to update removed reaction: %w", err)
			}

			err = m.eventService.Dispatch(
				ctx,
				event.EventMessageUpdated,
				&event.DispatchPayload{
					Id:        lastRemovedReaction.Id,
					AccountId: lastRemovedReaction.AccountId,
				}, &event.EventServiceDispatchOptions{
					DebounceKey: lastRemovedReaction.Id.String(),
					Etx:         etx2,
				})

			if err != nil {
				slog.ErrorContext(ctx, "Failed to receive reaction on dispatch message updated event", slog.String("error", err.Error()), slog.Any("message", lastRemovedReaction))
				return fmt.Errorf("failed to receive reaction on dispatch message updated event: %w", err)
			}

			lastReactionMessage = lastRemovedReaction

			// Atualiza lista reações
			for index, createdReactionMessage := range createdReactionMessages {
				if createdReactionMessage.Id == lastRemovedReaction.Id {
					createdReactionMessages[index] = lastRemovedReaction
				}
			}
			continue
		}

		var origin string
		if isFromMe {
			origin = "user"
		}

		idFromService := message.Id.String() + "-" + fmt.Sprintf("%v", builtWebhook.Reaction.Timestamp.Unix())

		lastReactionMessage = &models.Message{
			Text:          text,
			IdFromService: idFromService,
			Timestamp:     builtWebhook.Reaction.Timestamp,
			Type:          "reaction",
			Data: &models.MessageData{
				Ack:     1,
				IsNew:   false,
				IsFirst: false,
			},
			IsFromMe:                isFromMe,
			FromId:                  fromId,
			ContactId:               contact.Id,
			ServiceId:               contact.ServiceId,
			AccountId:               contact.AccountId,
			Origin:                  origin,
			ReactionParentMessageId: message.Id,
		}

		err = m.messageRepository.Create(ctx, lastReactionMessage, transaction)

		if err != nil {
			slog.ErrorContext(ctx, "Failed to create reaction message",
				slog.String("messageId", idFromService),
				slog.String("contactId", contact.Id.String()),
				slog.String("error", err.Error()))
			return fmt.Errorf("failed to create reaction message: %w", err)
		}

		err = m.eventService.Dispatch(
			ctx,
			event.EventMessageCreated,
			&event.DispatchPayload{
				Id:        lastReactionMessage.Id,
				AccountId: lastReactionMessage.AccountId,
			}, &event.EventServiceDispatchOptions{
				DebounceKey: lastReactionMessage.Id.String(),
				Etx:         etx2,
			})

		if err != nil {
			slog.ErrorContext(ctx, "Failed to receive reaction on dispatch message created event", slog.String("error", err.Error()), slog.Any("message", lastReactionMessage))
			return fmt.Errorf("failed to receive reaction on dispatch message created event: %w", err)
		}
	}

	if !message.Data.HasReaction {
		message.Data.HasReaction = true

		err = m.messageRepository.UpdateById(ctx, message.Id, message, transaction)

		if err != nil {
			slog.ErrorContext(ctx, "Failed to update message reaction status",
				slog.String("messageId", message.Id.String()),
				slog.String("error", err.Error()))
			return fmt.Errorf("failed to update message reaction status: %w", err)
		}

		err = m.eventService.Dispatch(
			ctx,
			event.EventMessageUpdated,
			&event.DispatchPayload{
				Id:        message.Id,
				AccountId: message.AccountId,
			}, &event.EventServiceDispatchOptions{
				DebounceKey: message.Id.String(),
				Etx:         etx2,
			})

		if err != nil {
			slog.ErrorContext(ctx, "Failed to receive reaction on dispatch message updated event", slog.String("error", err.Error()), slog.Any("message", message))
			return fmt.Errorf("failed to receive reaction on dispatch message updated event: %w", err)
		}
	}

	if lastReactionMessage != nil && (contact.LastMessageAt != nil || lastReactionMessage.Timestamp != nil && lastReactionMessage.Timestamp.After(*contact.LastMessageAt)) {
		contact.LastMessageId = lastReactionMessage.Id
		contact.LastMessageAt = lastReactionMessage.Timestamp
		contact.Visible = true
		contact.HadChat = true

		if contact.Unread == 0 {
			contact.Unread++
		}

		err = m.contactRepository.UpdateById(ctx, contact.Id, contact, transaction)

		if err != nil {
			slog.ErrorContext(ctx, "Failed to update contact last message",
				slog.String("contactId", contact.Id.String()),
				slog.String("messageId", lastReactionMessage.Id.String()),
				slog.String("error", err.Error()))
			return fmt.Errorf("failed to update contact last message: %w", err)
		}

		err = m.eventService.Dispatch(
			ctx,
			event.EventContactUpdated,
			&event.DispatchPayload{
				Id:        message.Id,
				AccountId: message.AccountId,
			}, &event.EventServiceDispatchOptions{
				DebounceKey: contact.Id.String(),
				Etx:         etx2,
			})

		if err != nil {
			slog.ErrorContext(ctx, "Failed to receive reaction on dispatch contact updated event", slog.String("error", err.Error()), slog.Any("contact", contact))
			return fmt.Errorf("failed to receive reaction on dispatch contact updated event: %w", err)
		}
	}

	if etx == nil {
		err = etx2.Commit()
		if err != nil {
			slog.ErrorContext(ctx, "Failed to commit event transaction", slog.String("error", err.Error()))
			return fmt.Errorf("failed to commit event transaction: %w", err)
		}
	}

	return nil
}

func (m *ReceiveService) ReceiveService(ctx context.Context, payload *payloads.ReceiveServiceWebhookPayloadPayload) (err error) {
	service, err := m.serviceRepository.FindById(ctx, payload.ServiceId)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to find service",
			slog.String("serviceId", payload.ServiceId.String()),
			slog.String("error", err.Error()))
		return fmt.Errorf("failed to find service: %w", err)
	}

	err = m.serviceRepository.UpdateByIdInPlace(ctx, service.Id, service, func() {
		service.Data.MyId = payload.BuiltWebhook.Service.Data.MyId
		service.Data.Status = payload.BuiltWebhook.Service.Data.Status
		service.Data.Error = ""
	})

	if err != nil {
		slog.ErrorContext(ctx, "Failed to update service",
			slog.String("serviceId", payload.ServiceId.String()),
			slog.String("myId", payload.BuiltWebhook.Service.Data.MyId),
			slog.Any("status", payload.BuiltWebhook.Service.Data.Status),
			slog.String("error", err.Error()))
		return fmt.Errorf("failed to update service: %w", err)
	}

	err = m.eventService.Dispatch(
		ctx,
		event.EventServiceUpdated,
		&event.DispatchPayload{
			Id:        service.Id,
			AccountId: service.AccountId,
		},
		&event.EventServiceDispatchOptions{
			DebounceKey:   service.Id.String(),
			DebounceDelay: time.Second * 2,
		})

	if err != nil {
		slog.ErrorContext(ctx, "Failed to dispatch service updated event", slog.String("error", err.Error()), slog.Any("service", service))
		return fmt.Errorf("failed to dispatch service updated event: %w", err)
	}

	return nil
}

func (m *ReceiveService) ReceiveStatuses(ctx context.Context, payload *payloads.ReceiveStatusWebhookPayloadPayload) (err error) {
	message, err := m.messageRepository.FindOne(ctx, repositories.WithQueryStruct(map[string]interface{}{
		"idFromService": payload.BuiltWebhook.Statuses.Id,
		"serviceId":     payload.ServiceId,
	}))

	// Se for status de reação vai dar erro!
	if err != nil {
		slog.ErrorContext(ctx, "Failed to find message for status update",
			slog.String("messageId", payload.BuiltWebhook.Statuses.Id),
			slog.String("serviceId", payload.ServiceId.String()),
			slog.String("error", err.Error()))
		return fmt.Errorf("failed to find message for status update: %w", err)
	}

	// Usado na gupshup
	if payload.BuiltWebhook.Statuses.WhatsappId != "" && message.IdFromService != payload.BuiltWebhook.Statuses.WhatsappId {
		message.Data.WhatsappMessageId = payload.BuiltWebhook.Statuses.WhatsappId
	}

	message.Data.Ack = payload.BuiltWebhook.Statuses.Ack

	mask := []string{
		"data",
	}

	if payload.BuiltWebhook.Statuses.Error != nil {
		message.Data.Error = &models.MessageError{
			Error:         payload.BuiltWebhook.Statuses.Error.Error,
			Code:          payload.BuiltWebhook.Statuses.Error.Code,
			Message:       payload.BuiltWebhook.Statuses.Error.Message,
			OriginalError: payload.BuiltWebhook.Statuses.Error.OriginalError,
		}
	}

	if message.Timestamp != nil && !message.Timestamp.IsZero() && payload.BuiltWebhook.Statuses.Timestamp != nil && !payload.BuiltWebhook.Statuses.Timestamp.IsZero() {
		message.Timestamp = payload.BuiltWebhook.Statuses.Timestamp
		mask = append(mask, "timestamp")
	}

	err = m.messageRepository.UpdateById(ctx, message.Id, message, repositories.WithQuery(func(d *gorm.DB) *gorm.DB {
		if payload.BuiltWebhook.Statuses.Ack != "error" && payload.BuiltWebhook.Statuses.Ack != "-1" {
			return d.Select(mask).Where("(data->>'ack') in ('0', '1', '2', '3', '4') AND (data->>'ack')::int < ?",
				payload.BuiltWebhook.Statuses.Ack)
		}

		return d.Select(mask)
	}))

	if err != nil {
		slog.ErrorContext(ctx, "Failed to update message status",
			slog.String("messageId", message.Id.String()),
			slog.String("serviceId", payload.ServiceId.String()),
			slog.String("ack", payload.BuiltWebhook.Statuses.Ack),
			slog.Any("mask", mask),
			slog.String("error", err.Error()))
		return fmt.Errorf("failed to update message status: %w", err)
	}

	err = m.eventService.Dispatch(
		ctx,
		event.EventMessageUpdated,
		&event.DispatchPayload{
			Id:        message.Id,
			AccountId: message.AccountId,
		}, &event.EventServiceDispatchOptions{
			DebounceKey:   message.Id.String(),
			DebounceDelay: time.Second * 2,
		})

	if err != nil {
		slog.ErrorContext(ctx, "Failed to dispatch message updated event", slog.String("error", err.Error()), slog.Any("message", message))
		return fmt.Errorf("failed to dispatch message updated event: %w", err)
	}

	return nil
}

func (m *ReceiveService) ReceiveTemplateUpdate(ctx context.Context, payload *payloads.ReceiveTemplateWebhookPayloadPayload) (err error) {
	slog.InfoContext(ctx, "Received template update webhook",
		slog.String("serviceId", payload.ServiceId.String()),
		slog.String("templateId", payload.BuiltWebhook.Template.Id),
		slog.String("type", string(payload.BuiltWebhook.Template.Type)))

	// Buscar o template pelo Id do Gupshup
	template, err := m.templateRepository.FindOne(ctx, repositories.WithQuery(func(db *gorm.DB) *gorm.DB {
		return db.Where("id_gupshup = ?", payload.BuiltWebhook.Template.Id)
	}))

	if err != nil {
		slog.ErrorContext(ctx, "Failed to find template for update",
			slog.String("templateId", payload.BuiltWebhook.Template.Id),
			slog.String("serviceId", payload.ServiceId.String()),
			slog.String("error", err.Error()))
		return fmt.Errorf("failed to find template for update: %w", err)
	}

	// Preparar os dados para atualização
	updateData := &models.WhatsappBusinessTemplate{}
	var oldQuality models.WhatsappBusinessTemplateQualityEnum
	var newQuality models.WhatsappBusinessTemplateQualityEnum

	// Atualizar com base no tipo de webhook
	switch payload.BuiltWebhook.Template.Type {
	case adapter_types.WebhookTemplateTypeQuality:
		oldQuality = template.Quality
		newQuality = models.WhatsappBusinessTemplateQualityEnum(payload.BuiltWebhook.Template.Quality)
		updateData.Quality = newQuality
		slog.InfoContext(ctx, "Updating template quality",
			slog.String("templateId", template.Id.String()),
			slog.String("oldQuality", string(oldQuality)),
			slog.String("newQuality", string(newQuality)))
	case adapter_types.WebhookTemplateTypeStatus:
		// Converter o status para a enumeração
		status := base_waba_adapter.ParseStatus(payload.BuiltWebhook.Template.Status)
		updateData.Status = status
		updateData.RejectedReason = payload.BuiltWebhook.Template.RejectedReason
		slog.InfoContext(ctx, "Updating template status",
			slog.String("templateId", template.Id.String()),
			slog.String("oldStatus", string(template.Status)),
			slog.String("newStatus", string(status)))
	}

	// Atualizar o template
	err = m.templateRepository.UpdateById(ctx, template.Id, updateData, nil)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to update template",
			slog.String("templateId", template.Id.String()),
			slog.String("error", err.Error()))
		return fmt.Errorf("failed to update template: %w", err)
	}

	// Se a qualidade do template diminuiu, pausar campanhas relacionadas
	if payload.BuiltWebhook.Template.Type == adapter_types.WebhookTemplateTypeQuality &&
		(newQuality == models.WhatsappBusinessTemplateQualityLow || newQuality == models.WhatsappBusinessTemplateQualityMedium) &&
		(oldQuality == "" || oldQuality == models.WhatsappBusinessTemplateQualityHigh) {

		// Usar o serviço de campanha para pausar campanhas
		count, err := m.campaignService.PauseCampaignsFromTemplateId(
			ctx,
			template.Id,
			template.AccountId,
			template.ServiceId,
			newQuality,
		)

		if err != nil {
			slog.ErrorContext(ctx, "Failed to pause campaigns for template with decreased quality",
				slog.String("error", err.Error()),
				slog.String("templateId", template.Id.String()),
				slog.String("oldQuality", string(oldQuality)),
				slog.String("newQuality", string(newQuality)))
			// Não retornar erro para não impedir a atualização do template
		} else if count > 0 {
			slog.InfoContext(ctx, "Paused campaigns due to template quality decrease",
				slog.Int("count", count),
				slog.String("templateId", template.Id.String()),
				slog.String("oldQuality", string(oldQuality)),
				slog.String("newQuality", string(newQuality)))
		}
	}

	return nil
}

func (m *ReceiveService) ReceiveWebhook(ctx context.Context, payload *payloads.ReceiveWebhookPayload) (err error) {
	godump.Dump("payload====>", payload)

	adapter, err := m.adapterManager.GetAdapter(ctx, payload.ServiceId)

	if err != nil {
		slog.ErrorContext(ctx, "Failed to get adapter", slog.String("error", err.Error()), slog.String("serviceId", payload.ServiceId.String()))
		return fmt.Errorf("failed to get adapter: %w", err)
	}

	builtWebhooks, err := adapter.BuildWebhook(ctx, payload.ServiceId, payload)

	if err != nil {
		// @TODO: colocar em fila de erro para webhook, sem tentativas pois deu erro no build
		slog.ErrorContext(ctx, "webhook BuildWebhook failed", slog.String("error", err.Error()), slog.Any("payload", payload))
		return fmt.Errorf("webhook BuildWebhook failed: %w", err)
	}

	for _, builtWebhook := range builtWebhooks {
		if builtWebhook.Service != nil {
			err = m.queueDispatcher.Dispatch(ctx, string(queueDispatcher.ReceiveServiceWebhookJobName), &payloads.ReceiveServiceWebhookPayloadPayload{
				ServiceId:    payload.ServiceId,
				AccountId:    payload.AccountId,
				BuiltWebhook: builtWebhook,
			}, &queueDispatcher.QueueJobsDispatcherDispatchOptions{
				HashKey: payload.AccountId.String() + "_" + payload.ServiceId.String(),
			})

			if err != nil {
				slog.ErrorContext(ctx, "Failed on ReceiveService", slog.String("error", err.Error()), slog.Any("payload", payload), slog.Any("builtWebhook.Service", builtWebhook.Service))
			}

			continue
		}

		if builtWebhook.Message != nil {
			err = m.queueDispatcher.Dispatch(ctx, string(queueDispatcher.ReceiveMessageWebhookJobName), &payloads.ReceiveMessagePayload{
				ServiceId:    payload.ServiceId,
				AccountId:    payload.AccountId,
				BuiltWebhook: builtWebhook,
			}, &queueDispatcher.QueueJobsDispatcherDispatchOptions{
				HashKey: payload.AccountId.String() + "_" + payload.ServiceId.String() + "_" + builtWebhook.Contact.Id,
			})

			if err != nil {
				slog.ErrorContext(ctx, "Failed on ReceiveMessage", slog.String("error", err.Error()), slog.Any("payload", payload), slog.Any("builtWebhook.Message", builtWebhook.Message))
			}

			continue
		}

		if builtWebhook.Statuses != nil {
			err = m.queueDispatcher.Dispatch(ctx, string(queueDispatcher.ReceiveStatusWebhookJobName), &payloads.ReceiveStatusWebhookPayloadPayload{
				ServiceId:    payload.ServiceId,
				AccountId:    payload.AccountId,
				BuiltWebhook: builtWebhook,
			}, &queueDispatcher.QueueJobsDispatcherDispatchOptions{
				HashKey: payload.AccountId.String() + "_" + payload.ServiceId.String() + "_" + builtWebhook.Statuses.Id,
			})

			if err != nil {
				slog.ErrorContext(ctx, "Failed on ReceiveStatuses", slog.String("error", err.Error()), slog.Any("payload", payload), slog.Any("builtWebhook.Statuses", builtWebhook.Statuses))
			}

			continue
		}

		if builtWebhook.Template != nil {
			err = m.queueDispatcher.Dispatch(ctx, string(queueDispatcher.ReceiveTemplateWebhookJobName), &payloads.ReceiveTemplateWebhookPayloadPayload{
				ServiceId:    payload.ServiceId,
				AccountId:    payload.AccountId,
				BuiltWebhook: builtWebhook,
			}, &queueDispatcher.QueueJobsDispatcherDispatchOptions{
				HashKey: payload.AccountId.String() + "_" + payload.ServiceId.String() + "_" + builtWebhook.Template.Id,
			})

			if err != nil {
				slog.ErrorContext(ctx, "Failed on ReceiveTemplateUpdate", slog.String("error", err.Error()), slog.Any("payload", payload), slog.Any("builtWebhook.Template", builtWebhook.Template))
			}

			continue
		}
	}

	return nil
}
func (m *ReceiveService) Webhook(ctx context.Context, receiveData *payloads.WebhookPayload) (err error) {
	err = m.ReceiveWebhook(ctx, &payloads.ReceiveWebhookPayload{
		Payload:   receiveData.Payload,
		ServiceId: receiveData.ServiceId,
		AccountId: receiveData.AccountId,
	})

	if err != nil {
		slog.ErrorContext(ctx, "Failed to call ReceiveWebhook", slog.String("error", err.Error()), slog.Any("receiveData", receiveData))
		return fmt.Errorf("failed to call ReceiveWebhook: %w", err)
	}

	return nil
}
func (m *ReceiveService) ReceiveMessage(ctx context.Context, receiveData *payloads.ReceiveMessageWebhookPayloadPayload) (err error) {
	builtWebhook := receiveData.BuiltWebhook

	var contact *models.Contact
	var message *models.Message
	var isMedia bool

	etx := m.eventService.BeginTransaction()

	tx := m.messageRepository.BeginTransaction()

	contact, err = m.contactService.SaveContact(ctx, &contact_service.SaveContactPayload{
		ServiceId:       receiveData.ServiceId,
		AccountId:       receiveData.AccountId,
		Name:            builtWebhook.Contact.Name,
		AlternativeName: builtWebhook.Contact.AlternativeName,
		IdFromService:   builtWebhook.Contact.Id,
		IsGroup:         builtWebhook.Contact.IsGroup,
		IsMe:            builtWebhook.Contact.IsMe,
		Visible:         builtWebhook.Contact.Visible,
		AvatarUrl:       builtWebhook.Contact.AvatarUrl,
	}, tx, etx)

	if err != nil {
		err2 := tx.Rollback()
		if err2 != nil {
			slog.ErrorContext(ctx, "Failed to rollback transaction", slog.String("ServiceId", receiveData.ServiceId.String()), slog.String("AccountId", receiveData.AccountId.String()), slog.String("ContactId", builtWebhook.Contact.Id), slog.String("error", err2.Error()))
		}

		slog.ErrorContext(ctx, "Failed to save contact", slog.String("ServiceId", receiveData.ServiceId.String()), slog.String("AccountId", receiveData.AccountId.String()), slog.String("ContactId", builtWebhook.Contact.Id), slog.String("error", err.Error()))
		return fmt.Errorf("failed to save contact: %w", err)
	}

	err = tx.Commit()
	if err != nil {
		slog.ErrorContext(ctx, "Failed to commit transaction", slog.String("ServiceId", receiveData.ServiceId.String()), slog.String("AccountId", receiveData.AccountId.String()), slog.String("ContactId", builtWebhook.Contact.Id), slog.String("error", err.Error()))
		return fmt.Errorf("failed to commit transaction: %w", err)
	}

	err = etx.Commit()
	if err != nil {
		slog.ErrorContext(ctx, "Failed to commit event transaction", slog.String("ServiceId", receiveData.ServiceId.String()), slog.String("AccountId", receiveData.AccountId.String()), slog.String("ContactId", builtWebhook.Contact.Id), slog.String("error", err.Error()))
		return fmt.Errorf("failed to commit eventtransaction: %w", err)
	}

	queueKey, err := lockutils.GetContactLockKey(receiveData.AccountId, receiveData.ServiceId, contact.Id)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to get contact lock key", slog.String("AccountId", receiveData.AccountId.String()), slog.String("ServiceId", receiveData.ServiceId.String()), slog.String("ContactId", contact.Id.String()), slog.String("error", err.Error()))
		return fmt.Errorf("failed to get contact lock key: %w", err)
	}

	queue := m.distributedLockFactory(queueKey, 2*time.Minute)

	_, err = queue.Run(ctx, func() (interface{}, error) {
		tx := m.messageRepository.BeginTransaction()
		defer tx.Finish(&err)

		transaction := repositories.WithTx(tx)
		var from *models.Contact

		// É um membro do grupo
		if builtWebhook.Contact.Id != builtWebhook.From.Id {
			from, err = m.contactService.SaveContact(ctx, &contact_service.SaveContactPayload{
				ServiceId:       receiveData.ServiceId,
				AccountId:       receiveData.AccountId,
				Name:            builtWebhook.From.Name,
				AlternativeName: builtWebhook.From.AlternativeName,
				IdFromService:   builtWebhook.From.Id,
				IsGroup:         builtWebhook.From.IsGroup,
				IsMe:            builtWebhook.From.IsMe,
				Visible:         builtWebhook.From.Visible,
				AvatarUrl:       builtWebhook.From.AvatarUrl,
			}, tx, etx)
		}

		if err != nil {
			slog.ErrorContext(ctx, "Failed to save contact", slog.String("ServiceId", receiveData.ServiceId.String()), slog.String("AccountId", receiveData.AccountId.String()), slog.String("FromId", builtWebhook.From.Id), slog.String("error", err.Error()))
			return nil, fmt.Errorf("failed to save contact: %w", err)
		}

		fromId := contact.Id

		if from != nil && from.Id != uuid.Nil {
			fromId = from.Id
		}

		isFromMe := builtWebhook.Message.IsFromMe

		if adapter_types.MessageTypeEnum(builtWebhook.Message.Type) == adapter_types.MESSAGE_TYPE_REACTION {
			err = m.receiveReaction(ctx, builtWebhook, contact, isFromMe, fromId, contact.ServiceId, contact.AccountId, transaction, etx)

			if err != nil {
				slog.ErrorContext(ctx, "Failed to receive reaction", slog.String("error", err.Error()), slog.Any("builtWebhook", builtWebhook))
				return nil, err
			}

			err = etx.Commit()
			if err != nil {
				slog.ErrorContext(ctx, "Failed to commit event transaction", slog.String("error", err.Error()))
				return nil, fmt.Errorf("failed to commit event transaction: %w", err)
			}
			return nil, err
		}

		message, err = m.messageRepository.FindOne(ctx, transaction, repositories.WithQueryStruct(map[string]interface{}{
			"idFromService": builtWebhook.Message.Id,
			"serviceId":     receiveData.ServiceId,
			"accountId":     receiveData.AccountId,
			"isFromMe":      isFromMe,
		}))

		if err != nil && err.Error() != "record not found" {
			slog.ErrorContext(ctx, "Failed to find message", slog.String("idFromService", builtWebhook.Message.Id), slog.String("serviceId", receiveData.ServiceId.String()), slog.String("accountId", receiveData.AccountId.String()), slog.Bool("isFromMe", isFromMe), slog.String("error", err.Error()))
			return nil, fmt.Errorf("failed to find message: %w", err)
		}

		// Mensagem já existe.
		if message != nil {
			if builtWebhook.Message.Preview != nil {
				filePreview, err := m.fileService.CreatePreviewFile(ctx, &fileService.CreatePreviewFilePayload{
					AttachedId: message.Id,
					AccountId:  message.AccountId,
					Mimetype:   builtWebhook.Message.Preview.Mimetype,
					Base64:     builtWebhook.Message.Preview.Base64,
				})
				if err != nil {
					slog.ErrorContext(ctx, "Failed to create file preview", slog.String("error", err.Error()), slog.Any("builtWebhook", builtWebhook), slog.Any("filePreview", filePreview))
				}
			}

			// Atualiza texto da mensagem editada
			if builtWebhook.Message.Text != "" && message.Text != "" {
				text, err := m.accountCryptor.DecryptTextForAccount(ctx, contact.Account, message.Text, false)
				if err != nil {
					slog.ErrorContext(ctx, "Failed to decrypt text for account", slog.String("accountId", contact.AccountId.String()), slog.String("text", message.Text), slog.String("error", err.Error()))
					return nil, fmt.Errorf("failed to decrypt text for account: %w", err)
				}

				if text != builtWebhook.Message.Text {
					newText, err := m.accountCryptor.EncryptTextForAccount(ctx, contact.Account, builtWebhook.Message.Text, false)
					if err != nil {
						slog.ErrorContext(ctx, "Failed to encrypt text for account", slog.String("accountId", contact.AccountId.String()), slog.String("text", builtWebhook.Message.Text), slog.String("error", err.Error()))
						return nil, fmt.Errorf("failed to encrypt text for account: %w", err)
					}

					message.Text = newText
					message.Data.Edited = true

					// Não usar transaction aqui para não causar dead lock
					err = m.messageRepository.UpdateById(ctx, message.Id, message)
					if err != nil {
						slog.ErrorContext(ctx, "Failed to update message", slog.String("messageId", message.Id.String()), slog.String("error", err.Error()))
						return nil, fmt.Errorf("failed to update message: %w", err)
					}

					err = m.eventService.Dispatch(
						ctx,
						event.EventMessageUpdated,
						&event.DispatchPayload{
							Id:        message.Id,
							AccountId: message.AccountId,
						}, &event.EventServiceDispatchOptions{
							DebounceKey: message.Id.String(),
							Etx:         etx,
						})

					if err != nil {
						slog.ErrorContext(ctx, "Failed to receive reaction on dispatch message updated event", slog.String("error", err.Error()), slog.Any("message", message))
						return nil, fmt.Errorf("failed to receive reaction on dispatch message updated event: %w", err)
					}
				}
			}

			// Atualiza recebimento de localização em tempo real
			if builtWebhook.Message.Location != nil {
				if message.Data.Location != nil || message.Data.Location.Lat != builtWebhook.Message.Location.Lat || message.Data.Location.Lng != builtWebhook.Message.Location.Lng {
					message.Data.Location.Lat = builtWebhook.Message.Location.Lat
					message.Data.Location.Lng = builtWebhook.Message.Location.Lng

					// Não usar transaction aqui para não causar dead lock
					err = m.messageRepository.UpdateById(ctx, message.Id, message)

					if err != nil {
						slog.ErrorContext(ctx, "Failed to update message location", slog.String("messageId", message.Id.String()), slog.String("error", err.Error()))
						return nil, fmt.Errorf("failed to update message location: %w", err)
					}

					err = m.eventService.Dispatch(
						ctx,
						event.EventMessageUpdated,
						&event.DispatchPayload{
							Id:        message.Id,
							AccountId: message.AccountId,
						}, &event.EventServiceDispatchOptions{
							DebounceKey: message.Id.String(),
							Etx:         etx,
						})

					if err != nil {
						slog.ErrorContext(ctx, "Failed to receive reaction on dispatch message updated event", slog.String("error", err.Error()), slog.Any("message", message))
						return nil, fmt.Errorf("failed to receive reaction on dispatch message updated event: %w", err)
					}
				}
			}

			if builtWebhook.Statuses != nil {
				err = m.queueDispatcher.Dispatch(ctx, string(queueDispatcher.ReceiveStatusWebhookJobName), &payloads.ReceiveStatusWebhookPayloadPayload{
					ServiceId:    receiveData.ServiceId,
					AccountId:    receiveData.AccountId,
					BuiltWebhook: builtWebhook,
				}, &queueDispatcher.QueueJobsDispatcherDispatchOptions{
					HashKey: receiveData.AccountId.String() + "_" + receiveData.ServiceId.String() + "_" + builtWebhook.Statuses.Id,
				})

				if err != nil {
					slog.ErrorContext(ctx, "Failed to dispatch receive-status-webhook", slog.String("error", err.Error()), slog.Any("BuiltWebhook", builtWebhook))
					return nil, fmt.Errorf("failed to dispatch receive-status-webhook: %w", err)
				}

				return nil, nil
			}

			return nil, errors.New("message already exists")
		}

		var isFirst bool

		if contact.LastMessageId != uuid.Nil {
			isFirst = true
		}

		messageType := builtWebhook.Message.Type

		var text string

		if builtWebhook.Message.Text != "" {
			text, err = m.accountCryptor.EncryptTextForAccount(ctx, contact.Account, builtWebhook.Message.Text, false)
			if err != nil {
				slog.ErrorContext(ctx, "Failed to encrypt text for account", slog.String("accountId", contact.AccountId.String()), slog.String("text", builtWebhook.Message.Text), slog.String("error", err.Error()))
				return nil, fmt.Errorf("failed to encrypt text for account: %w", err)
			}
		}

		var quotedMessageId uuid.UUID

		if builtWebhook.Message.ReplyMessageId != "" {
			quotedMessage, err := m.messageRepository.FindOne(ctx, transaction, repositories.WithQueryStruct(map[string]interface{}{
				"idFromService": builtWebhook.Message.ReplyMessageId,
				"serviceId":     receiveData.ServiceId,
				"accountId":     receiveData.AccountId,
			}))

			if err != nil && err.Error() != "record not found" {
				slog.ErrorContext(ctx, "Failed to find quoted message", slog.String("idFromService", builtWebhook.Message.ReplyMessageId), slog.String("serviceId", receiveData.ServiceId.String()), slog.String("accountId", receiveData.AccountId.String()), slog.String("error", err.Error()))
				return nil, fmt.Errorf("failed to find quoted message: %w", err)
			}

			if quotedMessage != nil {
				quotedMessageId = quotedMessage.Id
			} else {
				err = m.ReceiveMessage(ctx, &payloads.ReceiveMessageWebhookPayloadPayload{
					ServiceId: contact.ServiceId,
					AccountId: contact.AccountId,
					BuiltWebhook: &adapter_types.BuiltWebhook{
						Message: builtWebhook.Message.ReplyMessageWebhook.Message,
						Contact: builtWebhook.Message.ReplyMessageWebhook.Contact,
						From:    builtWebhook.Message.ReplyMessageWebhook.From,
					},
				})

				if err != nil {
					slog.ErrorContext(ctx, "Failed to receive quoted message", slog.String("ServiceId", contact.ServiceId.String()), slog.String("AccountId", contact.AccountId.String()), slog.Any("BuiltWebhook", builtWebhook.Message.ReplyMessageWebhook), slog.String("error", err.Error()))
					return nil, fmt.Errorf("failed to receive quoted message: %w", err)
				}

				quotedMessage, err = m.messageRepository.FindOne(ctx, transaction, repositories.WithQueryStruct(map[string]interface{}{
					"idFromService": builtWebhook.Message.ReplyMessageWebhook.Message.Id,
					"serviceId":     receiveData.ServiceId,
					"accountId":     receiveData.AccountId,
				}))

				if err != nil && err.Error() != "record not found" {
					slog.ErrorContext(ctx, "Failed to find quoted message after receiving it", slog.String("idFromService", builtWebhook.Message.ReplyMessageWebhook.Message.Id), slog.String("serviceId", receiveData.ServiceId.String()), slog.String("accountId", receiveData.AccountId.String()), slog.String("error", err.Error()))
					return nil, fmt.Errorf("failed to find quoted message after receiving it: %w", err)
				}

				if quotedMessage != nil {
					quotedMessageId = quotedMessage.Id
				}
			}
		}

		mediaTypes := []string{"audio", "image", "video", "document", "sticker"}
		isMedia = slices.Contains(mediaTypes, messageType)
		var mediaId string
		var mediaUrl string

		if isMedia {
			mediaId = builtWebhook.Message.File.Id
			mediaUrl = builtWebhook.Message.File.Url
		}

		now := time.Now()

		var location *models.Location

		if messageType == "location" {
			location = &models.Location{
				Lat:        builtWebhook.Message.Location.Lat,
				Lng:        builtWebhook.Message.Location.Lng,
				PreviewUrl: builtWebhook.Message.Location.PreviewUrl,
			}
		}

		var ctwaContex *models.CtwaContext

		if builtWebhook.Message.CtwaContext != nil {
			ctwaContex = &models.CtwaContext{
				ConversionSource: builtWebhook.Message.CtwaContext.ConversionSource,
				Description:      builtWebhook.Message.CtwaContext.Description,
				IsSuspiciousLink: builtWebhook.Message.CtwaContext.IsSuspiciousLink,
				MediaUrl:         builtWebhook.Message.CtwaContext.MediaUrl,
				SourceUrl:        builtWebhook.Message.CtwaContext.SourceUrl,
				ThumbnailUrl:     builtWebhook.Message.CtwaContext.ThumbnailUrl,
				Title:            builtWebhook.Message.CtwaContext.Title,
				MediaType:        builtWebhook.Message.CtwaContext.MediaType,
			}
		}

		message = &models.Message{
			Text:          text,
			IdFromService: builtWebhook.Message.Id,
			Timestamp:     builtWebhook.Message.Timestamp,
			Type:          messageType,
			IsFromMe:      isFromMe,
			ContactId:     contact.Id,
			AccountId:     contact.AccountId,
			ServiceId:     contact.ServiceId,
			Data: &models.MessageData{
				MediaId:  mediaId,
				MediaUrl: mediaUrl,
				IsNew:    true,
				IsFirst:  isFirst,
				Ack:      -1,
				FileDownload: &models.FileDownload{
					StartedAt:     &now,
					IsDownloading: isMedia,
				},
				VCard:       builtWebhook.Message.VCard,
				Location:    location,
				CtwaContext: ctwaContex,
			},
			FromId:          fromId,
			QuotedMessageId: quotedMessageId,
		}

		err = m.messageRepository.Create(ctx, message, transaction)

		if err != nil {
			slog.ErrorContext(ctx, "Failed to create message", slog.String("error", err.Error()), slog.Any("message", message))
			return nil, fmt.Errorf("failed to create message: %w", err)
		}

		err := m.eventService.Dispatch(
			ctx,
			event.EventMessageCreated,
			&event.DispatchPayload{
				Id:        message.Id,
				AccountId: message.AccountId,
			}, &event.EventServiceDispatchOptions{
				Etx: etx,
			})

		if err != nil {
			slog.ErrorContext(ctx, "Failed to dispatch message created event", slog.String("error", err.Error()), slog.Any("message", message))
			return nil, fmt.Errorf("failed to dispatch message created event: %w", err)
		}

		contact.LastMessageAt = message.Timestamp
		contact.LastMessageId = message.Id
		contact.LastContactMessageAt = message.Timestamp
		contact.Visible = true
		contact.HadChat = true

		if contact.Data.Survey == nil {
			contact.Unread = contact.Unread + 1
		}

		err = m.contactRepository.UpdateById(ctx, contact.Id, contact, transaction)

		if err != nil {
			slog.ErrorContext(ctx, "Failed to update contact", slog.String("error", err.Error()), slog.Any("contact", contact))
			return nil, fmt.Errorf("failed to update contact: %w", err)
		}

		err = m.eventService.Dispatch(
			ctx,
			event.EventContactUpdated,
			&event.DispatchPayload{
				Id:        contact.Id,
				AccountId: contact.AccountId,
			},
			&event.EventServiceDispatchOptions{
				DebounceKey: contact.Id.String(),
				Etx:         etx,
			})

		if err != nil {
			slog.ErrorContext(ctx, "Failed to dispatch contact updated event", slog.String("error", err.Error()), slog.Any("message", message))
			return nil, fmt.Errorf("failed to dispatch contact updated event: %w", err)
		}

		if contact.Data.Survey != nil {
			shouldOpenTicket, err := m.evaluationService.HandleAnswer(ctx, contact, builtWebhook.Message.Text, tx, etx)

			if err != nil {
				slog.ErrorContext(ctx, "Failed to handle survey answer", slog.String("error", err.Error()), slog.Any("contact", contact), slog.String("builtWebhook.Message.Text", builtWebhook.Message.Text))
				return nil, fmt.Errorf("failed to handle survey answer: %w", err)
			}

			if !shouldOpenTicket {
				return nil, nil
			}
		}

		err = m.ticketService.HandleMessageCreated(ctx, message, nil, tx, etx)

		if err != nil {
			slog.ErrorContext(ctx, "Failed to handle message created for ticket", slog.String("error", err.Error()), slog.Any("message", message))
			return nil, fmt.Errorf("failed to handle message created for ticket: %w", err)
		}

		err = etx.Commit()

		if err != nil {
			slog.ErrorContext(ctx, "Failed to commit event transaction", slog.String("error", err.Error()), slog.Any("message", message))
			return nil, fmt.Errorf("failed to commit event transaction: %w", err)
		}

		return nil, nil
	})

	if err != nil {
		slog.ErrorContext(ctx, "ReceiveMessage failed", slog.String("err", err.Error()), slog.Any("receiveData", receiveData))

		if err.Error() == "message already exists" {
			return nil
		}

		return err
	}

	if isMedia {
		err = m.queueDispatcher.Dispatch(ctx, string(queueDispatcher.ReceiveMediaJobName), &payloads.ReceiveMediaPayloadPayload{
			MessageId:    message.Id,
			ServiceId:    message.ServiceId,
			AccountId:    message.AccountId,
			BuiltWebhook: builtWebhook,
		}, &queueDispatcher.QueueJobsDispatcherDispatchOptions{
			HashKey: message.AccountId.String() + "_" + message.ServiceId.String() + "_" + contact.Id.String(),
		})

		if err != nil {
			slog.ErrorContext(ctx, "Failed to dispatch ReceiveMedia", slog.String("error", err.Error()), slog.Any("receiveData", receiveData))
			return fmt.Errorf("failed to dispatch ReceiveMedia: %w", err)
		}
	}

	return nil
}

func (m *ReceiveService) ReceiveMedia(ctx context.Context, payload *payloads.ReceiveMediaPayloadPayload) error {
	message, err := m.messageRepository.FindById(ctx, payload.MessageId)
	if err != nil {
		slog.ErrorContext(ctx, "ReceiveMedia find message failed", slog.String("error", err.Error()), slog.Any("payload", payload))
		return fmt.Errorf("receiveMedia find message failed: %w", err)
	}

	adapter, err := m.adapterManager.GetAdapter(ctx, payload.ServiceId)
	if err != nil {
		slog.ErrorContext(ctx, "ReceiveMedia get adapter failed", slog.String("error", err.Error()), slog.Any("payload", payload))
		return fmt.Errorf("receiveMedia get adapter failed: %w", err)
	}

	media, err := adapter.DownloadMedia(ctx, payload.ServiceId, message.Data.MediaId, message.Data.MediaUrl)

	if err != nil {
		slog.ErrorContext(ctx, "ReceiveMedia download failed ", slog.String("error", err.Error()), slog.Any("payload", payload))
		return fmt.Errorf("receiveMedia download failed: %w", err)
	}

	if media.Type == "stream" {
		file, err := m.fileService.CreateFile(ctx, &fileService.CreateFilePayload{
			Name:         payload.BuiltWebhook.Message.File.Name,
			AttachedId:   message.Id,
			AttachedType: "message.file",
			Mimetype:     payload.BuiltWebhook.Message.File.Mimetype,
			AccountId:    message.AccountId,
			Stream:       media.Stream,
		}, nil)
		if err != nil {
			slog.ErrorContext(ctx, "ReceiveMedia create file failed", slog.String("error", err.Error()), slog.Any("payload", payload), slog.Any("file", file))
			return fmt.Errorf("receiveMedia create file failed: %w", err)
		}
	}

	now := time.Now()

	message.Data.FileDownload = &models.FileDownload{
		EndedAt:       &now,
		IsDownloading: false,
	}

	err = m.messageRepository.UpdateById(ctx, message.Id, message)

	if err != nil {
		slog.ErrorContext(ctx, "ReceiveMedia update message failed", slog.String("error", err.Error()), slog.Any("payload", payload), slog.Any("message", message))
		return fmt.Errorf("receiveMedia update message failed: %w", err)
	}

	if payload.BuiltWebhook.Message.Preview != nil {
		filePreview, err := m.fileService.CreatePreviewFile(ctx, &fileService.CreatePreviewFilePayload{
			AttachedId: message.Id,
			AccountId:  message.AccountId,
			Mimetype:   payload.BuiltWebhook.Message.Preview.Mimetype,
			Base64:     payload.BuiltWebhook.Message.Preview.Base64,
		})
		if err != nil {
			slog.ErrorContext(ctx, "Failed to create file preview", slog.String("error", err.Error()), slog.Any("payload", payload), slog.Any("filePreview", filePreview))
		}
	}

	err = m.eventService.Dispatch(
		ctx,
		event.EventMessageUpdated,
		&event.DispatchPayload{
			Id:        message.Id,
			AccountId: message.AccountId,
		},
		&event.EventServiceDispatchOptions{
			DebounceKey: message.Id.String(),
		})

	if err != nil {
		slog.ErrorContext(ctx, "ReceiveMedia failed on dispatch message updated event", slog.String("error", err.Error()), slog.Any("message", message))
		return fmt.Errorf("receiveMedia failed on dispatch message updated event: %w", err)
	}

	return nil
}
