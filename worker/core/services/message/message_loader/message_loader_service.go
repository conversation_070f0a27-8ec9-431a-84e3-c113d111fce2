package message_loader

import (
	"context"
	"digisac-go/worker/core/adapters/adapter_types"
	"digisac-go/worker/core/http/payloads"
	"digisac-go/worker/core/managers/service_manager"
	"digisac-go/worker/core/repositories"
	"digisac-go/worker/core/services/contact/contact_service"
	"digisac-go/worker/core/services/crypto"
	"digisac-go/worker/core/services/message/message_receiver"
	"fmt"
	"log/slog"
)

type LoadService struct {
	adapterManager    *service_manager.ServiceManager
	contactRepository repositories.ContactRepository
	messageRepository repositories.MessageRepository
	contactservice    *contact_service.ContactService
	accountCryptor    *crypto.AccountCryptor
	messageReceiver   *message_receiver.ReceiveService
}

func NewLoadService(
	adapterManager *service_manager.ServiceManager,
	contactRepository repositories.ContactRepository,
	messageRepository repositories.MessageRepository,
	contactservice *contact_service.ContactService,
	accountCryptor *crypto.AccountCryptor,
	messageReceiver *message_receiver.ReceiveService,

) *LoadService {
	return &LoadService{
		adapterManager:    adapterManager,
		contactRepository: contactRepository,
		messageRepository: messageRepository,
		contactservice:    contactservice,
		accountCryptor:    accountCryptor,
		messageReceiver:   messageReceiver,
	}
}

func (l *LoadService) LoadEarlierMessages(ctx context.Context, loadData *payloads.LoadEarlierMessagesPayload) (interface{}, error) {
	adapter, err := l.adapterManager.GetAdapter(ctx, loadData.ServiceId)

	if err != nil {
		slog.ErrorContext(ctx, "Error getting adapter",
			slog.String("error", err.Error()),
			slog.String("serviceId", loadData.ServiceId.String()))
		return nil, fmt.Errorf("error getting adapter: %w", err)
	}

	contact, err := l.contactRepository.FindById(ctx, loadData.ContactId)

	if err != nil {
		slog.ErrorContext(ctx, "Error finding contact",
			slog.String("error", err.Error()),
			slog.String("contactId", loadData.ContactId.String()))
		return nil, fmt.Errorf("error finding contact: %w", err)
	}

	builtWebhooks, err := adapter.LoadEarlierMessages(ctx, loadData.ServiceId, &adapter_types.LoadEarlierMessagesPayload{
		IdFromService: contact.IdFromService,
		Timestamp:     loadData.Timestamp,
	})

	if err != nil {
		slog.ErrorContext(ctx, "Error loading earlier messages from adapter",
			slog.String("error", err.Error()),
			slog.String("serviceId", loadData.ServiceId.String()),
			slog.String("contactIdFromService", contact.IdFromService),
			slog.String("timestamp", loadData.Timestamp.String()))

		return nil, fmt.Errorf("error loading earlier messages from adapter: %w", err)
	}

	for _, builtWebhook := range builtWebhooks {
		err = l.messageReceiver.ReceiveMessage(ctx, &payloads.ReceiveMessageWebhookPayloadPayload{
			ServiceId:    loadData.ServiceId,
			AccountId:    loadData.AccountId,
			BuiltWebhook: builtWebhook,
		})

		if err != nil {
			slog.ErrorContext(ctx, "Error receiving message",
				slog.String("error", err.Error()),
				slog.String("serviceId", loadData.ServiceId.String()),
				slog.String("accountId", loadData.AccountId.String()),
				slog.Any("builtWebhook", builtWebhook))

			return nil, fmt.Errorf("error receiving message: %w", err)
		}
	}

	return true, nil
}
