package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type QuestionTypeEnum string

const (
	QuestionTypeNPS    QuestionTypeEnum = "nps"
	QuestionTypeCSAT   QuestionTypeEnum = "csat"
	QuestionTypeCustom QuestionTypeEnum = "custom"
)

type Question struct {
	Id              uuid.UUID        `gorm:"type:uuid;primary_key" json:"id,omitempty"`
	Name            string           `gorm:"type:text;not null" json:"name,omitempty"`
	Type            QuestionTypeEnum `gorm:"type:enum_questions_type;not null;" json:"type,omitempty"`
	QuestionMessage string           `gorm:"type:text;not null" json:"questionMessage,omitempty"`
	Duration        int              `gorm:"type:int;not null" json:"duration,omitempty"`
	Tries           int              `gorm:"type:int;not null" json:"tries,omitempty"`
	SuccessMessage  string           `gorm:"type:text;not null" json:"successMessage,omitempty"`
	InvalidMessage  string           `gorm:"type:text;not null" json:"invalidMessage,omitempty"`
	ReasonMessage   string           `gorm:"type:text" json:"reasonMessage,omitempty"`
	AccountId       uuid.UUID        `gorm:"type:uuid;default:null" json:"accountId,omitempty"`
	Account         *Account         `gorm:"foreignKey:AccountId" json:"account,omitempty"`
	QuestionId      uuid.UUID        `gorm:"type:uuid;default:null" json:"questionId,omitempty"`
	Answer          *Answer          `gorm:"foreignKey:QuestionId" json:"answer,omitempty"`
	CreatedAt       *time.Time       `json:"createdAt,omitempty"`
	UpdatedAt       *time.Time       `json:"updatedAt,omitempty"`
	DeletedAt       *gorm.DeletedAt  `gorm:"index" json:"deletedAt,omitempty"`
}

func (Question) CreateEnums(db *gorm.DB) error {
	return CreateEnumIfNotExists(db, "enum_questions_type", []string{"nps", "csat", "custom"})
}

func (Question) TableName() string {
	return "questions"
}

func (q *Question) BeforeCreate(tx *gorm.DB) (err error) {
	q.Id = uuid.New()
	return
}
