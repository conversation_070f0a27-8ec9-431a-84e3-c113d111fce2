package models

import (
	"time"

	"github.com/google/uuid"

	"gorm.io/gorm"
)

type ContactOrigin string

const (
	ContactOriginApi      ContactOrigin = "api"
	ContactOriginApp      ContactOrigin = "app"
	ContactOriginCampaign ContactOrigin = "campaign"
	ContactOriginDriver   ContactOrigin = "driver"
	ContactOriginImport   ContactOrigin = "import"
	ContactOriginWeb      ContactOrigin = "web"
)

// @jsonb
type ContactDataSurvey struct {
	ExpiresAt  *time.Time `json:"expiresAt,omitempty"`
	TicketId   uuid.UUID  `json:"ticketId,omitempty"`
	QuestionId uuid.UUID  `json:"questionId,omitempty"`
	Tries      int        `json:"tries,omitempty"`
}

// @jsonb
type ContactDataBlock struct {
	Level       int       `json:"level"` // 0,1 = desbloqueado/bloqueado via lista. 2,3 = desbloqueado/bloqueado manual
	ByUserId    uuid.UUID `json:"byUserId"`
	Date        time.Time `json:"date"`
	Description string    `json:"description"`
}

// @jsonb
type ContactData struct {
	BotFinishedAt *time.Time         `json:"botFinishedAt,omitempty"`
	BotIsRunning  bool               `json:"botIsRunning,omitempty"`
	Number        string             `json:"number,omitempty"`
	Survey        *ContactDataSurvey `json:"survey,omitempty"`
}

type Contact struct {
	Id                        uuid.UUID         `gorm:"type:uuid;primary_key" json:"id,omitempty"`
	IdFromService             string            `gorm:"type:string" json:"idFromService,omitempty"`
	Name                      string            `gorm:"type:string" json:"name,omitempty"`
	InternalName              string            `gorm:"type:string" json:"internalName,omitempty"`
	AlternativeName           string            `gorm:"type:string" json:"alternativeName,omitempty"`
	IsGroup                   bool              `gorm:"default:false" json:"isGroup,omitempty"`
	IsBroadcast               bool              `gorm:"default:false" json:"isBroadcast,omitempty"`
	IsMe                      bool              `gorm:"default:false" json:"isMe,omitempty"`
	IsMyContact               bool              `gorm:"default:false" json:"isMyContact,omitempty"`
	HadChat                   bool              `gorm:"default:false" json:"hadChat,omitempty"`
	Visible                   bool              `gorm:"default:false" json:"visible,omitempty"`
	IsSilenced                bool              `gorm:"default:false" json:"isSilenced,omitempty"`
	Data                      *ContactData      `gorm:"type:jsonb;serializer:json;not null;default:'{}'" json:"data,omitempty"`
	Note                      string            `gorm:"type:text" json:"note,omitempty"`
	Unread                    int               `gorm:"default:0" json:"unread,omitempty"`
	LastMessageAt             *time.Time        `gorm:"type:timestamptz" json:"lastMessageAt,omitempty"`
	Unsubscribed              bool              `gorm:"default:false" json:"unsubscribed,omitempty"`
	LastContactMessageAt      *time.Time        `gorm:"type:timestamptz" json:"lastContactMessageAt,omitempty"`
	AcceptedTermAt            *time.Time        `gorm:"type:timestamptz" json:"acceptedTermAt,omitempty"`
	HsmExpirationTime         *time.Time        `gorm:"type:timestamptz" json:"hsmExpirationTime,omitempty"`
	Block                     bool              `gorm:"default:false" json:"block,omitempty"`
	DataBlock                 *ContactDataBlock `gorm:"column:dataBlock;type:jsonb;serializer:json;" json:"dataBlock,omitempty"`
	Origin                    ContactOrigin     `gorm:"type:enum_contacts_origin;" json:"origin,omitempty"`
	Status                    string            `gorm:"type:string;default:'offline'" json:"status,omitempty"`
	AccountId                 uuid.UUID         `gorm:"type:uuid;default:null" json:"accountId,omitempty"`
	ServiceId                 uuid.UUID         `gorm:"type:uuid;default:null" json:"serviceId,omitempty"`
	DefaultDepartmentId       uuid.UUID         `gorm:"type:uuid;default:null" json:"defaultDepartmentId,omitempty"`
	DefaultDepartment         *Department       `gorm:"foreignKey:DefaultDepartmentId" json:"defaultDepartment,omitempty"`
	DefaultUserId             uuid.UUID         `gorm:"type:uuid;default:null" json:"defaultUserId,omitempty"`
	DefaultUser               *User             `gorm:"foreignKey:DefaultUserId" json:"defaultUser,omitempty"`
	CurrentTicketId           uuid.UUID         `gorm:"type:uuid;default:null" json:"currentTicketId,omitempty"`
	LastMessageId             uuid.UUID         `gorm:"type:uuid;default:null" json:"lastMessageId,omitempty"`
	PersonId                  uuid.UUID         `gorm:"type:uuid;default:null" json:"personId,omitempty"`
	ContactBlockListControlId uuid.UUID         `gorm:"type:uuid;default:null" json:"contactBlockListControlId,omitempty"`
	Account                   *Account          `gorm:"foreignKey:AccountId" json:"account,omitempty"`
	Service                   *Service          `gorm:"foreignKey:ServiceId" json:"service,omitempty"`
	LastMessage               *Message          `gorm:"foreignKey:LastMessageId;constraint:false" json:"lastMessage,omitempty"`
	Participants              []*Contact        `gorm:"many2many:group_participants;foreignKey:Id;joinForeignKey:GroupId;References:Id;joinReferences:ContactId" json:"participants,omitempty"`
	Groups                    []*Contact        `gorm:"many2many:group_participants;foreignKey:Id;joinForeignKey:ContactId;References:Id;joinReferences:GroupId" json:"groups,omitempty"`
	Tags                      []Tag             `gorm:"many2many:contact_tags;foreignKey:Id;joinForeignKey:ContactId;References:Id;joinReferences:TagId" json:"tags,omitempty"`
	Avatar                    *File             `gorm:"foreignKey:AttachedId;constraint:false" json:"avatar,omitempty"`
	ThumbAvatar               *File             `gorm:"foreignKey:AttachedId;constraint:false" json:"thumbAvatar,omitempty"`
	CurrentTicket             *Ticket           `gorm:"foreignKey:CurrentTicketId;constraint:false" json:"currentTicket,omitempty"`
	Tickets                   []*Ticket         `gorm:"many2many:contact_tickets;foreignKey:Id;joinForeignKey:ContactId;References:Id;joinReferences:TicketId" json:"tickets,omitempty"`
	User                      []*User           `gorm:"many2many:user_contacts;foreignKey:Id;joinForeignKey:ContactId;References:Id;joinReferences:UserId" json:"user,omitempty"`
	CreatedAt                 *time.Time        `json:"createdAt,omitempty"`
	UpdatedAt                 *time.Time        `json:"updatedAt,omitempty"`
	DeletedAt                 *gorm.DeletedAt   `gorm:"index" json:"deletedAt,omitempty"`
	ArchivedAt                *time.Time        `gorm:"default:null" json:"archivedAt,omitempty"`
	Person                    *Person           `gorm:"foreignKey:PersonId" json:"person,omitempty"`
	//CustomFieldValues         []CustomFieldValue    `gorm:"foreignKey:RelatedId;constraint:false"`
	//Schedules                 []Schedule            `gorm:"foreignKey:ContactId"`
	//AcceptanceTerm            AcceptanceTerm        `gorm:"foreignKey:AcceptanceTermId"`
	//ServicesWebhookFails      []ServicesWebhookFail `gorm:"foreignKey:ContactId"`

}

func (Contact) CreateEnums(db *gorm.DB) error {
	return CreateEnumIfNotExists(db, "enum_contacts_origin", []string{"api", "app", "campaign", "driver", "import", "web"})
}

func (Contact) TableName() string {
	return "contacts"
}

func (c *Contact) BeforeCreate(tx *gorm.DB) (err error) {
	c.Id = uuid.New()
	return
}
