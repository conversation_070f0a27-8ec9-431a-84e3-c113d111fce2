package payloads

import (
	"digisac-go/worker/core/adapters/adapter_types"
	"digisac-go/worker/core/models"

	"github.com/google/uuid"
)

type File struct {
	IsPtt    *bool   `json:"isPtt,omitempty"`
	Base64   *string `json:"base64,omitempty"`
	Mimetype string  `json:"mimetype"`
	Name     string  `json:"name"`
}

type Attachment struct {
	Base64   string `json:"base64"`
	Mimetype string `json:"mimetype"`
	Name     string `json:"name"`
}

type FileTemplate struct {
	URL *string `json:"url,omitempty"`
}

type VCard struct {
	VCard string `json:"vcard"`
}

type SendMessagePayload struct {
	CampaignMessageProgressId uuid.UUID                        `json:"campaignMessageProgressId,omitempty"`
	CampaignId                uuid.UUID                        `json:"campaignId,omitempty"`
	ContactId                 uuid.UUID                        `json:"contactId,omitempty" validate:"required_without=Number"`
	Number                    string                           `json:"number,omitempty" validate:"required_without=ContactId"`
	IsFromMe                  bool                             `json:"isFromMe,omitempty"`
	ItemIndex                 int                              `json:"itemIndex,omitempty"`
	Type                      string                           `json:"type,omitempty"`
	IsComment                 bool                             `json:"isComment,omitempty"`
	Name                      string                           `json:"name,omitempty"`
	IsPtt                     bool                             `json:"isPtt,omitempty"`
	FileId                    uuid.UUID                        `json:"fileId,omitempty"`
	FilesIds                  []uuid.UUID                      `json:"filesIds,omitempty"`
	File                      *File                            `json:"file,omitempty"`
	UserId                    uuid.UUID                        `json:"userId,omitempty"`
	DepartmentId              uuid.UUID                        `json:"departmentId,omitempty"`
	Text                      string                           `json:"text,omitempty" validate:"required_without_all=FileId File StickerId HsmId"`
	StickerId                 uuid.UUID                        `json:"stickerId,omitempty"`
	QuotedMessageId           uuid.UUID                        `json:"quotedMessageId,omitempty"`
	DontOpenTicket            bool                             `json:"dontOpenTicket,omitempty"`
	DontSendUserName          bool                             `json:"dontSendUserName,omitempty"`
	Origin                    string                           `json:"origin,omitempty" validate:"oneof=user bot schedule campaign"`
	BotId                     uuid.UUID                        `json:"botId,omitempty"`
	AccountId                 uuid.UUID                        `json:"accountId,omitempty" validate:"required"`
	ServiceId                 uuid.UUID                        `json:"serviceId,omitempty" validate:"required_with=number"`
	TagId                     []uuid.UUID                      `json:"tagId,omitempty"`
	IsFromSurvey              bool                             `json:"isFromSurvey,omitempty"`
	Attachments               []*Attachment                    `json:"attachments,omitempty"`
	Subject                   string                           `json:"subject,omitempty"`
	Ccs                       string                           `json:"ccs,omitempty"`
	Hsm                       *models.WhatsappBusinessTemplate `json:"hsm,omitempty"` // retrocompatibilidade
	HsmId                     uuid.UUID                        `json:"hsmId,omitempty"`
	HsmFileId                 uuid.UUID                        `json:"hsmFileId,omitempty"`
	Parameters                []*models.HsmParameters          `json:"parameters,omitempty"`
	ExtraOptions              interface{}                      `json:"extraOptions,omitempty"`
	Actions                   interface{}                      `json:"actions,omitempty"`
	Mask                      string                           `json:"mask,omitempty"`
	FileTemplate              *FileTemplate                    `json:"fileTemplate,omitempty"`
	VCard                     []*VCard                         `json:"vcard,omitempty"`
	InteractiveMessage        *struct {
		File        *models.File               `json:"file,omitempty"` // retrocompatibilidade
		Interactive *models.InteractiveMessage `json:"interactive,omitempty"`
	} `json:"interactiveMessage,omitempty"`
	OutsideRequest bool `json:"outsideRequest,omitempty"`
}

type SendMessageRequest struct {
	Payload  *SendMessagePayload `json:"payload,omitempty" validate:"required"`
	Metadata *Metadata           `json:"metadata"`
}

type SendVCardsPayload struct {
	ServiceId   uuid.UUID    `json:"serviceId,omitempty" validate:"required"`
	ContactId   uuid.UUID    `json:"contactId,omitempty" validate:"required"`
	UserId      uuid.UUID    `json:"userId,omitempty" validate:"required"`
	ContactsIds []*uuid.UUID `json:"contactsIds,omitempty" validate:"required"`
}

type SendVCardsRequest struct {
	Payload  *SendVCardsPayload `json:"payload,omitempty" validate:"required"`
	Metadata *Metadata          `json:"metadata"`
}

type SendReactionPayload struct {
	MessageId             uuid.UUID `json:"messageId,omitempty" validate:"required"`
	ReactionEmojiRendered string    `json:"reactionEmojiRendered,omitempty" validate:"required"`
}

type RevokeReactionPayload struct {
	MessageId uuid.UUID `json:"messageId,omitempty" validate:"required"`
}

type RevokeReactionRequest struct {
	Payload  *RevokeReactionPayload `json:"payload,omitempty" validate:"required"`
	Metadata *Metadata              `json:"metadata"`
}

type SendReactionRequest struct {
	Payload  *SendReactionPayload `json:"payload,omitempty" validate:"required"`
	Metadata *Metadata            `json:"metadata"`
}

type WebhookPayload struct {
	ServiceId uuid.UUID              `json:"serviceId" validate:"required"`
	AccountId uuid.UUID              `json:"accountId" validate:"required"`
	Payload   map[string]interface{} `json:"payload" validate:"required"`
}

type ReceiveMessagePayload struct {
	ServiceId    uuid.UUID                   `json:"serviceId" validate:"required"`
	AccountId    uuid.UUID                   `json:"accountId" validate:"required"`
	BuiltWebhook *adapter_types.BuiltWebhook `json:"builtWebhook" validate:"required"`
}

type WebhookRequest struct {
	Payload  *WebhookPayload `json:"payload"`
	Metadata *Metadata       `json:"metadata"`
}

type SendMessageToBrokerPayloadPayload struct {
	MessageId uuid.UUID `json:"messageId" validate:"required"`
	ServiceId uuid.UUID `json:"serviceId" validate:"required"`
	AccountId uuid.UUID `json:"accountId" validate:"required"`
}
type SendMessageToBrokerPayload struct {
	Payload  *SendMessageToBrokerPayloadPayload `json:"payload" validate:"required"`
	Metadata *Metadata                          `json:"metadata"`
}

type SendMessageToBrokerRequest struct {
	Payload *SendMessageToBrokerPayload `json:"payload" validate:"required"`
}

type ReceiveMessageWebhookPayloadPayload struct {
	ServiceId    uuid.UUID                   `json:"serviceId" validate:"required"`
	AccountId    uuid.UUID                   `json:"accountId" validate:"required"`
	BuiltWebhook *adapter_types.BuiltWebhook `json:"builtWebhook" validate:"required"`
}

type ReceiveMessageWebhookPayload struct {
	Payload  *ReceiveMessageWebhookPayloadPayload `json:"payload" validate:"required"`
	Metadata *Metadata                            `json:"metadata"`
}

type ReceiveMessageWebhookRequest struct {
	Payload *ReceiveMessageWebhookPayload `json:"payload" validate:"required"`
}

type ReceiveStatusWebhookPayloadPayload struct {
	ServiceId    uuid.UUID                   `json:"serviceId" validate:"required"`
	AccountId    uuid.UUID                   `json:"accountId" validate:"required"`
	BuiltWebhook *adapter_types.BuiltWebhook `json:"builtWebhook" validate:"required"`
}

type ReceiveStatusWebhookPayload struct {
	Payload  *ReceiveStatusWebhookPayloadPayload
	Metadata *Metadata `json:"metadata"`
}

type ReceiveStatusWebhookRequest struct {
	Payload *ReceiveStatusWebhookPayload `json:"payload" validate:"required"`
}

type ReceiveTemplateWebhookPayloadPayload struct {
	ServiceId    uuid.UUID                   `json:"serviceId" validate:"required"`
	AccountId    uuid.UUID                   `json:"accountId" validate:"required"`
	BuiltWebhook *adapter_types.BuiltWebhook `json:"builtWebhook" validate:"required"`
}

type ReceiveTemplateWebhookPayload struct {
	Payload  *ReceiveTemplateWebhookPayloadPayload `json:"payload" validate:"required"`
	Metadata *Metadata                             `json:"metadata"`
}

type ReceiveTemplateWebhookRequest struct {
	Payload *ReceiveTemplateWebhookPayload `json:"payload" validate:"required"`
}

type ReceiveServiceWebhookPayloadPayload struct {
	ServiceId    uuid.UUID                   `json:"serviceId" validate:"required"`
	AccountId    uuid.UUID                   `json:"accountId" validate:"required"`
	BuiltWebhook *adapter_types.BuiltWebhook `json:"builtWebhook" validate:"required"`
}

type ReceiveServiceWebhookPayload struct {
	Payload  *ReceiveServiceWebhookPayloadPayload `json:"payload" validate:"required"`
	Metadata *Metadata                            `json:"metadata"`
}

type ReceiveServiceWebhookRequest struct {
	Payload  *ReceiveServiceWebhookPayload `json:"payload" validate:"required"`
	Metadata *Metadata                     `json:"metadata"`
}

type ReceiveMediaPayloadPayload struct {
	MessageId    uuid.UUID                   `json:"messageId" validate:"required"`
	BuiltWebhook *adapter_types.BuiltWebhook `json:"builtWebhook" validate:"required"`
	ServiceId    uuid.UUID                   `json:"serviceId" validate:"required"`
	AccountId    uuid.UUID                   `json:"accountId" validate:"required"`
}

type ReceiveMediaPayload struct {
	Payload  *ReceiveMediaPayloadPayload `json:"payload" validate:"required"`
	Metadata *Metadata                   `json:"metadata"`
}

type ReceiveMediaRequest struct {
	Payload  *ReceiveMediaPayload `json:"payload" validate:"required"`
	Metadata *Metadata            `json:"metadata"`
}

type ReceiveWebhookPayload struct {
	Payload   map[string]interface{} `json:"payload" validate:"required"`
	ServiceId uuid.UUID              `json:"serviceId" validate:"required"`
	AccountId uuid.UUID              `json:"accountId" validate:"required"`
}

type ReceiveWebhookRequest struct {
	Payload  *ReceiveWebhookPayload `json:"payload" validate:"required"`
	Metadata *Metadata              `json:"metadata"`
}

type ForwardMessagesPayload struct {
	ContactsIds []uuid.UUID `json:"contactsIds" validate:"required"`
	MessagesIds []uuid.UUID `json:"messagesIds" validate:"required"`
	UserId      uuid.UUID   `json:"userId" validate:"required"`
}

type ForwardMessagesRequest struct {
	Payload  *ForwardMessagesPayload `json:"payload" validate:"required"`
	Metadata *Metadata               `json:"metadata"`
}
