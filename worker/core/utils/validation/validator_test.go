//go:build unit

package validation

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestNewValidator(t *testing.T) {
	t.Run("should create a non-nil validator instance", func(t *testing.T) {
		validator := NewValidator()
		require.NotNil(t, validator)
	})

	t.Run("should validate a valid struct without errors", func(t *testing.T) {
		validator := NewValidator()

		type TestStruct struct {
			Name string `validate:"required"`
		}

		validInstance := TestStruct{Name: "Valid Name"}
		err := validator.Struct(validInstance)

		assert.NoError(t, err)
	})

	t.Run("should return an error for an invalid struct", func(t *testing.T) {
		validator := NewValidator()

		type TestStruct struct {
			Name string `validate:"required"`
		}

		invalidInstance := TestStruct{Name: ""} // Name is required but empty
		err := validator.Struct(invalidInstance)

		assert.Error(t, err)
	})

	t.Run("should return an error for a nil required struct pointer due to WithRequiredStructEnabled", func(t *testing.T) {
		validator := NewValidator()

		type InnerStruct struct {
			Value string `validate:"required"`
		}

		type OuterStruct struct {
			Nested *InnerStruct `validate:"required"`
		}

		// This struct is invalid because the Nested pointer is nil and WithRequiredStructEnabled is active.
		invalidOuterStruct := OuterStruct{Nested: nil}
		err := validator.Struct(invalidOuterStruct)

		assert.Error(t, err, "Expected an error for a nil pointer on a required struct field")
	})
}
