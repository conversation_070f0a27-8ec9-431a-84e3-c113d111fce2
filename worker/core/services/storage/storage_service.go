package storage

import (
	"context"
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/services/storage/storage_adapter"
	"fmt"
	"io"
	"log/slog"
	"time"
)

func NewStorageService(
	adapter *storage_adapter.Adapter,
) *StorageService {
	return &StorageService{
		adapter: adapter,
	}
}

type GetPresignedUrlOptions struct {
	Expiration string
}

type WriteStreamOptions struct {
	ContentLength int64
	ContentType   string
}

func (s *StorageService) GetPresignedUrl(ctx context.Context, file *models.File, options *GetPresignedUrlOptions) (string, error) {
	err := s.adapter.InitAdapter(ctx, string(file.Storage))

	if err != nil {
		slog.ErrorContext(ctx, "Failed to init adapter", slog.String("storage", string(file.Storage)), slog.String("error", err.Error()))
		return "", fmt.Errorf("failed to init adapter: %w", err)
	}

	var expiration time.Duration

	if options != nil && options.Expiration != "" {
		expiration = 24 * time.Hour
	}

	if file.Name == "" {
		err := fmt.Errorf("file name not found")
		slog.ErrorContext(ctx, "File name not found", slog.String("fileId", file.Id.String()), slog.String("error", err.Error()))
		return "", err
	}

	responseContentDisposition := fmt.Sprintf(`attachment; filename=%s`, file.Name)

	presignedUrl, err := s.adapter.GetPresignedUrl(ctx,
		&storage_adapter.GetPresignedUrlOptions{
			Key:                        file.Filepath,
			Expiration:                 expiration,
			ResponseContentDisposition: responseContentDisposition,
		})

	if err != nil {
		slog.ErrorContext(ctx, "Failed to get presigned URL", slog.String("filePath", file.Filepath), slog.String("error", err.Error()))
		return "", fmt.Errorf("failed to get presigned URL: %w", err)
	}

	return presignedUrl, nil
}

func (s *StorageService) WriteStream(ctx context.Context, file *models.File, stream io.Reader, options *WriteStreamOptions) error {
	err := s.adapter.InitAdapter(ctx, string(file.Storage))

	if err != nil {
		slog.ErrorContext(ctx, "Failed to init adapter", slog.String("storage", string(file.Storage)), slog.String("error", err.Error()))
		return fmt.Errorf("failed to init adapter: %w", err)
	}

	_, err = s.adapter.Write(ctx,
		&storage_adapter.WriteOptions{
			Key:           file.Filepath,
			Body:          stream,
			ContentLength: options.ContentLength,
			ContentType:   options.ContentType,
		})

	if err != nil {
		slog.ErrorContext(ctx, "Failed to write stream",
			slog.String("filePath", file.Filepath),
			slog.Int64("contentLength", options.ContentLength),
			slog.String("contentType", options.ContentType),
			slog.String("error", err.Error()))
		return fmt.Errorf("failed to write stream: %w", err)
	}

	return nil
}

func (s *StorageService) GetStream(ctx context.Context, file *models.File) (io.Reader, error) {
	err := s.adapter.InitAdapter(ctx, string(file.Storage))

	if err != nil {
		slog.ErrorContext(ctx, "Failed to init adapter", slog.String("storage", string(file.Storage)), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to init adapter: %w", err)
	}

	stream, err := s.adapter.Read(ctx, &storage_adapter.ReadOptions{
		Key: file.Filepath,
	})

	if err != nil {
		slog.ErrorContext(ctx, "Failed to get stream", slog.String("filePath", file.Filepath), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to get stream: %w", err)
	}

	return stream.Body, nil
}

func (s *StorageService) Clone(ctx context.Context, sourceFile *models.File, destinationFile *models.File) error {
	if sourceFile.Storage != destinationFile.Storage {
		err := fmt.Errorf("source and destination files must use the same storage")
		slog.ErrorContext(ctx, "Failed to clone file",
			slog.String("sourceStorage", string(sourceFile.Storage)),
			slog.String("destinationStorage", string(destinationFile.Storage)),
			slog.String("error", err.Error()))
		return err
	}

	err := s.adapter.InitAdapter(ctx, string(sourceFile.Storage))

	if err != nil {
		slog.ErrorContext(ctx, "Failed to init adapter for cloning",
			slog.String("storage", string(sourceFile.Storage)),
			slog.String("error", err.Error()))
		return fmt.Errorf("failed to init adapter for cloning: %w", err)
	}

	if sourceFile.Filepath == "" {
		sourceFile.Filepath = fmt.Sprintf("%s/%s", sourceFile.AccountId, sourceFile.Filename)
	}

	if destinationFile.Filepath == "" {
		destinationFile.Filepath = fmt.Sprintf("%s/%s", destinationFile.AccountId, destinationFile.Filename)
	}

	_, err = s.adapter.Clone(ctx, sourceFile.Filepath, destinationFile.Filepath)

	if err != nil {
		slog.ErrorContext(ctx, "Failed to clone file",
			slog.String("sourceFilePath", sourceFile.Filepath),
			slog.String("destinationFilePath", destinationFile.Filepath),
			slog.String("error", err.Error()))
		return fmt.Errorf("failed to clone file: %w", err)
	}

	return nil
}
