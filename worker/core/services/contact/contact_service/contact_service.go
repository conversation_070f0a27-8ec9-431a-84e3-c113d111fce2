package contact_service

import (
	"bytes"
	"context"
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/repositories"
	contact_block_list_service "digisac-go/worker/core/services/contact_block_list"
	"digisac-go/worker/core/services/event"
	fileService "digisac-go/worker/core/services/file"
	"digisac-go/worker/core/services/person"
	storageService "digisac-go/worker/core/services/storage"
	"digisac-go/worker/core/utils/common"
	"digisac-go/worker/core/utils/contact_utils"
	fileutils "digisac-go/worker/core/utils/file_utils"
	streamutils "digisac-go/worker/core/utils/stream_utils"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"strings"

	"github.com/google/uuid"
	gormrepository "github.com/ikateclab/gorm-repository"
	"gorm.io/gorm"
)

type SaveContactPayload struct {
	ServiceId       uuid.UUID
	AccountId       uuid.UUID
	Name            string
	AlternativeName string
	IdFromService   string
	IsGroup         bool
	IsMe            bool
	Visible         bool
	AvatarUrl       string
}

type ContactService struct {
	contactRepository repositories.ContactRepository
	fileRepository    repositories.FileRepository
	storageService    *storageService.StorageService
	serviceRepository repositories.ServiceRepository
	blockListService  *contact_block_list_service.ContactBlockListService
	db                *gorm.DB
	personRepository  repositories.PersonRepository
	personService     *person.PersonService
	eventService      *event.EventService
	fileService       *fileService.FileService
}

func NewContactService(
	contactRepository repositories.ContactRepository,
	fileRepository repositories.FileRepository,
	storageService *storageService.StorageService,
	serviceRepository repositories.ServiceRepository,
	blockListService *contact_block_list_service.ContactBlockListService,
	db *gorm.DB,
	personRepository repositories.PersonRepository,
	personService *person.PersonService,
	eventService *event.EventService,
	fileService *fileService.FileService,
) *ContactService {
	return &ContactService{
		contactRepository: contactRepository,
		fileRepository:    fileRepository,
		storageService:    storageService,
		serviceRepository: serviceRepository,
		blockListService:  blockListService,
		db:                db,
		personRepository:  personRepository,
		personService:     personService,
		eventService:      eventService,
		fileService:       fileService,
	}
}

func (m *ContactService) SaveContact(ctx context.Context, payload *SaveContactPayload, tx *gormrepository.Tx, etx *event.Etx) (contact *models.Contact, err error) {
	service, err := m.serviceRepository.FindById(ctx, payload.ServiceId)
	if err != nil {
		return nil, fmt.Errorf("failed to find service: %w", err)
	}

	idFromServiceVariants := contact_utils.GenerateIdFromServiceVariants(payload.IdFromService, service.Type)

	contact, err = m.contactRepository.FindOne(
		ctx,
		repositories.WithTx(tx),
		repositories.WithQueryStruct(
			map[string]interface{}{
				"serviceId":  payload.ServiceId,
				"accountId":  payload.AccountId,
				"archivedAt": nil,
			},
		),
		repositories.WithRelations("Account"),
		repositories.WithQuery(func(d *gorm.DB) *gorm.DB {
			return d.Where(`"idFromService" IN ?`, idFromServiceVariants)
		}),
	)

	if err != nil && err.Error() != "record not found" {
		slog.ErrorContext(ctx, "Error finding contact",
			slog.String("error", err.Error()),
			slog.String("idFromService", payload.IdFromService),
			slog.String("serviceId", payload.ServiceId.String()),
			slog.String("accountId", payload.AccountId.String()))
		return nil, fmt.Errorf("error finding contact: %w", err)
	}

	if contact != nil {
		needsUpdate := false

		if contact.Name != payload.Name && payload.Name != "" {
			contact.Name = payload.Name
			needsUpdate = true
		}

		if contact.AlternativeName != payload.AlternativeName && payload.AlternativeName != "" {
			contact.AlternativeName = payload.AlternativeName
			needsUpdate = true
		}

		if contact.IsGroup != payload.IsGroup {
			contact.IsGroup = payload.IsGroup
			needsUpdate = true
		}

		if contact.IsMe != payload.IsMe {
			contact.IsMe = payload.IsMe
			needsUpdate = true
		}

		if !contact.Visible && payload.Visible {
			contact.Visible = payload.Visible
			needsUpdate = true
		}

		if needsUpdate {
			err = m.contactRepository.UpdateById(ctx, contact.Id, contact, repositories.WithTx(tx))

			if err != nil {
				slog.ErrorContext(ctx, "Error updating contact",
					slog.String("error", err.Error()),
					slog.String("contactId", contact.Id.String()))
				return nil, fmt.Errorf("error updating contact: %w", err)
			}

			err = m.eventService.Dispatch(
				ctx,
				event.EventContactUpdated,
				&event.DispatchPayload{
					Id:        contact.Id,
					AccountId: contact.AccountId,
				},
				&event.EventServiceDispatchOptions{
					DebounceKey: contact.Id.String(),
					Etx:         etx,
				})

			if err != nil {
				slog.ErrorContext(ctx, "Failed to dispatch contact updated event", slog.String("error", err.Error()), slog.Any("contact", contact))
				return nil, fmt.Errorf("failed to dispatch contact updated event: %w", err)
			}
		}
	} else {
		blockedControl, err := m.blockListService.NumberIsBlockedBySomeBlocklist(ctx, payload.IdFromService, payload.ServiceId)
		if err != nil {
			slog.ErrorContext(ctx, "Failed to check if contact is blocked",
				slog.String("error", err.Error()),
				slog.String("idFromService", payload.IdFromService),
				slog.String("serviceId", payload.ServiceId.String()))
			return nil, fmt.Errorf("failed to check if contact is blocked: %w", err)
		} else if blockedControl != nil {
			slog.DebugContext(ctx, "contact is blocked",
				slog.String("idFromService", payload.IdFromService),
				slog.String("serviceId", payload.ServiceId.String()),
				slog.String("reason", blockedControl.Reason))
		}

		contact = &models.Contact{
			IdFromService:   payload.IdFromService,
			Name:            payload.Name,
			AlternativeName: payload.AlternativeName,
			IsGroup:         payload.IsGroup,
			Visible:         payload.Visible,
			Origin:          "driver",
			IsMe:            payload.IsMe,
			Data: &models.ContactData{
				Number: strings.Replace(payload.IdFromService, "@c.us", "", 1),
			},
			AccountId: payload.AccountId,
			ServiceId: payload.ServiceId,
		}

		if blockedControl != nil {
			contact.Block = true
			contact.ContactBlockListControlId = blockedControl.Id
			contact.DataBlock = &models.ContactDataBlock{
				ByUserId:    blockedControl.UserId,
				Description: blockedControl.Reason,
				Date:        blockedControl.CreatedAt,
				Level:       1,
			}
		}

		err = m.contactRepository.Create(ctx, contact, repositories.WithTx(tx))

		if err != nil {
			slog.ErrorContext(ctx, "Error creating contact", slog.String("error", err.Error()), slog.Any("contact", contact))
			return nil, fmt.Errorf("error creating contact: %w", err)
		}

		err = m.eventService.Dispatch(
			ctx,
			event.EventContactCreated,
			&event.DispatchPayload{
				Id:        contact.Id,
				AccountId: contact.AccountId,
			},
			&event.EventServiceDispatchOptions{
				Etx: etx,
			})

		if err != nil {
			slog.ErrorContext(ctx, "Failed to dispatch contact updated event", slog.String("error", err.Error()), slog.Any("contact", contact))
			return nil, fmt.Errorf("failed to dispatch contact updated event: %w", err)
		}
	}

	if payload.AvatarUrl != "" {
		err = m.processContactAvatar(ctx, contact, payload.AvatarUrl, tx)
		if err != nil {
			slog.ErrorContext(ctx, "Error processing avatar",
				slog.String("error", err.Error()),
				slog.String("contactId", contact.Id.String()),
				slog.String("avatarUrl", payload.AvatarUrl))
		}
	}

	contact, err = m.contactRepository.FindById(ctx, contact.Id, repositories.WithTx(tx), repositories.WithRelations("Account", "Service"))

	if err != nil {
		return nil, fmt.Errorf("error finding contact after save: %w", err)
	}

	if contact != nil && contact.PersonId == uuid.Nil {
		err = m.personService.LinkContactToPerson(ctx, contact, tx, etx)
		if err != nil {
			slog.ErrorContext(ctx, "Failed to link contact to person",
				slog.String("error", err.Error()),
				slog.String("contactId", contact.Id.String()))
			return nil, fmt.Errorf("failed to link contact to person: %w", err)
		}
	}

	return contact, nil
}

func (m *ContactService) processContactAvatar(ctx context.Context, contact *models.Contact, avatarUrl string, tx *gormrepository.Tx) error {
	resp, err := http.Get(avatarUrl)
	if err != nil {
		slog.ErrorContext(ctx, "Error getting avatar image", slog.String("error", err.Error()))
		return fmt.Errorf("error getting avatar image: %w", err)
	}

	defer func() {
		if err := resp.Body.Close(); err != nil {
			fmt.Println("Error closing response body:", err)
		}
	}()

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		slog.ErrorContext(ctx, "Error reading avatar image data", slog.String("error", err.Error()))
		return fmt.Errorf("error reading avatar image data: %w", err)
	}

	stream := bytes.NewReader(data)

	_, bufferedStream, err := streamutils.GetStreamSizeBuffered(stream)
	if err != nil {
		slog.ErrorContext(ctx, "Error getting stream size", slog.String("error", err.Error()))
		return fmt.Errorf("error getting stream size: %w", err)
	}

	checksum, err := common.GetChecksum(ctx, bytes.NewReader(data))
	if err != nil {
		slog.ErrorContext(ctx, "Error getting checksum", slog.String("error", err.Error()))
		return fmt.Errorf("error getting checksum: %w", err)
	}

	fileInfo, err := fileutils.GetFileInfo(avatarUrl)
	if err != nil {
		slog.ErrorContext(ctx, "Error getting file info", slog.String("error", err.Error()))
		return fmt.Errorf("error getting file info: %w", err)
	}

	file, err := m.fileRepository.FindOne(
		ctx,
		repositories.WithTx(tx),
		repositories.WithQueryStruct(
			map[string]interface{}{
				"attachedType": "contact",
				"attachedId":   contact.Id,
				"accountId":    contact.AccountId,
			},
		),
	)

	if err == nil {
		if file.Checksum == checksum {
			slog.InfoContext(ctx, "The contact's avatar already has the same checksum", slog.Any("file", file))
			return nil
		}

		err = m.fileRepository.DeleteById(ctx, file.Id, repositories.WithTx(tx))
		if err != nil {
			slog.ErrorContext(ctx, "Error deleting file record", slog.String("error", err.Error()), slog.Any("file", file))
			return fmt.Errorf("error deleting file record: %w", err)
		}
	}

	file, err = m.fileService.CreateFile(ctx, &fileService.CreateFilePayload{
		Name:         fileInfo.Name + "." + fileInfo.Extension,
		AttachedId:   contact.Id,
		AttachedType: "contact",
		Mimetype:     fileInfo.Mimetype,
		AccountId:    contact.AccountId,
		Stream:       bufferedStream,
	}, tx)
	if err != nil {
		slog.ErrorContext(ctx, "Process contact avatar create file failed", slog.String("error", err.Error()), slog.Any("file", file))
		return fmt.Errorf("process contact avatar create file failed: %w", err)
	}

	return nil
}
