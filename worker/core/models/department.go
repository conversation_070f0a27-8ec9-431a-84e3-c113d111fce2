package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Department struct {
	Id             uuid.UUID  `gorm:"type:uuid;primary_key" json:"id,omitempty"`
	Name           string     `gorm:"type:text" json:"name,omitempty"`
	AccountId      uuid.UUID  `gorm:"type:uuid" json:"accountId,omitempty"`
	DistributionId string     `gorm:"type:uuid" json:"distributionId,omitempty"`
	CreatedAt      *time.Time `json:"createdAt,omitempty"`
	UpdatedAt      *time.Time `json:"updatedAt,omitempty"`
	ArchivedAt     *time.Time `gorm:"type:timestamptz;default:null" json:"archivedAt,omitempty"`
	Account        *Account   `gorm:"foreignKey:AccountId" json:"account,omitempty"`
	Tickets        []*Ticket  `gorm:"foreignKey:DepartmentId" json:"tickets,omitempty"`
	Users          []*User    `gorm:"many2many:user_departments;foreignKey:Id;joinForeignKey:DepartmentId;References:Id;joinReferences:UserId"`
	//Distribution   Distribution `gorm:"foreignKey:DistributionId;constraint:onUpdate:SET NULL,onDelete:SET NULL"`
	//Schedules      []Schedule   `gorm:"foreignKey:DepartmentId"`
}

func (Department) TableName() string {
	return "departments"
}

func (d *Department) BeforeCreate(tx *gorm.DB) (err error) {
	d.Id = uuid.New()
	return
}
