package gupshup_adapter

import (
	"context"
	"digisac-go/worker/core/repositories"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"net/url"
	"strings"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type GetPartnerAppsResponse struct {
	Status          string                `json:"status"`
	PartnerAppsList []*GupshupPartnerData `json:"partnerAppsList"`
}

type SetPartnerAppResponse struct {
	PartnerApp *GupshupPartnerData `json:"partnerApps"`
}

type GupshupPartnerData struct {
	CreatedOn  int64  `json:"createdOn"`
	Healthy    bool   `json:"healthy"`
	Id         string `json:"id"`
	Live       bool   `json:"live"`
	ModifiedOn int64  `json:"modifiedOn"`
	Name       string `json:"name"`
	PartnerId  int    `json:"partnerId"`
	Phone      string `json:"phone"`
	Stopped    bool   `json:"stopped"`
	WalletId   string `json:"walletId"`
}

type PartnerData struct {
	Token               string `json:"token"`
	Id                  int    `json:"id"`
	Name                string `json:"name"`
	TermsRead           bool   `json:"terms_read"`
	Admin               bool   `json:"admin"`
	Email               string `json:"email"`
	ActivationRead      bool   `json:"activationRead"`
	BillingType         string `json:"billingType"`
	ContactName         string `json:"contactName"`
	PhoneNumber         string `json:"phoneNumber"`
	EnableCustomer      bool   `json:"enableCustomer"`
	EnableWallet        bool   `json:"enableWallet"`
	EnableInrWallet     bool   `json:"enableInrWallet"`
	EnableLoaderWallet  bool   `json:"enableLoaderWallet"`
	EnableAppOnboarding bool   `json:"enableAppOnboarding"`
	IsTpp               bool   `json:"isTpp"`
	OnboardEnabled      bool   `json:"onboardEnabled"`
}

type GetAppTokenResponse struct {
	Status string `json:"status"`
	Token  *Token `json:"token"`
}

type Token struct {
	Token        string `json:"token"`
	AuthoriserId string `json:"authoriserId"`
	RequestorId  string `json:"requestorId"`
	CreatedOn    int64  `json:"createdOn"`
	ModifiedOn   int64  `json:"modifiedOn"`
	ExpiresOn    int64  `json:"expiresOn"`
	Active       bool   `json:"active"`
}

type GetAllSubscriptionsResponse struct {
	Status        string          `json:"status"`
	Subscriptions []*Subscription `json:"subscriptions"`
}

type SetSubscriptionResponse struct {
	Status       string        `json:"status"`
	Subscription *Subscription `json:"subscription"`
}

type Subscription struct {
	Active     bool     `json:"active"`
	AppId      string   `json:"appId"`
	CreatedOn  int64    `json:"createdOn"`
	Id         string   `json:"id"`
	Mode       int      `json:"mode"`
	Modes      []string `json:"modes"`
	ModifiedOn int64    `json:"modifiedOn"`
	ShowOnUI   bool     `json:"showOnUI"`
	Tag        string   `json:"tag"`
	URL        string   `json:"url"`
	Version    int      `json:"version"`
}

// Deleta um webhook do app
// https://partner-docs.gupshup.io/reference/delete_partner-app-appid-subscription-subscriptionid
func (a *GupshupAdapter) deleteSubscription(ctx context.Context, serviceId uuid.UUID, subscriptionId string) (err error) {
	slog.InfoContext(ctx, "deleteSubscription called", slog.String("serviceId", serviceId.String()), slog.String("subscriptionId", subscriptionId))

	client, err := a.GetClient(ctx, serviceId)

	if err != nil {
		return fmt.Errorf("failed to get client for serviceId %s: %w", serviceId, err)
	}

	service, err := a.ServiceRepository.FindById(ctx, serviceId)

	if err != nil {
		return fmt.Errorf("failed to find service with serviceId %s: %w", serviceId, err)
	}

	endpoint := a.partnerApiUrl + "/app/" + service.InternalData.Id + "/subscription/" + subscriptionId

	req, err := http.NewRequest("DELETE", endpoint, nil)

	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Add("accept", "application/json")
	req.Header.Add("Authorization", service.InternalData.AppToken)

	res, err := client.Do(req)

	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}

	defer func() {
		if err := res.Body.Close(); err != nil {
			fmt.Println("Error closing response body:", err)
		}
	}()

	_, err = io.ReadAll(res.Body)

	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}

	if res.StatusCode != 204 && res.StatusCode != 200 {
		return fmt.Errorf("failed to delete subscription: %d", res.StatusCode)
	}

	return nil
}

// Pega todos os webhooks do app
// https://partner-docs.gupshup.io/reference/get_partner-app-appid-subscription
func (a *GupshupAdapter) getAllSubscriptions(ctx context.Context, serviceId uuid.UUID) (response *GetAllSubscriptionsResponse, err error) {
	slog.InfoContext(ctx, "GetAllSubscriptions called", slog.String("serviceId", serviceId.String()))

	client, err := a.GetClient(ctx, serviceId)

	if err != nil {
		return nil, fmt.Errorf("failed to get client for serviceId %s: %w", serviceId, err)
	}

	service, err := a.ServiceRepository.FindById(ctx, serviceId)

	if err != nil {
		return nil, fmt.Errorf("failed to find service with serviceId %s: %w", serviceId, err)
	}

	endpoint := a.partnerApiUrl + "/app/" + service.InternalData.Id + "/subscription"

	req, err := http.NewRequest("GET", endpoint, nil)

	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Add("accept", "application/json")
	req.Header.Add("Authorization", service.InternalData.AppToken)

	res, err := client.Do(req)

	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}

	defer func() {
		if err := res.Body.Close(); err != nil {
			fmt.Println("Error closing response body:", err)
		}
	}()

	body, err := io.ReadAll(res.Body)

	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	if res.StatusCode != 200 {
		return nil, fmt.Errorf("failed to get app subscriptions: %d", res.StatusCode)
	}

	err = json.Unmarshal(body, &response)

	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal response body: %w", err)
	}

	return response, nil
}

// Seta o webhook
// https://partner-docs.gupshup.io/reference/setsubscription-api-v3
func (a *GupshupAdapter) setSubscription(ctx context.Context, serviceId uuid.UUID, callbackUrl string) (subscriptionResponse *SetSubscriptionResponse, err error) {
	slog.InfoContext(ctx, "setSubscription called", slog.String("serviceId", serviceId.String()), slog.String("url", callbackUrl))

	client, err := a.GetClient(ctx, serviceId)

	if err != nil {
		return nil, fmt.Errorf("failed to get client for serviceId %s: %w", serviceId, err)
	}

	service, err := a.ServiceRepository.FindById(ctx, serviceId)

	if err != nil {
		return nil, fmt.Errorf("failed to find service with serviceId %s: %w", serviceId, err)
	}

	endpoint := a.partnerApiUrl + "/app/" + service.InternalData.Id + "/subscription"

	formData := url.Values{}

	formData.Add("url", callbackUrl)
	formData.Add("modes", "FAILED,DELETED,ENQUEUED,DELIVERED,SENT,READ,MESSAGE")
	formData.Add("showOnUI", "true")
	formData.Add("version", "3")

	// Para sobrescreve o webhook atual que é gerado automaticamente pela digisac
	// caso haja com nomes diferentes mantemos pois o cliente pode ter mais de um webhook
	formData.Add("tag", "default_callback")

	req, _ := http.NewRequest("POST", endpoint, strings.NewReader(formData.Encode()))

	req.Header.Add("accept", "application/json")
	req.Header.Add("content-type", "application/x-www-form-urlencoded")
	req.Header.Add("Authorization", service.InternalData.AppToken)

	res, err := client.Do(req)

	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}

	defer func() {
		if err := res.Body.Close(); err != nil {
			fmt.Println("Error closing response body:", err)
		}
	}()

	body, err := io.ReadAll(res.Body)

	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	if res.StatusCode != 200 {
		return nil, fmt.Errorf("failed to set app subscription: %d", res.StatusCode)
	}

	err = json.Unmarshal(body, &subscriptionResponse)

	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal response body: %w", err)
	}

	return subscriptionResponse, nil
}

// Login no Gupshup do Partner (Digisac) via email e senha que está na env
// https://partner-docs.gupshup.io/reference/post_partner-account-login
func (a *GupshupAdapter) loginPartner(ctx context.Context, serviceId uuid.UUID) (response *PartnerData, err error) {
	slog.InfoContext(ctx, "loginPartner called", slog.String("serviceId", serviceId.String()))

	client, err := a.GetClient(ctx, serviceId)

	if err != nil {
		return nil, fmt.Errorf("failed to get client for serviceId %s: %w", serviceId, err)
	}

	service, err := a.ServiceRepository.FindById(ctx, serviceId, repositories.WithQuery(func(db *gorm.DB) *gorm.DB {
		return db.Select("internalData")
	}))

	if err != nil {
		return nil, fmt.Errorf("failed to find service with serviceId %s: %w", serviceId, err)
	}

	endpoint := a.partnerApiUrl + "/account/login"

	formData := url.Values{}
	formData.Add("email", a.Config.GupshupEmail)
	formData.Add("password", a.Config.GupshupPassword)

	req, err := http.NewRequest("POST", endpoint, strings.NewReader(formData.Encode()))

	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Add("accept", "application/json")
	req.Header.Add("content-type", "application/x-www-form-urlencoded")
	req.Header.Add("Authorization", "Bearer "+service.InternalData.PartnerToken)

	res, err := client.Do(req)

	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}

	defer func() {
		if err := res.Body.Close(); err != nil {
			fmt.Println("Error closing response body:", err)
		}
	}()

	body, err := io.ReadAll(res.Body)

	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	if res.StatusCode != 200 {
		return nil, fmt.Errorf("failed to login partner: %d", res.StatusCode)
	}

	err = json.Unmarshal(body, &response)

	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal response body: %w", err)
	}

	return response, nil
}

// Vincula o App Gupshup do cliente com o Partner (Digisac)
// https://partner-docs.gupshup.io/reference/post_partner-account-api-applink
func (a *GupshupAdapter) setPartner(ctx context.Context, serviceId uuid.UUID) (response *SetPartnerAppResponse, err error) {
	slog.InfoContext(ctx, "setPartner called", slog.String("serviceId", serviceId.String()))

	client, err := a.GetClient(ctx, serviceId)

	if err != nil {
		return nil, fmt.Errorf("failed to get client for serviceId %s: %w", serviceId, err)
	}

	service, err := a.ServiceRepository.FindById(ctx, serviceId)

	if err != nil {
		return nil, fmt.Errorf("failed to find service with serviceId %s: %w", serviceId, err)
	}

	endpoint := a.partnerApiUrl + "/account/api/appLink"

	postForm := url.Values{}

	postForm.Add("apiKey", service.InternalData.ApiKey) // Colocado  no formulário da criação/alteração de conexão no front
	postForm.Add("appName", service.Data.AppName)       // Colocado  no formulário da criação/alteração de conexão no front

	req, err := http.NewRequest("POST", endpoint, strings.NewReader(postForm.Encode()))

	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Add("accept", "application/json")
	req.Header.Add("content-type", "application/x-www-form-urlencoded")
	req.Header.Add("Authorization", service.InternalData.PartnerToken)

	res, err := client.Do(req)

	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}

	defer func() {
		if err := res.Body.Close(); err != nil {
			fmt.Println("Error closing response body:", err)
		}
	}()

	body, err := io.ReadAll(res.Body)

	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	if res.StatusCode != 200 {
		return nil, fmt.Errorf("failed to set partner: %d", res.StatusCode)
	}

	err = json.Unmarshal(body, &response)

	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal response body: %w", err)
	}

	return response, nil
}

// Pega apps vinculados ao Partner (Digisac)
// https://partner-docs.gupshup.io/reference/get_partner-account-api-partnerapps
func (a *GupshupAdapter) getPartnerApps(ctx context.Context, serviceId uuid.UUID) (response *GetPartnerAppsResponse, err error) {
	slog.InfoContext(ctx, "getPartners called", slog.String("serviceId", serviceId.String()))

	client, err := a.GetClient(ctx, serviceId)

	if err != nil {
		return nil, fmt.Errorf("failed to get client for serviceId %s: %w", serviceId, err)
	}

	service, err := a.ServiceRepository.FindById(ctx, serviceId)

	if err != nil {
		return nil, fmt.Errorf("failed to find service with serviceId %s: %w", serviceId, err)
	}

	endpoint := a.partnerApiUrl + "/account/api/partnerApps"

	req, err := http.NewRequest("GET", endpoint, nil)

	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Add("accept", "application/json")
	req.Header.Add("Authorization", service.InternalData.PartnerToken)

	res, err := client.Do(req)

	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}

	defer func() {
		if err := res.Body.Close(); err != nil {
			fmt.Println("Error closing response body:", err)
		}
	}()

	body, err := io.ReadAll(res.Body)

	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	if res.StatusCode != 200 {
		return nil, fmt.Errorf("failed to get partner apps: %d", res.StatusCode)
	}

	err = json.Unmarshal(body, &response)

	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal response body: %w", err)
	}

	return response, nil
}

// Pega token do app
// https://partner-docs.gupshup.io/reference/get_partner-app-appid-token
func (a *GupshupAdapter) getAppToken(ctx context.Context, serviceId uuid.UUID) (response *GetAppTokenResponse, err error) {
	slog.InfoContext(ctx, "getPartners called", slog.String("serviceId", serviceId.String()))

	client, err := a.GetClient(ctx, serviceId)

	if err != nil {
		return nil, fmt.Errorf("failed to get client for serviceId %s: %w", serviceId, err)
	}

	service, err := a.ServiceRepository.FindById(ctx, serviceId)

	if err != nil {
		return nil, fmt.Errorf("failed to find service with serviceId %s: %w", serviceId, err)
	}

	endpoint := a.partnerApiUrl + "/app/" + service.InternalData.Id + "/token"

	req, err := http.NewRequest("GET", endpoint, nil)

	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Add("accept", "application/json")
	req.Header.Add("Authorization", service.InternalData.PartnerToken)

	res, err := client.Do(req)

	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}

	defer func() {
		if err := res.Body.Close(); err != nil {
			fmt.Println("Error closing response body:", err)
		}
	}()

	body, err := io.ReadAll(res.Body)

	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	if res.StatusCode != 200 {
		return nil, fmt.Errorf("failed to get app token: %d", res.StatusCode)
	}

	err = json.Unmarshal(body, &response)

	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal response body: %w", err)
	}

	return response, nil
}
