//go:build unit

package config

import (
	"fmt"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestGetRandomBuckets(t *testing.T) {
	tests := []struct {
		name                  string
		storageDriver         string
		oracleBucketNames     string
		expectedPossibilities []string
	}{
		{
			name:                  "should return driver name when not oracle",
			storageDriver:         "s3",
			oracleBucketNames:     "",
			expectedPossibilities: []string{"s3"},
		},
		{
			name:                  "should return 'oracle' when oracle driver with one bucket",
			storageDriver:         "oracle",
			oracleBucketNames:     "single-bucket",
			expectedPossibilities: []string{"oracle"},
		},
		{
			name:                  "should return 'oracle' when oracle driver with empty bucket names string",
			storageDriver:         "oracle",
			oracleBucketNames:     "", // strings.Split("", ",") results in a slice of length 1: `[""]`
			expectedPossibilities: []string{"oracle"},
		},
		{
			name:              "should return a random oracle bucket when multiple are configured",
			storageDriver:     "oracle",
			oracleBucketNames: "bucket1,bucket2,bucket3",
			expectedPossibilities: []string{
				"oracle",
				"oracle2",
				"oracle3",
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Set environment variables to mock the config functions' return values
			t.Setenv("STORAGE_DRIVER", tt.storageDriver)
			t.Setenv("ORACLE_BUCKETS_NAMES", tt.oracleBucketNames)

			result := GetRandomBuckets()

			assert.Contains(t, tt.expectedPossibilities, result, fmt.Sprintf("Expected result to be one of %v, but got %s", tt.expectedPossibilities, result))
		})
	}
}
