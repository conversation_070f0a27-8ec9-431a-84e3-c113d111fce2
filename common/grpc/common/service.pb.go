// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: common/grpc/common/proto/service.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// The `Service` entity
type Service struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                  // Unique identifier for the Service
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`              // Name of the service
	KeepOnline    bool                   `protobuf:"varint,3,opt,name=keepOnline,proto3" json:"keepOnline,omitempty"` // Whether the service should be kept online
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Service) Reset() {
	*x = Service{}
	mi := &file_common_grpc_common_proto_service_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Service) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Service) ProtoMessage() {}

func (x *Service) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_service_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Service.ProtoReflect.Descriptor instead.
func (*Service) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_service_proto_rawDescGZIP(), []int{0}
}

func (x *Service) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Service) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Service) GetKeepOnline() bool {
	if x != nil {
		return x.KeepOnline
	}
	return false
}

// Create operation
type CreateServiceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Name          string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`              // Name of the service
	KeepOnline    bool                   `protobuf:"varint,2,opt,name=keepOnline,proto3" json:"keepOnline,omitempty"` // Whether the service should be kept online
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateServiceRequest) Reset() {
	*x = CreateServiceRequest{}
	mi := &file_common_grpc_common_proto_service_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceRequest) ProtoMessage() {}

func (x *CreateServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_service_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceRequest.ProtoReflect.Descriptor instead.
func (*CreateServiceRequest) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_service_proto_rawDescGZIP(), []int{1}
}

func (x *CreateServiceRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateServiceRequest) GetKeepOnline() bool {
	if x != nil {
		return x.KeepOnline
	}
	return false
}

type CreateServiceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"` // ID of the created service
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateServiceResponse) Reset() {
	*x = CreateServiceResponse{}
	mi := &file_common_grpc_common_proto_service_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateServiceResponse) ProtoMessage() {}

func (x *CreateServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_service_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateServiceResponse.ProtoReflect.Descriptor instead.
func (*CreateServiceResponse) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_service_proto_rawDescGZIP(), []int{2}
}

func (x *CreateServiceResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// Read operation
type GetServiceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"` // ID of the service to retrieve
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetServiceRequest) Reset() {
	*x = GetServiceRequest{}
	mi := &file_common_grpc_common_proto_service_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceRequest) ProtoMessage() {}

func (x *GetServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_service_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceRequest.ProtoReflect.Descriptor instead.
func (*GetServiceRequest) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_service_proto_rawDescGZIP(), []int{3}
}

func (x *GetServiceRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type GetServiceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Service       *Service               `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"` // The service details
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *GetServiceResponse) Reset() {
	*x = GetServiceResponse{}
	mi := &file_common_grpc_common_proto_service_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *GetServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetServiceResponse) ProtoMessage() {}

func (x *GetServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_service_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetServiceResponse.ProtoReflect.Descriptor instead.
func (*GetServiceResponse) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_service_proto_rawDescGZIP(), []int{4}
}

func (x *GetServiceResponse) GetService() *Service {
	if x != nil {
		return x.Service
	}
	return nil
}

// List operation
type ListServicesRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Page          int32                  `protobuf:"varint,1,opt,name=page,proto3" json:"page,omitempty"`         // Optional pagination: page number
	PageSize      int32                  `protobuf:"varint,2,opt,name=pageSize,proto3" json:"pageSize,omitempty"` // Optional pagination: number of results per page
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListServicesRequest) Reset() {
	*x = ListServicesRequest{}
	mi := &file_common_grpc_common_proto_service_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListServicesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServicesRequest) ProtoMessage() {}

func (x *ListServicesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_service_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServicesRequest.ProtoReflect.Descriptor instead.
func (*ListServicesRequest) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_service_proto_rawDescGZIP(), []int{5}
}

func (x *ListServicesRequest) GetPage() int32 {
	if x != nil {
		return x.Page
	}
	return 0
}

func (x *ListServicesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

type ListServicesResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Services      []*Service             `protobuf:"bytes,1,rep,name=services,proto3" json:"services,omitempty"` // List of services
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ListServicesResponse) Reset() {
	*x = ListServicesResponse{}
	mi := &file_common_grpc_common_proto_service_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ListServicesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListServicesResponse) ProtoMessage() {}

func (x *ListServicesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_service_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListServicesResponse.ProtoReflect.Descriptor instead.
func (*ListServicesResponse) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_service_proto_rawDescGZIP(), []int{6}
}

func (x *ListServicesResponse) GetServices() []*Service {
	if x != nil {
		return x.Services
	}
	return nil
}

// Update operation
type UpdateServiceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                  // ID of the service to update
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`              // New name (optional)
	KeepOnline    bool                   `protobuf:"varint,3,opt,name=keepOnline,proto3" json:"keepOnline,omitempty"` // New status for keepOnline (optional)
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateServiceRequest) Reset() {
	*x = UpdateServiceRequest{}
	mi := &file_common_grpc_common_proto_service_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceRequest) ProtoMessage() {}

func (x *UpdateServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_service_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceRequest.ProtoReflect.Descriptor instead.
func (*UpdateServiceRequest) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_service_proto_rawDescGZIP(), []int{7}
}

func (x *UpdateServiceRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateServiceRequest) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *UpdateServiceRequest) GetKeepOnline() bool {
	if x != nil {
		return x.KeepOnline
	}
	return false
}

type UpdateServiceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Service       *Service               `protobuf:"bytes,1,opt,name=service,proto3" json:"service,omitempty"` // The updated service details
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *UpdateServiceResponse) Reset() {
	*x = UpdateServiceResponse{}
	mi := &file_common_grpc_common_proto_service_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *UpdateServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateServiceResponse) ProtoMessage() {}

func (x *UpdateServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_service_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateServiceResponse.ProtoReflect.Descriptor instead.
func (*UpdateServiceResponse) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_service_proto_rawDescGZIP(), []int{8}
}

func (x *UpdateServiceResponse) GetService() *Service {
	if x != nil {
		return x.Service
	}
	return nil
}

// Delete operation
type DeleteServiceRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"` // ID of the service to delete
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteServiceRequest) Reset() {
	*x = DeleteServiceRequest{}
	mi := &file_common_grpc_common_proto_service_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteServiceRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteServiceRequest) ProtoMessage() {}

func (x *DeleteServiceRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_service_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteServiceRequest.ProtoReflect.Descriptor instead.
func (*DeleteServiceRequest) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_service_proto_rawDescGZIP(), []int{9}
}

func (x *DeleteServiceRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type DeleteServiceResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"` // Whether the deletion was successful
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *DeleteServiceResponse) Reset() {
	*x = DeleteServiceResponse{}
	mi := &file_common_grpc_common_proto_service_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *DeleteServiceResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteServiceResponse) ProtoMessage() {}

func (x *DeleteServiceResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_service_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteServiceResponse.ProtoReflect.Descriptor instead.
func (*DeleteServiceResponse) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_service_proto_rawDescGZIP(), []int{10}
}

func (x *DeleteServiceResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

var File_common_grpc_common_proto_service_proto protoreflect.FileDescriptor

const file_common_grpc_common_proto_service_proto_rawDesc = "" +
	"\n" +
	"&common/grpc/common/proto/service.proto\x12\x06common\"M\n" +
	"\aService\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1e\n" +
	"\n" +
	"keepOnline\x18\x03 \x01(\bR\n" +
	"keepOnline\"J\n" +
	"\x14CreateServiceRequest\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12\x1e\n" +
	"\n" +
	"keepOnline\x18\x02 \x01(\bR\n" +
	"keepOnline\"'\n" +
	"\x15CreateServiceResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"#\n" +
	"\x11GetServiceRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"?\n" +
	"\x12GetServiceResponse\x12)\n" +
	"\aservice\x18\x01 \x01(\v2\x0f.common.ServiceR\aservice\"E\n" +
	"\x13ListServicesRequest\x12\x12\n" +
	"\x04page\x18\x01 \x01(\x05R\x04page\x12\x1a\n" +
	"\bpageSize\x18\x02 \x01(\x05R\bpageSize\"C\n" +
	"\x14ListServicesResponse\x12+\n" +
	"\bservices\x18\x01 \x03(\v2\x0f.common.ServiceR\bservices\"Z\n" +
	"\x14UpdateServiceRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1e\n" +
	"\n" +
	"keepOnline\x18\x03 \x01(\bR\n" +
	"keepOnline\"B\n" +
	"\x15UpdateServiceResponse\x12)\n" +
	"\aservice\x18\x01 \x01(\v2\x0f.common.ServiceR\aservice\"&\n" +
	"\x14DeleteServiceRequest\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"1\n" +
	"\x15DeleteServiceResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccessB\"Z digisac-go/common/grpc/common;pbb\x06proto3"

var (
	file_common_grpc_common_proto_service_proto_rawDescOnce sync.Once
	file_common_grpc_common_proto_service_proto_rawDescData []byte
)

func file_common_grpc_common_proto_service_proto_rawDescGZIP() []byte {
	file_common_grpc_common_proto_service_proto_rawDescOnce.Do(func() {
		file_common_grpc_common_proto_service_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_common_grpc_common_proto_service_proto_rawDesc), len(file_common_grpc_common_proto_service_proto_rawDesc)))
	})
	return file_common_grpc_common_proto_service_proto_rawDescData
}

var file_common_grpc_common_proto_service_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_common_grpc_common_proto_service_proto_goTypes = []any{
	(*Service)(nil),               // 0: common.Service
	(*CreateServiceRequest)(nil),  // 1: common.CreateServiceRequest
	(*CreateServiceResponse)(nil), // 2: common.CreateServiceResponse
	(*GetServiceRequest)(nil),     // 3: common.GetServiceRequest
	(*GetServiceResponse)(nil),    // 4: common.GetServiceResponse
	(*ListServicesRequest)(nil),   // 5: common.ListServicesRequest
	(*ListServicesResponse)(nil),  // 6: common.ListServicesResponse
	(*UpdateServiceRequest)(nil),  // 7: common.UpdateServiceRequest
	(*UpdateServiceResponse)(nil), // 8: common.UpdateServiceResponse
	(*DeleteServiceRequest)(nil),  // 9: common.DeleteServiceRequest
	(*DeleteServiceResponse)(nil), // 10: common.DeleteServiceResponse
}
var file_common_grpc_common_proto_service_proto_depIdxs = []int32{
	0, // 0: common.GetServiceResponse.service:type_name -> common.Service
	0, // 1: common.ListServicesResponse.services:type_name -> common.Service
	0, // 2: common.UpdateServiceResponse.service:type_name -> common.Service
	3, // [3:3] is the sub-list for method output_type
	3, // [3:3] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_common_grpc_common_proto_service_proto_init() }
func file_common_grpc_common_proto_service_proto_init() {
	if File_common_grpc_common_proto_service_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_common_grpc_common_proto_service_proto_rawDesc), len(file_common_grpc_common_proto_service_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_grpc_common_proto_service_proto_goTypes,
		DependencyIndexes: file_common_grpc_common_proto_service_proto_depIdxs,
		MessageInfos:      file_common_grpc_common_proto_service_proto_msgTypes,
	}.Build()
	File_common_grpc_common_proto_service_proto = out.File
	file_common_grpc_common_proto_service_proto_goTypes = nil
	file_common_grpc_common_proto_service_proto_depIdxs = nil
}
