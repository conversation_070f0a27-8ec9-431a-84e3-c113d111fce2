// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: common/grpc/common/proto/account.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type Account struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	IsActive      bool                   `protobuf:"varint,3,opt,name=isActive,proto3" json:"isActive,omitempty"`
	CorrelationId string                 `protobuf:"bytes,4,opt,name=correlationId,proto3" json:"correlationId,omitempty"`
	WebhookUrl    string                 `protobuf:"bytes,5,opt,name=webhookUrl,proto3" json:"webhookUrl,omitempty"`
	CreatedAt     string                 `protobuf:"bytes,6,opt,name=createdAt,proto3" json:"createdAt,omitempty"`
	UpdatedAt     string                 `protobuf:"bytes,7,opt,name=updatedAt,proto3" json:"updatedAt,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Account) Reset() {
	*x = Account{}
	mi := &file_common_grpc_common_proto_account_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Account) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Account) ProtoMessage() {}

func (x *Account) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_account_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Account.ProtoReflect.Descriptor instead.
func (*Account) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_account_proto_rawDescGZIP(), []int{0}
}

func (x *Account) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Account) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Account) GetIsActive() bool {
	if x != nil {
		return x.IsActive
	}
	return false
}

func (x *Account) GetCorrelationId() string {
	if x != nil {
		return x.CorrelationId
	}
	return ""
}

func (x *Account) GetWebhookUrl() string {
	if x != nil {
		return x.WebhookUrl
	}
	return ""
}

func (x *Account) GetCreatedAt() string {
	if x != nil {
		return x.CreatedAt
	}
	return ""
}

func (x *Account) GetUpdatedAt() string {
	if x != nil {
		return x.UpdatedAt
	}
	return ""
}

type SetAccountWebhookUrlRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	WebhookUrl    string                 `protobuf:"bytes,1,opt,name=webhookUrl,proto3" json:"webhookUrl,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SetAccountWebhookUrlRequest) Reset() {
	*x = SetAccountWebhookUrlRequest{}
	mi := &file_common_grpc_common_proto_account_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SetAccountWebhookUrlRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SetAccountWebhookUrlRequest) ProtoMessage() {}

func (x *SetAccountWebhookUrlRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_account_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SetAccountWebhookUrlRequest.ProtoReflect.Descriptor instead.
func (*SetAccountWebhookUrlRequest) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_account_proto_rawDescGZIP(), []int{1}
}

func (x *SetAccountWebhookUrlRequest) GetWebhookUrl() string {
	if x != nil {
		return x.WebhookUrl
	}
	return ""
}

type AccountResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Account       *Account               `protobuf:"bytes,1,opt,name=account,proto3" json:"account,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *AccountResponse) Reset() {
	*x = AccountResponse{}
	mi := &file_common_grpc_common_proto_account_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *AccountResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*AccountResponse) ProtoMessage() {}

func (x *AccountResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_account_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use AccountResponse.ProtoReflect.Descriptor instead.
func (*AccountResponse) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_account_proto_rawDescGZIP(), []int{2}
}

func (x *AccountResponse) GetAccount() *Account {
	if x != nil {
		return x.Account
	}
	return nil
}

var File_common_grpc_common_proto_account_proto protoreflect.FileDescriptor

const file_common_grpc_common_proto_account_proto_rawDesc = "" +
	"\n" +
	"&common/grpc/common/proto/account.proto\x12\x06common\"\xcb\x01\n" +
	"\aAccount\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1a\n" +
	"\bisActive\x18\x03 \x01(\bR\bisActive\x12$\n" +
	"\rcorrelationId\x18\x04 \x01(\tR\rcorrelationId\x12\x1e\n" +
	"\n" +
	"webhookUrl\x18\x05 \x01(\tR\n" +
	"webhookUrl\x12\x1c\n" +
	"\tcreatedAt\x18\x06 \x01(\tR\tcreatedAt\x12\x1c\n" +
	"\tupdatedAt\x18\a \x01(\tR\tupdatedAt\"=\n" +
	"\x1bSetAccountWebhookUrlRequest\x12\x1e\n" +
	"\n" +
	"webhookUrl\x18\x01 \x01(\tR\n" +
	"webhookUrl\"<\n" +
	"\x0fAccountResponse\x12)\n" +
	"\aaccount\x18\x01 \x01(\v2\x0f.common.AccountR\aaccountB\"Z digisac-go/common/grpc/common;pbb\x06proto3"

var (
	file_common_grpc_common_proto_account_proto_rawDescOnce sync.Once
	file_common_grpc_common_proto_account_proto_rawDescData []byte
)

func file_common_grpc_common_proto_account_proto_rawDescGZIP() []byte {
	file_common_grpc_common_proto_account_proto_rawDescOnce.Do(func() {
		file_common_grpc_common_proto_account_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_common_grpc_common_proto_account_proto_rawDesc), len(file_common_grpc_common_proto_account_proto_rawDesc)))
	})
	return file_common_grpc_common_proto_account_proto_rawDescData
}

var file_common_grpc_common_proto_account_proto_msgTypes = make([]protoimpl.MessageInfo, 3)
var file_common_grpc_common_proto_account_proto_goTypes = []any{
	(*Account)(nil),                     // 0: common.Account
	(*SetAccountWebhookUrlRequest)(nil), // 1: common.SetAccountWebhookUrlRequest
	(*AccountResponse)(nil),             // 2: common.AccountResponse
}
var file_common_grpc_common_proto_account_proto_depIdxs = []int32{
	0, // 0: common.AccountResponse.account:type_name -> common.Account
	1, // [1:1] is the sub-list for method output_type
	1, // [1:1] is the sub-list for method input_type
	1, // [1:1] is the sub-list for extension type_name
	1, // [1:1] is the sub-list for extension extendee
	0, // [0:1] is the sub-list for field type_name
}

func init() { file_common_grpc_common_proto_account_proto_init() }
func file_common_grpc_common_proto_account_proto_init() {
	if File_common_grpc_common_proto_account_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_common_grpc_common_proto_account_proto_rawDesc), len(file_common_grpc_common_proto_account_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   3,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_grpc_common_proto_account_proto_goTypes,
		DependencyIndexes: file_common_grpc_common_proto_account_proto_depIdxs,
		MessageInfos:      file_common_grpc_common_proto_account_proto_msgTypes,
	}.Build()
	File_common_grpc_common_proto_account_proto = out.File
	file_common_grpc_common_proto_account_proto_goTypes = nil
	file_common_grpc_common_proto_account_proto_depIdxs = nil
}
