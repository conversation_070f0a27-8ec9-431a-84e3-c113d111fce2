// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: common/grpc/common/proto/whatsapp.proto

package pb

import (
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	anypb "google.golang.org/protobuf/types/known/anypb"
	reflect "reflect"
	sync "sync"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

type ActionPayload struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ServiceId     string                 `protobuf:"bytes,1,opt,name=serviceId,proto3" json:"serviceId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActionPayload) Reset() {
	*x = ActionPayload{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[0]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActionPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActionPayload) ProtoMessage() {}

func (x *ActionPayload) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[0]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActionPayload.ProtoReflect.Descriptor instead.
func (*ActionPayload) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{0}
}

func (x *ActionPayload) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

type ActionResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ActionResponse) Reset() {
	*x = ActionResponse{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[1]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ActionResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ActionResponse) ProtoMessage() {}

func (x *ActionResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[1]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ActionResponse.ProtoReflect.Descriptor instead.
func (*ActionResponse) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{1}
}

type Metadata struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ServiceId     string                 `protobuf:"bytes,1,opt,name=serviceId,proto3" json:"serviceId,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Metadata) Reset() {
	*x = Metadata{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[2]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Metadata) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Metadata) ProtoMessage() {}

func (x *Metadata) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[2]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Metadata.ProtoReflect.Descriptor instead.
func (*Metadata) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{2}
}

func (x *Metadata) GetServiceId() string {
	if x != nil {
		return x.ServiceId
	}
	return ""
}

type File struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Url           string                 `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`
	Mimetype      string                 `protobuf:"bytes,2,opt,name=mimetype,proto3" json:"mimetype,omitempty"`
	Name          string                 `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	AsSticker     bool                   `protobuf:"varint,4,opt,name=asSticker,proto3" json:"asSticker,omitempty"`
	AsDocument    bool                   `protobuf:"varint,5,opt,name=asDocument,proto3" json:"asDocument,omitempty"`
	AsPtt         bool                   `protobuf:"varint,6,opt,name=asPtt,proto3" json:"asPtt,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *File) Reset() {
	*x = File{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[3]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *File) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*File) ProtoMessage() {}

func (x *File) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[3]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use File.ProtoReflect.Descriptor instead.
func (*File) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{3}
}

func (x *File) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *File) GetMimetype() string {
	if x != nil {
		return x.Mimetype
	}
	return ""
}

func (x *File) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *File) GetAsSticker() bool {
	if x != nil {
		return x.AsSticker
	}
	return false
}

func (x *File) GetAsDocument() bool {
	if x != nil {
		return x.AsDocument
	}
	return false
}

func (x *File) GetAsPtt() bool {
	if x != nil {
		return x.AsPtt
	}
	return false
}

// ****************************************
// SEND MESSAGE
// ****************************************
type SendMessagePayload struct {
	state           protoimpl.MessageState `protogen:"open.v1"`
	Id              string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ChatId          string                 `protobuf:"bytes,2,opt,name=chatId,proto3" json:"chatId,omitempty"`
	Text            string                 `protobuf:"bytes,3,opt,name=text,proto3" json:"text,omitempty"`
	QuotedMessageId string                 `protobuf:"bytes,4,opt,name=quotedMessageId,proto3" json:"quotedMessageId,omitempty"`
	File            *File                  `protobuf:"bytes,5,opt,name=file,proto3,oneof" json:"file,omitempty"`
	unknownFields   protoimpl.UnknownFields
	sizeCache       protoimpl.SizeCache
}

func (x *SendMessagePayload) Reset() {
	*x = SendMessagePayload{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[4]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendMessagePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMessagePayload) ProtoMessage() {}

func (x *SendMessagePayload) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[4]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMessagePayload.ProtoReflect.Descriptor instead.
func (*SendMessagePayload) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{4}
}

func (x *SendMessagePayload) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SendMessagePayload) GetChatId() string {
	if x != nil {
		return x.ChatId
	}
	return ""
}

func (x *SendMessagePayload) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *SendMessagePayload) GetQuotedMessageId() string {
	if x != nil {
		return x.QuotedMessageId
	}
	return ""
}

func (x *SendMessagePayload) GetFile() *File {
	if x != nil {
		return x.File
	}
	return nil
}

type SendMessageRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *Metadata              `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Payload       *SendMessagePayload    `protobuf:"bytes,2,opt,name=payload,proto3" json:"payload,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendMessageRequest) Reset() {
	*x = SendMessageRequest{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[5]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMessageRequest) ProtoMessage() {}

func (x *SendMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[5]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMessageRequest.ProtoReflect.Descriptor instead.
func (*SendMessageRequest) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{5}
}

func (x *SendMessageRequest) GetMetadata() *Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *SendMessageRequest) GetPayload() *SendMessagePayload {
	if x != nil {
		return x.Payload
	}
	return nil
}

type SendMessageResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendMessageResponse) Reset() {
	*x = SendMessageResponse{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[6]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendMessageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendMessageResponse) ProtoMessage() {}

func (x *SendMessageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[6]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendMessageResponse.ProtoReflect.Descriptor instead.
func (*SendMessageResponse) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{6}
}

func (x *SendMessageResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// ****************************************
// REVOKE MESSAGE
// ****************************************
type RevokeMessagePayload struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RevokeMessagePayload) Reset() {
	*x = RevokeMessagePayload{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[7]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RevokeMessagePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RevokeMessagePayload) ProtoMessage() {}

func (x *RevokeMessagePayload) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[7]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RevokeMessagePayload.ProtoReflect.Descriptor instead.
func (*RevokeMessagePayload) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{7}
}

func (x *RevokeMessagePayload) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type RevokeMessageResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Success       bool                   `protobuf:"varint,1,opt,name=success,proto3" json:"success,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RevokeMessageResponse) Reset() {
	*x = RevokeMessageResponse{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[8]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RevokeMessageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RevokeMessageResponse) ProtoMessage() {}

func (x *RevokeMessageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[8]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RevokeMessageResponse.ProtoReflect.Descriptor instead.
func (*RevokeMessageResponse) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{8}
}

func (x *RevokeMessageResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

type RevokeMessageRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *Metadata              `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Payload       *RevokeMessagePayload  `protobuf:"bytes,2,opt,name=payload,proto3" json:"payload,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *RevokeMessageRequest) Reset() {
	*x = RevokeMessageRequest{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[9]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *RevokeMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RevokeMessageRequest) ProtoMessage() {}

func (x *RevokeMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[9]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RevokeMessageRequest.ProtoReflect.Descriptor instead.
func (*RevokeMessageRequest) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{9}
}

func (x *RevokeMessageRequest) GetMetadata() *Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *RevokeMessageRequest) GetPayload() *RevokeMessagePayload {
	if x != nil {
		return x.Payload
	}
	return nil
}

// ****************************************
// FORWARD MESSAGE
// ****************************************
type ForwardMessagePayload struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	ChatId           string                 `protobuf:"bytes,2,opt,name=chatId,proto3" json:"chatId,omitempty"`
	ForwardMessageId string                 `protobuf:"bytes,3,opt,name=forwardMessageId,proto3" json:"forwardMessageId,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ForwardMessagePayload) Reset() {
	*x = ForwardMessagePayload{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[10]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ForwardMessagePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForwardMessagePayload) ProtoMessage() {}

func (x *ForwardMessagePayload) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[10]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForwardMessagePayload.ProtoReflect.Descriptor instead.
func (*ForwardMessagePayload) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{10}
}

func (x *ForwardMessagePayload) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ForwardMessagePayload) GetChatId() string {
	if x != nil {
		return x.ChatId
	}
	return ""
}

func (x *ForwardMessagePayload) GetForwardMessageId() string {
	if x != nil {
		return x.ForwardMessageId
	}
	return ""
}

type ForwardMessageResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ForwardMessageResponse) Reset() {
	*x = ForwardMessageResponse{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[11]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ForwardMessageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForwardMessageResponse) ProtoMessage() {}

func (x *ForwardMessageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[11]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForwardMessageResponse.ProtoReflect.Descriptor instead.
func (*ForwardMessageResponse) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{11}
}

func (x *ForwardMessageResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

type ForwardMessageRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *Metadata              `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Payload       *ForwardMessagePayload `protobuf:"bytes,2,opt,name=payload,proto3" json:"payload,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *ForwardMessageRequest) Reset() {
	*x = ForwardMessageRequest{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[12]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ForwardMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForwardMessageRequest) ProtoMessage() {}

func (x *ForwardMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[12]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForwardMessageRequest.ProtoReflect.Descriptor instead.
func (*ForwardMessageRequest) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{12}
}

func (x *ForwardMessageRequest) GetMetadata() *Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *ForwardMessageRequest) GetPayload() *ForwardMessagePayload {
	if x != nil {
		return x.Payload
	}
	return nil
}

// ****************************************
// SEND VCARD CONTACT MESSAGE
// ****************************************
type SendVCardContactPayload struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Name          string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendVCardContactPayload) Reset() {
	*x = SendVCardContactPayload{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[13]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendVCardContactPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendVCardContactPayload) ProtoMessage() {}

func (x *SendVCardContactPayload) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[13]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendVCardContactPayload.ProtoReflect.Descriptor instead.
func (*SendVCardContactPayload) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{13}
}

func (x *SendVCardContactPayload) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SendVCardContactPayload) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type SendVCardContactMessagePayload struct {
	state         protoimpl.MessageState     `protogen:"open.v1"`
	ChatId        string                     `protobuf:"bytes,1,opt,name=chatId,proto3" json:"chatId,omitempty"`
	Contacts      []*SendVCardContactPayload `protobuf:"bytes,2,rep,name=contacts,proto3" json:"contacts,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendVCardContactMessagePayload) Reset() {
	*x = SendVCardContactMessagePayload{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[14]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendVCardContactMessagePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendVCardContactMessagePayload) ProtoMessage() {}

func (x *SendVCardContactMessagePayload) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[14]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendVCardContactMessagePayload.ProtoReflect.Descriptor instead.
func (*SendVCardContactMessagePayload) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{14}
}

func (x *SendVCardContactMessagePayload) GetChatId() string {
	if x != nil {
		return x.ChatId
	}
	return ""
}

func (x *SendVCardContactMessagePayload) GetContacts() []*SendVCardContactPayload {
	if x != nil {
		return x.Contacts
	}
	return nil
}

type SendVCardContactMessageRequest struct {
	state         protoimpl.MessageState          `protogen:"open.v1"`
	Metadata      *Metadata                       `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Payload       *SendVCardContactMessagePayload `protobuf:"bytes,2,opt,name=payload,proto3" json:"payload,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendVCardContactMessageRequest) Reset() {
	*x = SendVCardContactMessageRequest{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[15]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendVCardContactMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendVCardContactMessageRequest) ProtoMessage() {}

func (x *SendVCardContactMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[15]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendVCardContactMessageRequest.ProtoReflect.Descriptor instead.
func (*SendVCardContactMessageRequest) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{15}
}

func (x *SendVCardContactMessageRequest) GetMetadata() *Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *SendVCardContactMessageRequest) GetPayload() *SendVCardContactMessagePayload {
	if x != nil {
		return x.Payload
	}
	return nil
}

type SendVCardContactMessageResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Ack           int32                  `protobuf:"varint,2,opt,name=ack,proto3" json:"ack,omitempty"`
	From          string                 `protobuf:"bytes,3,opt,name=from,proto3" json:"from,omitempty"`
	ChatId        string                 `protobuf:"bytes,4,opt,name=chatId,proto3" json:"chatId,omitempty"`
	Timestamp     string                 `protobuf:"bytes,5,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendVCardContactMessageResponse) Reset() {
	*x = SendVCardContactMessageResponse{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[16]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendVCardContactMessageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendVCardContactMessageResponse) ProtoMessage() {}

func (x *SendVCardContactMessageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[16]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendVCardContactMessageResponse.ProtoReflect.Descriptor instead.
func (*SendVCardContactMessageResponse) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{16}
}

func (x *SendVCardContactMessageResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SendVCardContactMessageResponse) GetAck() int32 {
	if x != nil {
		return x.Ack
	}
	return 0
}

func (x *SendVCardContactMessageResponse) GetFrom() string {
	if x != nil {
		return x.From
	}
	return ""
}

func (x *SendVCardContactMessageResponse) GetChatId() string {
	if x != nil {
		return x.ChatId
	}
	return ""
}

func (x *SendVCardContactMessageResponse) GetTimestamp() string {
	if x != nil {
		return x.Timestamp
	}
	return ""
}

// ****************************************
// SEND REACTION TO MESSAGE
// ****************************************
type SendReactionToMessagePayload struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ChatId        string                 `protobuf:"bytes,1,opt,name=chatId,proto3" json:"chatId,omitempty"`
	MessageId     string                 `protobuf:"bytes,2,opt,name=messageId,proto3" json:"messageId,omitempty"`
	Reaction      string                 `protobuf:"bytes,3,opt,name=reaction,proto3" json:"reaction,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendReactionToMessagePayload) Reset() {
	*x = SendReactionToMessagePayload{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[17]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendReactionToMessagePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendReactionToMessagePayload) ProtoMessage() {}

func (x *SendReactionToMessagePayload) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[17]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendReactionToMessagePayload.ProtoReflect.Descriptor instead.
func (*SendReactionToMessagePayload) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{17}
}

func (x *SendReactionToMessagePayload) GetChatId() string {
	if x != nil {
		return x.ChatId
	}
	return ""
}

func (x *SendReactionToMessagePayload) GetMessageId() string {
	if x != nil {
		return x.MessageId
	}
	return ""
}

func (x *SendReactionToMessagePayload) GetReaction() string {
	if x != nil {
		return x.Reaction
	}
	return ""
}

type SendReactionToMessageRequest struct {
	state         protoimpl.MessageState        `protogen:"open.v1"`
	Metadata      *Metadata                     `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Payload       *SendReactionToMessagePayload `protobuf:"bytes,2,opt,name=payload,proto3" json:"payload,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendReactionToMessageRequest) Reset() {
	*x = SendReactionToMessageRequest{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[18]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendReactionToMessageRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendReactionToMessageRequest) ProtoMessage() {}

func (x *SendReactionToMessageRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[18]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendReactionToMessageRequest.ProtoReflect.Descriptor instead.
func (*SendReactionToMessageRequest) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{18}
}

func (x *SendReactionToMessageRequest) GetMetadata() *Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *SendReactionToMessageRequest) GetPayload() *SendReactionToMessagePayload {
	if x != nil {
		return x.Payload
	}
	return nil
}

type SendReactionToMessageResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *SendReactionToMessageResponse) Reset() {
	*x = SendReactionToMessageResponse{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[19]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *SendReactionToMessageResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SendReactionToMessageResponse) ProtoMessage() {}

func (x *SendReactionToMessageResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[19]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SendReactionToMessageResponse.ProtoReflect.Descriptor instead.
func (*SendReactionToMessageResponse) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{19}
}

func (x *SendReactionToMessageResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// ****************************************
// CREATE GROUP
// ****************************************
type CreateGroupPayload struct {
	state          protoimpl.MessageState `protogen:"open.v1"`
	Name           string                 `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	ParticipantIds []string               `protobuf:"bytes,2,rep,name=participantIds,proto3" json:"participantIds,omitempty"`
	unknownFields  protoimpl.UnknownFields
	sizeCache      protoimpl.SizeCache
}

func (x *CreateGroupPayload) Reset() {
	*x = CreateGroupPayload{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[20]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateGroupPayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateGroupPayload) ProtoMessage() {}

func (x *CreateGroupPayload) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[20]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateGroupPayload.ProtoReflect.Descriptor instead.
func (*CreateGroupPayload) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{20}
}

func (x *CreateGroupPayload) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CreateGroupPayload) GetParticipantIds() []string {
	if x != nil {
		return x.ParticipantIds
	}
	return nil
}

type CreateGroupRequest struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Metadata      *Metadata              `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Payload       *CreateGroupPayload    `protobuf:"bytes,2,opt,name=payload,proto3" json:"payload,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateGroupRequest) Reset() {
	*x = CreateGroupRequest{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[21]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateGroupRequest) ProtoMessage() {}

func (x *CreateGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[21]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateGroupRequest.ProtoReflect.Descriptor instead.
func (*CreateGroupRequest) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{21}
}

func (x *CreateGroupRequest) GetMetadata() *Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *CreateGroupRequest) GetPayload() *CreateGroupPayload {
	if x != nil {
		return x.Payload
	}
	return nil
}

type CreateGroupParticipantResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ContactId     string                 `protobuf:"bytes,1,opt,name=contactId,proto3" json:"contactId,omitempty"`
	Success       bool                   `protobuf:"varint,2,opt,name=success,proto3" json:"success,omitempty"`
	Error         string                 `protobuf:"bytes,3,opt,name=error,proto3" json:"error,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateGroupParticipantResponse) Reset() {
	*x = CreateGroupParticipantResponse{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[22]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateGroupParticipantResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateGroupParticipantResponse) ProtoMessage() {}

func (x *CreateGroupParticipantResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[22]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateGroupParticipantResponse.ProtoReflect.Descriptor instead.
func (*CreateGroupParticipantResponse) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{22}
}

func (x *CreateGroupParticipantResponse) GetContactId() string {
	if x != nil {
		return x.ContactId
	}
	return ""
}

func (x *CreateGroupParticipantResponse) GetSuccess() bool {
	if x != nil {
		return x.Success
	}
	return false
}

func (x *CreateGroupParticipantResponse) GetError() string {
	if x != nil {
		return x.Error
	}
	return ""
}

type CreateGroupResponse struct {
	state         protoimpl.MessageState            `protogen:"open.v1"`
	Id            string                            `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	Participants  []*CreateGroupParticipantResponse `protobuf:"bytes,2,rep,name=participants,proto3" json:"participants,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *CreateGroupResponse) Reset() {
	*x = CreateGroupResponse{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[23]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CreateGroupResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateGroupResponse) ProtoMessage() {}

func (x *CreateGroupResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[23]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateGroupResponse.ProtoReflect.Descriptor instead.
func (*CreateGroupResponse) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{23}
}

func (x *CreateGroupResponse) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CreateGroupResponse) GetParticipants() []*CreateGroupParticipantResponse {
	if x != nil {
		return x.Participants
	}
	return nil
}

type LoadEarlierMessagesTillDatePayload struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	ContactId     string                 `protobuf:"bytes,1,opt,name=contactId,proto3" json:"contactId,omitempty"`
	Timestamp     int64                  `protobuf:"varint,2,opt,name=timestamp,proto3" json:"timestamp,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoadEarlierMessagesTillDatePayload) Reset() {
	*x = LoadEarlierMessagesTillDatePayload{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[24]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoadEarlierMessagesTillDatePayload) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadEarlierMessagesTillDatePayload) ProtoMessage() {}

func (x *LoadEarlierMessagesTillDatePayload) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[24]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadEarlierMessagesTillDatePayload.ProtoReflect.Descriptor instead.
func (*LoadEarlierMessagesTillDatePayload) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{24}
}

func (x *LoadEarlierMessagesTillDatePayload) GetContactId() string {
	if x != nil {
		return x.ContactId
	}
	return ""
}

func (x *LoadEarlierMessagesTillDatePayload) GetTimestamp() int64 {
	if x != nil {
		return x.Timestamp
	}
	return 0
}

type LoadEarlierMessagesTillDateRequest struct {
	state         protoimpl.MessageState              `protogen:"open.v1"`
	Metadata      *Metadata                           `protobuf:"bytes,1,opt,name=metadata,proto3" json:"metadata,omitempty"`
	Payload       *LoadEarlierMessagesTillDatePayload `protobuf:"bytes,2,opt,name=payload,proto3" json:"payload,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoadEarlierMessagesTillDateRequest) Reset() {
	*x = LoadEarlierMessagesTillDateRequest{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[25]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoadEarlierMessagesTillDateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadEarlierMessagesTillDateRequest) ProtoMessage() {}

func (x *LoadEarlierMessagesTillDateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[25]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadEarlierMessagesTillDateRequest.ProtoReflect.Descriptor instead.
func (*LoadEarlierMessagesTillDateRequest) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{25}
}

func (x *LoadEarlierMessagesTillDateRequest) GetMetadata() *Metadata {
	if x != nil {
		return x.Metadata
	}
	return nil
}

func (x *LoadEarlierMessagesTillDateRequest) GetPayload() *LoadEarlierMessagesTillDatePayload {
	if x != nil {
		return x.Payload
	}
	return nil
}

type LoadEarlierMessagesTillDateResponse struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Messages      []*MessageModel        `protobuf:"bytes,1,rep,name=messages,proto3" json:"messages,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *LoadEarlierMessagesTillDateResponse) Reset() {
	*x = LoadEarlierMessagesTillDateResponse{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[26]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *LoadEarlierMessagesTillDateResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LoadEarlierMessagesTillDateResponse) ProtoMessage() {}

func (x *LoadEarlierMessagesTillDateResponse) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[26]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LoadEarlierMessagesTillDateResponse.ProtoReflect.Descriptor instead.
func (*LoadEarlierMessagesTillDateResponse) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{26}
}

func (x *LoadEarlierMessagesTillDateResponse) GetMessages() []*MessageModel {
	if x != nil {
		return x.Messages
	}
	return nil
}

type MessageModel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Id            string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                       // Identificador único da mensagem
	Text          string                 `protobuf:"bytes,2,opt,name=text,proto3" json:"text,omitempty"`                   // Texto da mensagem
	Timestamp     string                 `protobuf:"bytes,3,opt,name=timestamp,proto3" json:"timestamp,omitempty"`         // Timestamp ISO 8601
	QuotedMessage *MessageModel          `protobuf:"bytes,4,opt,name=quotedMessage,proto3" json:"quotedMessage,omitempty"` // Mensagem citada (subestrutura)
	IsFromMe      bool                   `protobuf:"varint,5,opt,name=isFromMe,proto3" json:"isFromMe,omitempty"`          // Indicador se a mensagem é do remetente
	ContactId     string                 `protobuf:"bytes,6,opt,name=contactId,proto3" json:"contactId,omitempty"`         // ID do contato
	FromId        string                 `protobuf:"bytes,7,opt,name=fromId,proto3" json:"fromId,omitempty"`               // ID do remetente
	Type          string                 `protobuf:"bytes,8,opt,name=type,proto3" json:"type,omitempty"`                   // Tipo da mensagem
	Preview       *FileModel             `protobuf:"bytes,9,opt,name=preview,proto3" json:"preview,omitempty"`             // Pré-visualização (se aplicável)
	File          *FileModel             `protobuf:"bytes,10,opt,name=file,proto3" json:"file,omitempty"`                  // Arquivo associado (se aplicável)
	Contact       *ContactModel          `protobuf:"bytes,11,opt,name=contact,proto3" json:"contact,omitempty"`            // Contato associado
	From          *ContactModel          `protobuf:"bytes,12,opt,name=from,proto3" json:"from,omitempty"`                  // Remetente da mensagem
	Ack           string                 `protobuf:"bytes,13,opt,name=ack,proto3" json:"ack,omitempty"`                    // Estado de confirmação
	Data          *MessageDataModel      `protobuf:"bytes,14,opt,name=data,proto3" json:"data,omitempty"`                  // Dados adicionais
	IsStatus      bool                   `protobuf:"varint,15,opt,name=isStatus,proto3" json:"isStatus,omitempty"`         // Mensagem é um status do whatsapp
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessageModel) Reset() {
	*x = MessageModel{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[27]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageModel) ProtoMessage() {}

func (x *MessageModel) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[27]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageModel.ProtoReflect.Descriptor instead.
func (*MessageModel) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{27}
}

func (x *MessageModel) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *MessageModel) GetText() string {
	if x != nil {
		return x.Text
	}
	return ""
}

func (x *MessageModel) GetTimestamp() string {
	if x != nil {
		return x.Timestamp
	}
	return ""
}

func (x *MessageModel) GetQuotedMessage() *MessageModel {
	if x != nil {
		return x.QuotedMessage
	}
	return nil
}

func (x *MessageModel) GetIsFromMe() bool {
	if x != nil {
		return x.IsFromMe
	}
	return false
}

func (x *MessageModel) GetContactId() string {
	if x != nil {
		return x.ContactId
	}
	return ""
}

func (x *MessageModel) GetFromId() string {
	if x != nil {
		return x.FromId
	}
	return ""
}

func (x *MessageModel) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *MessageModel) GetPreview() *FileModel {
	if x != nil {
		return x.Preview
	}
	return nil
}

func (x *MessageModel) GetFile() *FileModel {
	if x != nil {
		return x.File
	}
	return nil
}

func (x *MessageModel) GetContact() *ContactModel {
	if x != nil {
		return x.Contact
	}
	return nil
}

func (x *MessageModel) GetFrom() *ContactModel {
	if x != nil {
		return x.From
	}
	return nil
}

func (x *MessageModel) GetAck() string {
	if x != nil {
		return x.Ack
	}
	return ""
}

func (x *MessageModel) GetData() *MessageDataModel {
	if x != nil {
		return x.Data
	}
	return nil
}

func (x *MessageModel) GetIsStatus() bool {
	if x != nil {
		return x.IsStatus
	}
	return false
}

type FileModel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Url           string                 `protobuf:"bytes,1,opt,name=url,proto3" json:"url,omitempty"`           // URL do arquivo
	MimeType      string                 `protobuf:"bytes,2,opt,name=mimeType,proto3" json:"mimeType,omitempty"` // Tipo MIME
	Size          int64                  `protobuf:"varint,3,opt,name=size,proto3" json:"size,omitempty"`        // Tamanho do arquivo
	Name          string                 `protobuf:"bytes,4,opt,name=name,proto3" json:"name,omitempty"`         // Nome do arquivo
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *FileModel) Reset() {
	*x = FileModel{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[28]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *FileModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*FileModel) ProtoMessage() {}

func (x *FileModel) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[28]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use FileModel.ProtoReflect.Descriptor instead.
func (*FileModel) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{28}
}

func (x *FileModel) GetUrl() string {
	if x != nil {
		return x.Url
	}
	return ""
}

func (x *FileModel) GetMimeType() string {
	if x != nil {
		return x.MimeType
	}
	return ""
}

func (x *FileModel) GetSize() int64 {
	if x != nil {
		return x.Size
	}
	return 0
}

func (x *FileModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

type ContactModel struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	Id               string                 `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`                              // ID do contato
	Name             string                 `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`                          // Nome do contato
	ProfilePic       string                 `protobuf:"bytes,3,opt,name=profilePic,proto3" json:"profilePic,omitempty"`              // URL da foto de perfil
	IsGroup          bool                   `protobuf:"varint,4,opt,name=isGroup,proto3" json:"isGroup,omitempty"`                   // Indicador se o contato é um grupo
	AvatarUrl        string                 `protobuf:"bytes,5,opt,name=avatarUrl,proto3" json:"avatarUrl,omitempty"`                // Correspondente a "avatarUrl", pode ser nulo ou vazio.
	IsBusiness       bool                   `protobuf:"varint,6,opt,name=isBusiness,proto3" json:"isBusiness,omitempty"`             // Correspondente a "isBusiness".
	IsContactBlocked bool                   `protobuf:"varint,7,opt,name=isContactBlocked,proto3" json:"isContactBlocked,omitempty"` // Correspondente a "isContactBlocked".
	IsEnterprise     bool                   `protobuf:"varint,8,opt,name=isEnterprise,proto3" json:"isEnterprise,omitempty"`         // Correspondente a "isEnterprise".
	IsMe             bool                   `protobuf:"varint,10,opt,name=isMe,proto3" json:"isMe,omitempty"`                        // Correspondente a "isMe".
	Number           string                 `protobuf:"bytes,12,opt,name=number,proto3" json:"number,omitempty"`                     // Correspondente a "number".
	ProfileName      string                 `protobuf:"bytes,13,opt,name=profileName,proto3" json:"profileName,omitempty"`           // Correspondente a "profileName".
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *ContactModel) Reset() {
	*x = ContactModel{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[29]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *ContactModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ContactModel) ProtoMessage() {}

func (x *ContactModel) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[29]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ContactModel.ProtoReflect.Descriptor instead.
func (*ContactModel) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{29}
}

func (x *ContactModel) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ContactModel) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ContactModel) GetProfilePic() string {
	if x != nil {
		return x.ProfilePic
	}
	return ""
}

func (x *ContactModel) GetIsGroup() bool {
	if x != nil {
		return x.IsGroup
	}
	return false
}

func (x *ContactModel) GetAvatarUrl() string {
	if x != nil {
		return x.AvatarUrl
	}
	return ""
}

func (x *ContactModel) GetIsBusiness() bool {
	if x != nil {
		return x.IsBusiness
	}
	return false
}

func (x *ContactModel) GetIsContactBlocked() bool {
	if x != nil {
		return x.IsContactBlocked
	}
	return false
}

func (x *ContactModel) GetIsEnterprise() bool {
	if x != nil {
		return x.IsEnterprise
	}
	return false
}

func (x *ContactModel) GetIsMe() bool {
	if x != nil {
		return x.IsMe
	}
	return false
}

func (x *ContactModel) GetNumber() string {
	if x != nil {
		return x.Number
	}
	return ""
}

func (x *ContactModel) GetProfileName() string {
	if x != nil {
		return x.ProfileName
	}
	return ""
}

type Location struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Lat           float32                `protobuf:"fixed32,1,opt,name=lat,proto3" json:"lat,omitempty"`
	Lng           float32                `protobuf:"fixed32,2,opt,name=lng,proto3" json:"lng,omitempty"`
	MapPreviewUrl string                 `protobuf:"bytes,3,opt,name=mapPreviewUrl,proto3" json:"mapPreviewUrl,omitempty"`
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *Location) Reset() {
	*x = Location{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[30]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *Location) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Location) ProtoMessage() {}

func (x *Location) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[30]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Location.ProtoReflect.Descriptor instead.
func (*Location) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{30}
}

func (x *Location) GetLat() float32 {
	if x != nil {
		return x.Lat
	}
	return 0
}

func (x *Location) GetLng() float32 {
	if x != nil {
		return x.Lng
	}
	return 0
}

func (x *Location) GetMapPreviewUrl() string {
	if x != nil {
		return x.MapPreviewUrl
	}
	return ""
}

type CtwaContext struct {
	state            protoimpl.MessageState `protogen:"open.v1"`
	ConversionSource string                 `protobuf:"bytes,1,opt,name=conversionSource,proto3" json:"conversionSource,omitempty"`
	Description      string                 `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	IsSuspiciousLink string                 `protobuf:"bytes,3,opt,name=isSuspiciousLink,proto3" json:"isSuspiciousLink,omitempty"`
	MediaType        int32                  `protobuf:"varint,4,opt,name=mediaType,proto3" json:"mediaType,omitempty"`
	MediaUrl         string                 `protobuf:"bytes,5,opt,name=mediaUrl,proto3" json:"mediaUrl,omitempty"`
	SourceUrl        string                 `protobuf:"bytes,6,opt,name=sourceUrl,proto3" json:"sourceUrl,omitempty"`
	ThumbnailUrl     string                 `protobuf:"bytes,7,opt,name=thumbnailUrl,proto3" json:"thumbnailUrl,omitempty"`
	Title            string                 `protobuf:"bytes,8,opt,name=title,proto3" json:"title,omitempty"`
	unknownFields    protoimpl.UnknownFields
	sizeCache        protoimpl.SizeCache
}

func (x *CtwaContext) Reset() {
	*x = CtwaContext{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[31]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *CtwaContext) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CtwaContext) ProtoMessage() {}

func (x *CtwaContext) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[31]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CtwaContext.ProtoReflect.Descriptor instead.
func (*CtwaContext) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{31}
}

func (x *CtwaContext) GetConversionSource() string {
	if x != nil {
		return x.ConversionSource
	}
	return ""
}

func (x *CtwaContext) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *CtwaContext) GetIsSuspiciousLink() string {
	if x != nil {
		return x.IsSuspiciousLink
	}
	return ""
}

func (x *CtwaContext) GetMediaType() int32 {
	if x != nil {
		return x.MediaType
	}
	return 0
}

func (x *CtwaContext) GetMediaUrl() string {
	if x != nil {
		return x.MediaUrl
	}
	return ""
}

func (x *CtwaContext) GetSourceUrl() string {
	if x != nil {
		return x.SourceUrl
	}
	return ""
}

func (x *CtwaContext) GetThumbnailUrl() string {
	if x != nil {
		return x.ThumbnailUrl
	}
	return ""
}

func (x *CtwaContext) GetTitle() string {
	if x != nil {
		return x.Title
	}
	return ""
}

type MessageDataModel struct {
	state         protoimpl.MessageState `protogen:"open.v1"`
	Ack           int32                  `protobuf:"varint,1,opt,name=ack,proto3" json:"ack,omitempty"`                // Estado de confirmação
	Product       *anypb.Any             `protobuf:"bytes,2,opt,name=product,proto3" json:"product,omitempty"`         // Dados do produto (campo genérico)
	Vcard         string                 `protobuf:"bytes,3,opt,name=vcard,proto3" json:"vcard,omitempty"`             // Dados do vcard
	Vcards        []string               `protobuf:"bytes,4,rep,name=vcards,proto3" json:"vcards,omitempty"`           // Dados dos vcards
	Location      *Location              `protobuf:"bytes,5,opt,name=location,proto3" json:"location,omitempty"`       // Dados da localização
	CtwaContext   *CtwaContext           `protobuf:"bytes,6,opt,name=ctwaContext,proto3" json:"ctwaContext,omitempty"` // CTWA = Click to Whatsapp Ads
	unknownFields protoimpl.UnknownFields
	sizeCache     protoimpl.SizeCache
}

func (x *MessageDataModel) Reset() {
	*x = MessageDataModel{}
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[32]
	ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
	ms.StoreMessageInfo(mi)
}

func (x *MessageDataModel) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*MessageDataModel) ProtoMessage() {}

func (x *MessageDataModel) ProtoReflect() protoreflect.Message {
	mi := &file_common_grpc_common_proto_whatsapp_proto_msgTypes[32]
	if x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use MessageDataModel.ProtoReflect.Descriptor instead.
func (*MessageDataModel) Descriptor() ([]byte, []int) {
	return file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP(), []int{32}
}

func (x *MessageDataModel) GetAck() int32 {
	if x != nil {
		return x.Ack
	}
	return 0
}

func (x *MessageDataModel) GetProduct() *anypb.Any {
	if x != nil {
		return x.Product
	}
	return nil
}

func (x *MessageDataModel) GetVcard() string {
	if x != nil {
		return x.Vcard
	}
	return ""
}

func (x *MessageDataModel) GetVcards() []string {
	if x != nil {
		return x.Vcards
	}
	return nil
}

func (x *MessageDataModel) GetLocation() *Location {
	if x != nil {
		return x.Location
	}
	return nil
}

func (x *MessageDataModel) GetCtwaContext() *CtwaContext {
	if x != nil {
		return x.CtwaContext
	}
	return nil
}

var File_common_grpc_common_proto_whatsapp_proto protoreflect.FileDescriptor

const file_common_grpc_common_proto_whatsapp_proto_rawDesc = "" +
	"\n" +
	"'common/grpc/common/proto/whatsapp.proto\x12\x06common\x1a\x19google/protobuf/any.proto\"-\n" +
	"\rActionPayload\x12\x1c\n" +
	"\tserviceId\x18\x01 \x01(\tR\tserviceId\"\x10\n" +
	"\x0eActionResponse\"(\n" +
	"\bMetadata\x12\x1c\n" +
	"\tserviceId\x18\x01 \x01(\tR\tserviceId\"\x9c\x01\n" +
	"\x04File\x12\x10\n" +
	"\x03url\x18\x01 \x01(\tR\x03url\x12\x1a\n" +
	"\bmimetype\x18\x02 \x01(\tR\bmimetype\x12\x12\n" +
	"\x04name\x18\x03 \x01(\tR\x04name\x12\x1c\n" +
	"\tasSticker\x18\x04 \x01(\bR\tasSticker\x12\x1e\n" +
	"\n" +
	"asDocument\x18\x05 \x01(\bR\n" +
	"asDocument\x12\x14\n" +
	"\x05asPtt\x18\x06 \x01(\bR\x05asPtt\"\xaa\x01\n" +
	"\x12SendMessagePayload\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x16\n" +
	"\x06chatId\x18\x02 \x01(\tR\x06chatId\x12\x12\n" +
	"\x04text\x18\x03 \x01(\tR\x04text\x12(\n" +
	"\x0fquotedMessageId\x18\x04 \x01(\tR\x0fquotedMessageId\x12%\n" +
	"\x04file\x18\x05 \x01(\v2\f.common.FileH\x00R\x04file\x88\x01\x01B\a\n" +
	"\x05_file\"x\n" +
	"\x12SendMessageRequest\x12,\n" +
	"\bmetadata\x18\x01 \x01(\v2\x10.common.MetadataR\bmetadata\x124\n" +
	"\apayload\x18\x02 \x01(\v2\x1a.common.SendMessagePayloadR\apayload\"%\n" +
	"\x13SendMessageResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"&\n" +
	"\x14RevokeMessagePayload\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"1\n" +
	"\x15RevokeMessageResponse\x12\x18\n" +
	"\asuccess\x18\x01 \x01(\bR\asuccess\"|\n" +
	"\x14RevokeMessageRequest\x12,\n" +
	"\bmetadata\x18\x01 \x01(\v2\x10.common.MetadataR\bmetadata\x126\n" +
	"\apayload\x18\x02 \x01(\v2\x1c.common.RevokeMessagePayloadR\apayload\"k\n" +
	"\x15ForwardMessagePayload\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x16\n" +
	"\x06chatId\x18\x02 \x01(\tR\x06chatId\x12*\n" +
	"\x10forwardMessageId\x18\x03 \x01(\tR\x10forwardMessageId\"(\n" +
	"\x16ForwardMessageResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"~\n" +
	"\x15ForwardMessageRequest\x12,\n" +
	"\bmetadata\x18\x01 \x01(\v2\x10.common.MetadataR\bmetadata\x127\n" +
	"\apayload\x18\x02 \x01(\v2\x1d.common.ForwardMessagePayloadR\apayload\"=\n" +
	"\x17SendVCardContactPayload\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\"u\n" +
	"\x1eSendVCardContactMessagePayload\x12\x16\n" +
	"\x06chatId\x18\x01 \x01(\tR\x06chatId\x12;\n" +
	"\bcontacts\x18\x02 \x03(\v2\x1f.common.SendVCardContactPayloadR\bcontacts\"\x90\x01\n" +
	"\x1eSendVCardContactMessageRequest\x12,\n" +
	"\bmetadata\x18\x01 \x01(\v2\x10.common.MetadataR\bmetadata\x12@\n" +
	"\apayload\x18\x02 \x01(\v2&.common.SendVCardContactMessagePayloadR\apayload\"\x8d\x01\n" +
	"\x1fSendVCardContactMessageResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x10\n" +
	"\x03ack\x18\x02 \x01(\x05R\x03ack\x12\x12\n" +
	"\x04from\x18\x03 \x01(\tR\x04from\x12\x16\n" +
	"\x06chatId\x18\x04 \x01(\tR\x06chatId\x12\x1c\n" +
	"\ttimestamp\x18\x05 \x01(\tR\ttimestamp\"p\n" +
	"\x1cSendReactionToMessagePayload\x12\x16\n" +
	"\x06chatId\x18\x01 \x01(\tR\x06chatId\x12\x1c\n" +
	"\tmessageId\x18\x02 \x01(\tR\tmessageId\x12\x1a\n" +
	"\breaction\x18\x03 \x01(\tR\breaction\"\x8c\x01\n" +
	"\x1cSendReactionToMessageRequest\x12,\n" +
	"\bmetadata\x18\x01 \x01(\v2\x10.common.MetadataR\bmetadata\x12>\n" +
	"\apayload\x18\x02 \x01(\v2$.common.SendReactionToMessagePayloadR\apayload\"/\n" +
	"\x1dSendReactionToMessageResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\"P\n" +
	"\x12CreateGroupPayload\x12\x12\n" +
	"\x04name\x18\x01 \x01(\tR\x04name\x12&\n" +
	"\x0eparticipantIds\x18\x02 \x03(\tR\x0eparticipantIds\"x\n" +
	"\x12CreateGroupRequest\x12,\n" +
	"\bmetadata\x18\x01 \x01(\v2\x10.common.MetadataR\bmetadata\x124\n" +
	"\apayload\x18\x02 \x01(\v2\x1a.common.CreateGroupPayloadR\apayload\"n\n" +
	"\x1eCreateGroupParticipantResponse\x12\x1c\n" +
	"\tcontactId\x18\x01 \x01(\tR\tcontactId\x12\x18\n" +
	"\asuccess\x18\x02 \x01(\bR\asuccess\x12\x14\n" +
	"\x05error\x18\x03 \x01(\tR\x05error\"q\n" +
	"\x13CreateGroupResponse\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12J\n" +
	"\fparticipants\x18\x02 \x03(\v2&.common.CreateGroupParticipantResponseR\fparticipants\"`\n" +
	"\"LoadEarlierMessagesTillDatePayload\x12\x1c\n" +
	"\tcontactId\x18\x01 \x01(\tR\tcontactId\x12\x1c\n" +
	"\ttimestamp\x18\x02 \x01(\x03R\ttimestamp\"\x98\x01\n" +
	"\"LoadEarlierMessagesTillDateRequest\x12,\n" +
	"\bmetadata\x18\x01 \x01(\v2\x10.common.MetadataR\bmetadata\x12D\n" +
	"\apayload\x18\x02 \x01(\v2*.common.LoadEarlierMessagesTillDatePayloadR\apayload\"W\n" +
	"#LoadEarlierMessagesTillDateResponse\x120\n" +
	"\bmessages\x18\x01 \x03(\v2\x14.common.MessageModelR\bmessages\"\xfc\x03\n" +
	"\fMessageModel\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04text\x18\x02 \x01(\tR\x04text\x12\x1c\n" +
	"\ttimestamp\x18\x03 \x01(\tR\ttimestamp\x12:\n" +
	"\rquotedMessage\x18\x04 \x01(\v2\x14.common.MessageModelR\rquotedMessage\x12\x1a\n" +
	"\bisFromMe\x18\x05 \x01(\bR\bisFromMe\x12\x1c\n" +
	"\tcontactId\x18\x06 \x01(\tR\tcontactId\x12\x16\n" +
	"\x06fromId\x18\a \x01(\tR\x06fromId\x12\x12\n" +
	"\x04type\x18\b \x01(\tR\x04type\x12+\n" +
	"\apreview\x18\t \x01(\v2\x11.common.FileModelR\apreview\x12%\n" +
	"\x04file\x18\n" +
	" \x01(\v2\x11.common.FileModelR\x04file\x12.\n" +
	"\acontact\x18\v \x01(\v2\x14.common.ContactModelR\acontact\x12(\n" +
	"\x04from\x18\f \x01(\v2\x14.common.ContactModelR\x04from\x12\x10\n" +
	"\x03ack\x18\r \x01(\tR\x03ack\x12,\n" +
	"\x04data\x18\x0e \x01(\v2\x18.common.MessageDataModelR\x04data\x12\x1a\n" +
	"\bisStatus\x18\x0f \x01(\bR\bisStatus\"a\n" +
	"\tFileModel\x12\x10\n" +
	"\x03url\x18\x01 \x01(\tR\x03url\x12\x1a\n" +
	"\bmimeType\x18\x02 \x01(\tR\bmimeType\x12\x12\n" +
	"\x04size\x18\x03 \x01(\x03R\x04size\x12\x12\n" +
	"\x04name\x18\x04 \x01(\tR\x04name\"\xc8\x02\n" +
	"\fContactModel\x12\x0e\n" +
	"\x02id\x18\x01 \x01(\tR\x02id\x12\x12\n" +
	"\x04name\x18\x02 \x01(\tR\x04name\x12\x1e\n" +
	"\n" +
	"profilePic\x18\x03 \x01(\tR\n" +
	"profilePic\x12\x18\n" +
	"\aisGroup\x18\x04 \x01(\bR\aisGroup\x12\x1c\n" +
	"\tavatarUrl\x18\x05 \x01(\tR\tavatarUrl\x12\x1e\n" +
	"\n" +
	"isBusiness\x18\x06 \x01(\bR\n" +
	"isBusiness\x12*\n" +
	"\x10isContactBlocked\x18\a \x01(\bR\x10isContactBlocked\x12\"\n" +
	"\fisEnterprise\x18\b \x01(\bR\fisEnterprise\x12\x12\n" +
	"\x04isMe\x18\n" +
	" \x01(\bR\x04isMe\x12\x16\n" +
	"\x06number\x18\f \x01(\tR\x06number\x12 \n" +
	"\vprofileName\x18\r \x01(\tR\vprofileName\"T\n" +
	"\bLocation\x12\x10\n" +
	"\x03lat\x18\x01 \x01(\x02R\x03lat\x12\x10\n" +
	"\x03lng\x18\x02 \x01(\x02R\x03lng\x12$\n" +
	"\rmapPreviewUrl\x18\x03 \x01(\tR\rmapPreviewUrl\"\x99\x02\n" +
	"\vCtwaContext\x12*\n" +
	"\x10conversionSource\x18\x01 \x01(\tR\x10conversionSource\x12 \n" +
	"\vdescription\x18\x02 \x01(\tR\vdescription\x12*\n" +
	"\x10isSuspiciousLink\x18\x03 \x01(\tR\x10isSuspiciousLink\x12\x1c\n" +
	"\tmediaType\x18\x04 \x01(\x05R\tmediaType\x12\x1a\n" +
	"\bmediaUrl\x18\x05 \x01(\tR\bmediaUrl\x12\x1c\n" +
	"\tsourceUrl\x18\x06 \x01(\tR\tsourceUrl\x12\"\n" +
	"\fthumbnailUrl\x18\a \x01(\tR\fthumbnailUrl\x12\x14\n" +
	"\x05title\x18\b \x01(\tR\x05title\"\xe7\x01\n" +
	"\x10MessageDataModel\x12\x10\n" +
	"\x03ack\x18\x01 \x01(\x05R\x03ack\x12.\n" +
	"\aproduct\x18\x02 \x01(\v2\x14.google.protobuf.AnyR\aproduct\x12\x14\n" +
	"\x05vcard\x18\x03 \x01(\tR\x05vcard\x12\x16\n" +
	"\x06vcards\x18\x04 \x03(\tR\x06vcards\x12,\n" +
	"\blocation\x18\x05 \x01(\v2\x10.common.LocationR\blocation\x125\n" +
	"\vctwaContext\x18\x06 \x01(\v2\x13.common.CtwaContextR\vctwaContextB\"Z digisac-go/common/grpc/common;pbb\x06proto3"

var (
	file_common_grpc_common_proto_whatsapp_proto_rawDescOnce sync.Once
	file_common_grpc_common_proto_whatsapp_proto_rawDescData []byte
)

func file_common_grpc_common_proto_whatsapp_proto_rawDescGZIP() []byte {
	file_common_grpc_common_proto_whatsapp_proto_rawDescOnce.Do(func() {
		file_common_grpc_common_proto_whatsapp_proto_rawDescData = protoimpl.X.CompressGZIP(unsafe.Slice(unsafe.StringData(file_common_grpc_common_proto_whatsapp_proto_rawDesc), len(file_common_grpc_common_proto_whatsapp_proto_rawDesc)))
	})
	return file_common_grpc_common_proto_whatsapp_proto_rawDescData
}

var file_common_grpc_common_proto_whatsapp_proto_msgTypes = make([]protoimpl.MessageInfo, 33)
var file_common_grpc_common_proto_whatsapp_proto_goTypes = []any{
	(*ActionPayload)(nil),                       // 0: common.ActionPayload
	(*ActionResponse)(nil),                      // 1: common.ActionResponse
	(*Metadata)(nil),                            // 2: common.Metadata
	(*File)(nil),                                // 3: common.File
	(*SendMessagePayload)(nil),                  // 4: common.SendMessagePayload
	(*SendMessageRequest)(nil),                  // 5: common.SendMessageRequest
	(*SendMessageResponse)(nil),                 // 6: common.SendMessageResponse
	(*RevokeMessagePayload)(nil),                // 7: common.RevokeMessagePayload
	(*RevokeMessageResponse)(nil),               // 8: common.RevokeMessageResponse
	(*RevokeMessageRequest)(nil),                // 9: common.RevokeMessageRequest
	(*ForwardMessagePayload)(nil),               // 10: common.ForwardMessagePayload
	(*ForwardMessageResponse)(nil),              // 11: common.ForwardMessageResponse
	(*ForwardMessageRequest)(nil),               // 12: common.ForwardMessageRequest
	(*SendVCardContactPayload)(nil),             // 13: common.SendVCardContactPayload
	(*SendVCardContactMessagePayload)(nil),      // 14: common.SendVCardContactMessagePayload
	(*SendVCardContactMessageRequest)(nil),      // 15: common.SendVCardContactMessageRequest
	(*SendVCardContactMessageResponse)(nil),     // 16: common.SendVCardContactMessageResponse
	(*SendReactionToMessagePayload)(nil),        // 17: common.SendReactionToMessagePayload
	(*SendReactionToMessageRequest)(nil),        // 18: common.SendReactionToMessageRequest
	(*SendReactionToMessageResponse)(nil),       // 19: common.SendReactionToMessageResponse
	(*CreateGroupPayload)(nil),                  // 20: common.CreateGroupPayload
	(*CreateGroupRequest)(nil),                  // 21: common.CreateGroupRequest
	(*CreateGroupParticipantResponse)(nil),      // 22: common.CreateGroupParticipantResponse
	(*CreateGroupResponse)(nil),                 // 23: common.CreateGroupResponse
	(*LoadEarlierMessagesTillDatePayload)(nil),  // 24: common.LoadEarlierMessagesTillDatePayload
	(*LoadEarlierMessagesTillDateRequest)(nil),  // 25: common.LoadEarlierMessagesTillDateRequest
	(*LoadEarlierMessagesTillDateResponse)(nil), // 26: common.LoadEarlierMessagesTillDateResponse
	(*MessageModel)(nil),                        // 27: common.MessageModel
	(*FileModel)(nil),                           // 28: common.FileModel
	(*ContactModel)(nil),                        // 29: common.ContactModel
	(*Location)(nil),                            // 30: common.Location
	(*CtwaContext)(nil),                         // 31: common.CtwaContext
	(*MessageDataModel)(nil),                    // 32: common.MessageDataModel
	(*anypb.Any)(nil),                           // 33: google.protobuf.Any
}
var file_common_grpc_common_proto_whatsapp_proto_depIdxs = []int32{
	3,  // 0: common.SendMessagePayload.file:type_name -> common.File
	2,  // 1: common.SendMessageRequest.metadata:type_name -> common.Metadata
	4,  // 2: common.SendMessageRequest.payload:type_name -> common.SendMessagePayload
	2,  // 3: common.RevokeMessageRequest.metadata:type_name -> common.Metadata
	7,  // 4: common.RevokeMessageRequest.payload:type_name -> common.RevokeMessagePayload
	2,  // 5: common.ForwardMessageRequest.metadata:type_name -> common.Metadata
	10, // 6: common.ForwardMessageRequest.payload:type_name -> common.ForwardMessagePayload
	13, // 7: common.SendVCardContactMessagePayload.contacts:type_name -> common.SendVCardContactPayload
	2,  // 8: common.SendVCardContactMessageRequest.metadata:type_name -> common.Metadata
	14, // 9: common.SendVCardContactMessageRequest.payload:type_name -> common.SendVCardContactMessagePayload
	2,  // 10: common.SendReactionToMessageRequest.metadata:type_name -> common.Metadata
	17, // 11: common.SendReactionToMessageRequest.payload:type_name -> common.SendReactionToMessagePayload
	2,  // 12: common.CreateGroupRequest.metadata:type_name -> common.Metadata
	20, // 13: common.CreateGroupRequest.payload:type_name -> common.CreateGroupPayload
	22, // 14: common.CreateGroupResponse.participants:type_name -> common.CreateGroupParticipantResponse
	2,  // 15: common.LoadEarlierMessagesTillDateRequest.metadata:type_name -> common.Metadata
	24, // 16: common.LoadEarlierMessagesTillDateRequest.payload:type_name -> common.LoadEarlierMessagesTillDatePayload
	27, // 17: common.LoadEarlierMessagesTillDateResponse.messages:type_name -> common.MessageModel
	27, // 18: common.MessageModel.quotedMessage:type_name -> common.MessageModel
	28, // 19: common.MessageModel.preview:type_name -> common.FileModel
	28, // 20: common.MessageModel.file:type_name -> common.FileModel
	29, // 21: common.MessageModel.contact:type_name -> common.ContactModel
	29, // 22: common.MessageModel.from:type_name -> common.ContactModel
	32, // 23: common.MessageModel.data:type_name -> common.MessageDataModel
	33, // 24: common.MessageDataModel.product:type_name -> google.protobuf.Any
	30, // 25: common.MessageDataModel.location:type_name -> common.Location
	31, // 26: common.MessageDataModel.ctwaContext:type_name -> common.CtwaContext
	27, // [27:27] is the sub-list for method output_type
	27, // [27:27] is the sub-list for method input_type
	27, // [27:27] is the sub-list for extension type_name
	27, // [27:27] is the sub-list for extension extendee
	0,  // [0:27] is the sub-list for field type_name
}

func init() { file_common_grpc_common_proto_whatsapp_proto_init() }
func file_common_grpc_common_proto_whatsapp_proto_init() {
	if File_common_grpc_common_proto_whatsapp_proto != nil {
		return
	}
	file_common_grpc_common_proto_whatsapp_proto_msgTypes[4].OneofWrappers = []any{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_common_grpc_common_proto_whatsapp_proto_rawDesc), len(file_common_grpc_common_proto_whatsapp_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   33,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_common_grpc_common_proto_whatsapp_proto_goTypes,
		DependencyIndexes: file_common_grpc_common_proto_whatsapp_proto_depIdxs,
		MessageInfos:      file_common_grpc_common_proto_whatsapp_proto_msgTypes,
	}.Build()
	File_common_grpc_common_proto_whatsapp_proto = out.File
	file_common_grpc_common_proto_whatsapp_proto_goTypes = nil
	file_common_grpc_common_proto_whatsapp_proto_depIdxs = nil
}
