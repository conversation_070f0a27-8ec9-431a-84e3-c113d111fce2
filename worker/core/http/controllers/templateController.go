package controllers

import (
	"digisac-go/worker/core/http/middlewares"
	"digisac-go/worker/core/http/payloads"
	httputils "digisac-go/worker/core/utils/http_utils"
	"fmt"
	"log/slog"
	"strings"

	templateService "digisac-go/worker/core/services/template"

	"github.com/gofiber/fiber/v3"
)

type TemplateController struct {
	templateManagerService *templateService.TemplateManagerService
}

func NewTemplateController(templateManagerService *templateService.TemplateManagerService) *TemplateController {
	return &TemplateController{
		templateManagerService: templateManagerService,
	}
}

func (c *TemplateController) SyncTemplates(ctx fiber.Ctx) (err error) {
	payload := new(payloads.SyncTemplatesRequest)

	if returned, err := httputils.ParseAndValidatePayload(ctx, payload); returned {
		if err != nil {
			slog.ErrorContext(ctx.Context(),
				"Failed to parse and validate payload",
				slog.Any("payload", payload),
				slog.String("error", err.Error()))
		}
		return fmt.Errorf("failed to parse and validate payload: %w", err)
	}

	result, err := c.templateManagerService.SyncTemplates(middlewares.TracingCtx(ctx), payload.Payload)

	if err != nil {
		slog.ErrorContext(ctx.Context(), "Failed to sync templates",
			slog.Any("payload", payload.Payload),
			slog.String("error", err.Error()))
		return c.handleErrors(ctx, err)
	}

	return ctx.JSON(result)
}

func (c *TemplateController) CreateTemplate(ctx fiber.Ctx) (err error) {
	payload := new(payloads.CreateTemplateRequest)

	if returned, err := httputils.ParseAndValidatePayload(ctx, payload); returned {
		if err != nil {
			slog.ErrorContext(ctx.Context(),
				"Failed to parse and validate payload",
				slog.Any("payload", payload),
				slog.String("error", err.Error()))
		}
		return fmt.Errorf("failed to parse and validate payload: %w", err)
	}

	result, err := c.templateManagerService.CreateTemplate(middlewares.TracingCtx(ctx), payload.Payload)

	if err != nil {
		slog.ErrorContext(ctx.Context(), "Failed to create template",
			slog.Any("payload", payload.Payload),
			slog.String("error", err.Error()))
		return c.handleErrors(ctx, err)
	}

	return ctx.JSON(result)
}

func (c *TemplateController) DeleteTemplate(ctx fiber.Ctx) (err error) {
	payload := new(payloads.DeleteTemplateRequest)

	if returned, err := httputils.ParseAndValidatePayload(ctx, payload); returned {
		if err != nil {
			slog.ErrorContext(ctx.Context(),
				"Failed to parse and validate payload",
				slog.Any("payload", payload),
				slog.String("error", err.Error()))
		}
		return fmt.Errorf("failed to parse and validate payload: %w", err)
	}

	result, err := c.templateManagerService.DeleteTemplate(middlewares.TracingCtx(ctx), payload.Payload)

	if err != nil {
		slog.ErrorContext(ctx.Context(), "Failed to delete template",
			slog.Any("payload", payload.Payload),
			slog.String("error", err.Error()))
		return c.handleErrors(ctx, err)
	}

	return ctx.JSON(result)
}

func (c *TemplateController) handleErrors(ctx fiber.Ctx, err error) *fiber.Error {
	if strings.Contains(err.Error(), "service disconnected") {
		slog.ErrorContext(ctx.Context(), "Service disconnected", slog.String("error", err.Error()))
		return fiber.NewError(500, "Service disconnected.")
	}

	slog.ErrorContext(ctx.Context(), "Internal server error", slog.String("error", err.Error()))

	return fiber.NewError(fiber.ErrInternalServerError.Code, fmt.Sprintf("Internal server error: %s", err.Error()))
}
