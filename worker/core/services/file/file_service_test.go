package files

import (
	"bytes"
	"context"
	"image"
	"image/color"
	"image/png"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

func TestResizeImageStream_Simple(t *testing.T) {
	// Cria uma imagem RGB vermelha de 800x600
	img := image.NewRGBA(image.Rect(0, 0, 800, 600))
	for x := 0; x < 800; x++ {
		for y := 0; y < 600; y++ {
			img.Set(x, y, color.RGBA{255, 0, 0, 255})
		}
	}
	buf := new(bytes.Buffer)
	err := png.Encode(buf, img)
	assert.NoError(t, err)

	start := time.Now()

	// Executa o resize
	out, size, err := resizeImageStream(context.Background(), bytes.NewReader(buf.Bytes()), 400, 400)

	duration := time.Since(start)

	t.Logf("Tempo para resize: %v", duration)
	t.Logf("Size: %v", size)

	assert.NoError(t, err)
	assert.NotNil(t, out)
	assert.True(t, size > 0)

	// Decodifica o thumbnail para checar dimensões
	thumbImg, _, err := image.Decode(out)
	assert.NoError(t, err)
	bounds := thumbImg.Bounds()
	assert.Equal(t, 400, bounds.Dx())
	assert.Equal(t, 300, bounds.Dy()) // Mantém proporção
}
