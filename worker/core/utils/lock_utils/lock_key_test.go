//go:build unit

package lock

import (
	"fmt"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestGetContactLockKey(t *testing.T) {
	accountId := uuid.New()
	serviceId := uuid.New()
	contactId := uuid.New()

	testCases := []struct {
		name        string
		accountId   uuid.UUID
		serviceId   uuid.UUID
		contactId   uuid.UUID
		expectedKey string
		expectError bool
		errorMsg    string
	}{
		{
			name:        "should return lock key successfully",
			accountId:   accountId,
			serviceId:   serviceId,
			contactId:   contactId,
			expectedKey: fmt.Sprintf("contact-lock:%s:%s:%s", accountId, serviceId, contactId),
			expectError: false,
		},
		{
			name:        "should return error when accountId is nil",
			accountId:   uuid.Nil,
			serviceId:   serviceId,
			contactId:   contactId,
			expectError: true,
			errorMsg:    "failed to get contact lock key, accountId is required",
		},
		{
			name:        "should return error when serviceId is nil",
			accountId:   accountId,
			serviceId:   uuid.Nil,
			contactId:   contactId,
			expectError: true,
			errorMsg:    "failed to get contact lock key, serviceId is required",
		},
		{
			name:        "should return error when contactId is nil",
			accountId:   accountId,
			serviceId:   serviceId,
			contactId:   uuid.Nil,
			expectError: true,
			errorMsg:    "failed to get contact lock key, contactId is required",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			key, err := GetContactLockKey(tc.accountId, tc.serviceId, tc.contactId)

			if tc.expectError {
				require.Error(t, err)
				assert.Equal(t, tc.errorMsg, err.Error())
				assert.Empty(t, key)
			} else {
				require.NoError(t, err)
				assert.Equal(t, tc.expectedKey, key)
			}
		})
	}
}
