package answer

import (
	"context"
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/repositories"
	"digisac-go/worker/core/services/event"
	"fmt"
	"log/slog"

	gormrepository "github.com/ikateclab/gorm-repository"
)

type AnswerService struct {
	contactRepository repositories.ContactRepository
	answerRepository  repositories.AnswerRepository
	eventService      *event.EventService
}

func NewAnswerService(contactRepository repositories.ContactRepository, answerRepository repositories.AnswerRepository, eventService *event.EventService) *AnswerService {
	return &AnswerService{
		contactRepository: contactRepository,
		answerRepository:  answerRepository,
		eventService:      eventService,
	}
}

func (e *AnswerService) UpdateAnswerForContact(
	ctx context.Context,
	contact *models.Contact,
	answer *models.Answer,
	text string,
	tx *gormrepository.Tx,
) (_ *models.Answer, err error) {
	if answer == nil {
		return e.CreateAnswerForContact(ctx, contact, text, tx)
	}

	if answer.Text != "" && answer.Text != "nv" && answer.Reason == "" {
		answer.Reason = text
	} else {
		answer.Text = text
	}

	err = e.answerRepository.UpdateById(ctx, answer.Id, answer, repositories.WithTx(tx))
	if err != nil {
		slog.InfoContext(ctx, "update answer for contact failed", slog.String("contactId", contact.Id.String()), slog.String("answerId", answer.Id.String()), slog.String("text", text), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to update answer for contact %s: %w", contact.Id, err)
	}

	return answer, nil
}

func (e *AnswerService) CreateAnswerForContact(
	ctx context.Context,
	contact *models.Contact,
	text string,
	tx *gormrepository.Tx,
) (answer *models.Answer, err error) {
	answer, err = e.answerRepository.FindOne(ctx,
		repositories.WithQueryStruct(map[string]interface{}{
			"ticketId": contact.Data.Survey.TicketId,
		}),
		repositories.WithTx(tx),
	)

	if err != nil && err.Error() != "record not found" {
		slog.InfoContext(ctx, "find answer for contact failed", slog.String("contactId", contact.Id.String()), slog.String("ticketId", contact.Data.Survey.TicketId.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to find answer for contact %s with ticketId %s: %w", contact.Id, contact.Data.Survey.TicketId, err)
	}

	if answer == nil {
		answer = &models.Answer{
			QuestionId: contact.Data.Survey.QuestionId,
			TicketId:   contact.Data.Survey.TicketId,
			Text:       text,
			Reason:     "",
		}
		err = e.answerRepository.Create(ctx,
			answer,
			repositories.WithTx(tx),
		)
		if err != nil {
			slog.InfoContext(ctx, "create answer for contact failed", slog.String("contactId", contact.Id.String()), slog.String("ticketId", contact.Data.Survey.TicketId.String()), slog.String("text", text), slog.String("error", err.Error()))
			return nil, fmt.Errorf("failed to create answer for contact %s with ticketId %s: %w", contact.Id, contact.Data.Survey.TicketId, err)
		}
	}
	return answer, nil
}

func (e *AnswerService) UnflagSurveyFromContact(ctx context.Context, contact *models.Contact, tx *gormrepository.Tx, etx *event.Etx) (err error) {
	contact.Data.Survey = nil

	err = e.contactRepository.UpdateById(ctx, contact.Id, contact, repositories.WithTx(tx))

	if err != nil {
		slog.InfoContext(ctx, "unflag survey from contact failed", slog.String("contactId", contact.Id.String()), slog.String("error", err.Error()))
		return fmt.Errorf("failed to unflag survey from contact %s: %w", contact.Id, err)
	}

	err = e.eventService.Dispatch(
		ctx,
		event.EventContactUpdated,
		&event.DispatchPayload{
			Id:        contact.Id,
			AccountId: contact.AccountId,
		},
		&event.EventServiceDispatchOptions{
			DebounceKey: contact.Id.String(),
			Etx:         etx,
		})

	if err != nil {
		slog.ErrorContext(ctx, "Failed to dispatch contact updated event", slog.String("error", err.Error()), slog.Any("contact", contact))
		return fmt.Errorf("failed to dispatch contact updated event: %w", err)
	}

	return nil
}
