package payloads

import "github.com/google/uuid"

type StartServicePayload struct {
	ServiceId uuid.UUID `json:"serviceId,omitempty"`
}

type StartServiceRequest struct {
	Payload  *StartServicePayload `json:"payload,omitempty"`
	Metadata *Metadata
}

type RestartServicePayload struct {
	ServiceId uuid.UUID `json:"serviceId,omitempty"`
}

type RestartServiceRequest struct {
	Payload  *RestartServicePayload `json:"payload,omitempty"`
	Metadata *Metadata
}

type ShutdownServicePayload struct {
	ServiceId uuid.UUID `json:"serviceId,omitempty"`
}

type ShutdownServiceRequest struct {
	Payload  *ShutdownServicePayload `json:"payload,omitempty"`
	Metadata *Metadata
}

type LogoutServicePayload struct {
	ServiceId uuid.UUID `json:"serviceId,omitempty"`
}

type LogoutServiceRequest struct {
	Payload  *LogoutServicePayload `json:"payload,omitempty"`
	Metadata *Metadata
}

type TakeoverServicePayload struct {
	ServiceId uuid.UUID `json:"serviceId,omitempty"`
}

type TakeoverServiceRequest struct {
	Payload  *TakeoverServicePayload `json:"payload,omitempty"`
	Metadata *Metadata
}
