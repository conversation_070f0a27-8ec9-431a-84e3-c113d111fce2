//go:build unit

package whatsapp_adapter

import (
	"context"
	common "digisac-go/common/grpc/common"
	"digisac-go/worker/core/adapters/adapter_types"
	"digisac-go/worker/core/http/payloads"
	"digisac-go/worker/core/models"
	"fmt"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"github.com/stretchr/testify/suite"
)

/*
 * Test function: SetWebhook
 */
func TestWhatsappAdapterSetWebhook(t *testing.T) {
	whatsappAdapter := NewWhatsappAdapter(nil, nil, nil)
	err := whatsappAdapter.SetWebhook(context.Background(), uuid.New(), "url")

	require.NoError(t, err)
}

/*
 * Test function: Refresh
 */
func TestWhatsappAdapterRefresh(t *testing.T) {
	whatsappAdapter := NewWhatsappAdapter(nil, nil, nil)
	err := whatsappAdapter.Refresh(context.Background(), uuid.New())

	require.NoError(t, err)
}

/*
 * Test function: NewToken
 */
func TestWhatsappAdapterNewToken(t *testing.T) {
	whatsappAdapter := NewWhatsappAdapter(nil, nil, nil)
	err := whatsappAdapter.NewToken(context.Background(), uuid.New())

	require.NoError(t, err)
}

/*
 * Test function: Takeover
 */
func TestWhatsappAdapterTakeover(t *testing.T) {
	whatsappAdapter := NewWhatsappAdapter(nil, nil, nil)
	err := whatsappAdapter.Takeover(context.Background(), uuid.New())

	require.NoError(t, err)
}

/*
 * Test function: Logout
 */
type WhatsappAdapterLogoutTestSuite struct {
	suite.Suite
}

func (suite *WhatsappAdapterLogoutTestSuite) TestErrorServiceNotFound() {
	ctx := context.Background()
	serviceId := uuid.New()

	mockServiceRepository := &MockServiceRepository{}
	mockServiceRepository.On("FindById", ctx, serviceId).Return(nil, fmt.Errorf("service not found"))

	mockGrpcWppClient := &MockGrpcWppClient{}

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)

	err := adapter.Logout(ctx, serviceId)

	suite.Require().EqualError(err, "failed to find service: service not found")

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterLogoutTestSuite) TestErrorLogout() {
	ctx := context.Background()
	serviceId := uuid.New()
	driverId := "driver-id"

	mockServiceRepository := &MockServiceRepository{}
	mockServiceRepository.On("FindById", ctx, serviceId).Return(&models.Service{Data: &models.ServiceData{DriverId: driverId}}, nil)

	mockGrpcWppClient := &MockGrpcWppClient{}
	mockGrpcWppClient.On("Logout", ctx, mock.MatchedBy(func(req *common.ActionPayload) bool {
		return req.ServiceId == driverId
	})).Return(nil, fmt.Errorf("failed to logout"))

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)
	err := adapter.Logout(ctx, serviceId)

	suite.Require().EqualError(err, "failed to logout whatsapp: failed to logout")

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterLogoutTestSuite) TestValidLogout() {
	ctx := context.Background()
	serviceId := uuid.New()
	driverId := "driver-id"

	mockServiceRepository := &MockServiceRepository{}
	mockServiceRepository.On("FindById", ctx, serviceId).Return(&models.Service{Data: &models.ServiceData{DriverId: driverId}}, nil)

	mockGrpcWppClient := &MockGrpcWppClient{}
	mockGrpcWppClient.On("Logout", ctx, mock.MatchedBy(func(req *common.ActionPayload) bool {
		return req.ServiceId == driverId
	})).Return(&common.ActionResponse{}, nil)

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)
	err := adapter.Logout(ctx, serviceId)

	suite.Require().NoError(err)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func TestWhatsappAdapterLogoutTestSuite(t *testing.T) {
	suite.Run(t, new(WhatsappAdapterLogoutTestSuite))
}

/*
 * Test function: Shutdown
 */
type WhatsappAdapterShutdownTestSuite struct {
	suite.Suite
}

func (suite *WhatsappAdapterShutdownTestSuite) TestErrorServiceNotFound() {
	ctx := context.Background()
	serviceId := uuid.New()

	mockServiceRepository := &MockServiceRepository{}
	mockServiceRepository.On("FindById", ctx, serviceId).Return(nil, fmt.Errorf("service not found"))

	mockGrpcWppClient := &MockGrpcWppClient{}

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)

	err := adapter.Shutdown(ctx, serviceId)

	suite.Require().EqualError(err, "failed to find service: service not found")

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterShutdownTestSuite) TestErrorShutdown() {
	ctx := context.Background()
	serviceId := uuid.New()
	driverId := "driver-id"

	mockServiceRepository := &MockServiceRepository{}
	mockServiceRepository.On("FindById", ctx, serviceId).Return(&models.Service{Data: &models.ServiceData{DriverId: driverId}}, nil)

	mockGrpcWppClient := &MockGrpcWppClient{}
	mockGrpcWppClient.On("Stop", ctx, mock.MatchedBy(func(req *common.ActionPayload) bool {
		return req.ServiceId == driverId
	})).Return(nil, fmt.Errorf("failed to shutdown"))

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)
	err := adapter.Shutdown(ctx, serviceId)

	suite.Require().EqualError(err, "failed to stop whatsapp: failed to shutdown")

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterShutdownTestSuite) TestValidShutdown() {
	ctx := context.Background()
	serviceId := uuid.New()
	driverId := "driver-id"

	mockServiceRepository := &MockServiceRepository{}
	mockServiceRepository.On("FindById", ctx, serviceId).Return(&models.Service{Data: &models.ServiceData{DriverId: driverId}}, nil)

	mockGrpcWppClient := &MockGrpcWppClient{}
	mockGrpcWppClient.On("Stop", ctx, mock.MatchedBy(func(req *common.ActionPayload) bool {
		return req.ServiceId == driverId
	})).Return(&common.ActionResponse{}, nil)

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)
	err := adapter.Shutdown(ctx, serviceId)

	suite.Require().NoError(err)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func TestWhatsappAdapterShutdownTestSuite(t *testing.T) {
	suite.Run(t, new(WhatsappAdapterShutdownTestSuite))
}

/*
 * Test function: SendSticker
 */
type WhatsappAdapterSendStickerTestSuite struct {
	suite.Suite
}

func (suite *WhatsappAdapterSendStickerTestSuite) TestErrorEmptyPayloadTo() {
	ctx := context.Background()
	serviceId := uuid.New()

	payload := &adapter_types.SendStickerPayload{
		To:  "",
		Url: "https://digisac.com.br/123.jpeg",
	}

	mockServiceRepository := &MockServiceRepository{}

	mockGrpcWppClient := &MockGrpcWppClient{}

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)

	result, err := adapter.SendSticker(ctx, serviceId, payload)

	suite.Require().EqualError(err, "payload.To () and payload.Url (https://digisac.com.br/123.jpeg) cannot be empty")
	suite.Require().Nil(result)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterSendStickerTestSuite) TestErrorEmptyPayloadUrl() {
	ctx := context.Background()
	serviceId := uuid.New()

	payload := &adapter_types.SendStickerPayload{
		To:  "chat-id",
		Url: "",
	}

	mockServiceRepository := &MockServiceRepository{}

	mockGrpcWppClient := &MockGrpcWppClient{}

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)

	result, err := adapter.SendSticker(ctx, serviceId, payload)

	suite.Require().EqualError(err, "payload.To (chat-id) and payload.Url () cannot be empty")
	suite.Require().Nil(result)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterSendStickerTestSuite) TestErrorServiceNotFound() {
	ctx := context.Background()
	serviceId := uuid.New()

	payload := &adapter_types.SendStickerPayload{
		To:  "chat-id",
		Url: "https://digisac.com.br/123.jpeg",
	}

	mockServiceRepository := &MockServiceRepository{}
	mockServiceRepository.On("FindById", ctx, serviceId).Return(nil, fmt.Errorf("service not found"))

	mockGrpcWppClient := &MockGrpcWppClient{}

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)

	result, err := adapter.SendSticker(ctx, serviceId, payload)

	suite.Require().EqualError(err, "failed to find service: service not found")
	suite.Require().Nil(result)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterSendStickerTestSuite) TestErrorSendMessage() {
	ctx := context.Background()
	serviceId := uuid.New()
	driverId := "driver-id"

	payload := &adapter_types.SendStickerPayload{
		To:             "chat-id",
		ReplyMessageId: "message-id",
		Url:            "https://digisac.com.br/123.jpeg",
	}

	mockServiceRepository := &MockServiceRepository{}
	mockServiceRepository.On("FindById", ctx, serviceId).Return(&models.Service{Data: &models.ServiceData{DriverId: driverId}}, nil)

	mockGrpcWppClient := &MockGrpcWppClient{}
	mockGrpcWppClient.On("SendMessage", ctx, mock.MatchedBy(func(req *common.SendMessageRequest) bool {
		return req.Payload.ChatId == payload.To &&
			req.Payload.QuotedMessageId == payload.ReplyMessageId &&
			req.Payload.File.Url == payload.Url &&
			req.Payload.File.AsSticker == true &&
			req.Metadata.ServiceId == driverId
	})).Return(nil, fmt.Errorf("failed to send message"))

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)
	result, err := adapter.SendSticker(ctx, serviceId, payload)

	suite.Require().NoError(err)
	suite.Require().Equal("failed to send message", result.Error.OriginalError)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterSendStickerTestSuite) TestValidSendSticker() {
	startDate := time.Now()
	ctx := context.Background()
	serviceId := uuid.New()
	driverId := "driver-id"
	respondeId := "responde-id"

	payload := &adapter_types.SendStickerPayload{
		To:             "chat-id",
		ReplyMessageId: "message-id",
		Url:            "https://digisac.com.br/123.jpeg",
	}

	mockServiceRepository := &MockServiceRepository{}
	mockServiceRepository.On("FindById", ctx, serviceId).Return(&models.Service{Data: &models.ServiceData{DriverId: driverId}}, nil)

	mockGrpcWppClient := &MockGrpcWppClient{}
	mockGrpcWppClient.On("SendMessage", ctx, mock.MatchedBy(func(req *common.SendMessageRequest) bool {
		return req.Payload.ChatId == payload.To &&
			req.Payload.QuotedMessageId == payload.ReplyMessageId &&
			req.Payload.File.Url == payload.Url &&
			req.Payload.File.AsSticker == true &&
			req.Metadata.ServiceId == driverId
	})).Return(&common.SendMessageResponse{Id: respondeId}, nil)

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)
	result, err := adapter.SendSticker(ctx, serviceId, payload)

	suite.Require().NoError(err)
	suite.Require().Equal(respondeId, result.MessageId)
	suite.Require().WithinDuration(startDate, *result.Timestamp, 5*time.Second)
	suite.Require().Equal("0", result.Ack)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func TestWhatsappAdapterSendStickerTestSuite(t *testing.T) {
	suite.Run(t, new(WhatsappAdapterSendStickerTestSuite))
}

/*
 * Test function: SendReaction
 */
type WhatsappAdapterSendReactionTestSuite struct {
	suite.Suite
}

func (suite *WhatsappAdapterSendReactionTestSuite) TestErrorEmptyPayloadTo() {
	ctx := context.Background()
	serviceId := uuid.New()

	payload := &adapter_types.SendReactionPayload{
		To:        "",
		MessageId: "message-id",
		Reaction:  "reaction",
	}

	mockServiceRepository := &MockServiceRepository{}

	mockGrpcWppClient := &MockGrpcWppClient{}

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)

	result, err := adapter.SendReaction(ctx, serviceId, payload)

	suite.Require().EqualError(err, "payload.To (), payload.MessageId (message-id) and payload.Reaction (reaction) cannot be empty")
	suite.Require().False(result)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterSendReactionTestSuite) TestErrorEmptyPayloadMessageId() {
	ctx := context.Background()
	serviceId := uuid.New()

	payload := &adapter_types.SendReactionPayload{
		To:        "chat-id",
		MessageId: "",
		Reaction:  "reaction",
	}

	mockServiceRepository := &MockServiceRepository{}

	mockGrpcWppClient := &MockGrpcWppClient{}

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)

	result, err := adapter.SendReaction(ctx, serviceId, payload)

	suite.Require().EqualError(err, "payload.To (chat-id), payload.MessageId () and payload.Reaction (reaction) cannot be empty")
	suite.Require().False(result)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterSendReactionTestSuite) TestErrorEmptyPayloadReaction() {
	ctx := context.Background()
	serviceId := uuid.New()

	payload := &adapter_types.SendReactionPayload{
		To:        "chat-id",
		MessageId: "message-id",
		Reaction:  "",
	}

	mockServiceRepository := &MockServiceRepository{}

	mockGrpcWppClient := &MockGrpcWppClient{}

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)

	result, err := adapter.SendReaction(ctx, serviceId, payload)

	suite.Require().EqualError(err, "payload.To (chat-id), payload.MessageId (message-id) and payload.Reaction () cannot be empty")
	suite.Require().False(result)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterSendReactionTestSuite) TestErrorServiceNotFound() {
	ctx := context.Background()
	serviceId := uuid.New()

	payload := &adapter_types.SendReactionPayload{
		To:        "chat-id",
		MessageId: "message-id",
		Reaction:  "reaction",
	}

	mockServiceRepository := &MockServiceRepository{}
	mockServiceRepository.On("FindById", ctx, serviceId).Return(nil, fmt.Errorf("service not found"))

	mockGrpcWppClient := &MockGrpcWppClient{}

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)

	result, err := adapter.SendReaction(ctx, serviceId, payload)

	suite.Require().EqualError(err, "failed to find service: service not found")
	suite.Require().False(result)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterSendReactionTestSuite) TestErrorSendReactionToMessage() {
	ctx := context.Background()
	serviceId := uuid.New()
	driverId := "driver-id"

	payload := &adapter_types.SendReactionPayload{
		To:        "chat-id",
		MessageId: "message-id",
		Reaction:  "reaction",
	}

	mockServiceRepository := &MockServiceRepository{}
	mockServiceRepository.On("FindById", ctx, serviceId).Return(&models.Service{Data: &models.ServiceData{DriverId: driverId}}, nil)

	mockGrpcWppClient := &MockGrpcWppClient{}
	mockGrpcWppClient.On("SendReactionToMessage", ctx, mock.MatchedBy(func(req *common.SendReactionToMessageRequest) bool {
		return req.Payload.ChatId == payload.To &&
			req.Payload.MessageId == payload.MessageId &&
			req.Payload.Reaction == payload.Reaction &&
			req.Metadata.ServiceId == driverId
	})).Return(nil, fmt.Errorf("failed to send reaction"))

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)
	result, err := adapter.SendReaction(ctx, serviceId, payload)

	suite.Require().EqualError(err, "failed to send reaction to message: failed to send reaction")
	suite.Require().False(result)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterSendReactionTestSuite) TestValidSendReaction() {
	ctx := context.Background()
	serviceId := uuid.New()
	driverId := "driver-id"

	payload := &adapter_types.SendReactionPayload{
		To:        "chat-id",
		MessageId: "message-id",
		Reaction:  "reaction",
	}

	mockServiceRepository := &MockServiceRepository{}
	mockServiceRepository.On("FindById", ctx, serviceId).Return(&models.Service{Data: &models.ServiceData{DriverId: driverId}}, nil)

	mockGrpcWppClient := &MockGrpcWppClient{}
	mockGrpcWppClient.On("SendReactionToMessage", ctx, mock.MatchedBy(func(req *common.SendReactionToMessageRequest) bool {
		return req.Payload.ChatId == payload.To &&
			req.Payload.MessageId == payload.MessageId &&
			req.Payload.Reaction == payload.Reaction &&
			req.Metadata.ServiceId == driverId
	})).Return(&common.SendReactionToMessageResponse{}, nil)

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)
	result, err := adapter.SendReaction(ctx, serviceId, payload)

	suite.Require().NoError(err)
	suite.Require().Equal(true, result)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func TestWhatsappAdapterSendReactionTestSuite(t *testing.T) {
	suite.Run(t, new(WhatsappAdapterSendReactionTestSuite))
}

/*
 * Test function: RevokeReaction
 */
type WhatsappAdapterRevokeReactionTestSuite struct {
	suite.Suite
}

func (suite *WhatsappAdapterRevokeReactionTestSuite) TestErrorEmptyPayloadTo() {
	ctx := context.Background()
	serviceId := uuid.New()

	payload := &adapter_types.RevokeReactionPayload{
		To:        "",
		MessageId: "message-id",
	}

	mockServiceRepository := &MockServiceRepository{}

	mockGrpcWppClient := &MockGrpcWppClient{}

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)

	result, err := adapter.RevokeReaction(ctx, serviceId, payload)

	suite.Require().EqualError(err, "payload.To () and payload.MessageId (message-id) cannot be empty")
	suite.Require().False(result)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterRevokeReactionTestSuite) TestErrorEmptyPayloadMessageId() {
	ctx := context.Background()
	serviceId := uuid.New()

	payload := &adapter_types.RevokeReactionPayload{
		To:        "chat-id",
		MessageId: "",
	}

	mockServiceRepository := &MockServiceRepository{}

	mockGrpcWppClient := &MockGrpcWppClient{}

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)

	result, err := adapter.RevokeReaction(ctx, serviceId, payload)

	suite.Require().EqualError(err, "payload.To (chat-id) and payload.MessageId () cannot be empty")
	suite.Require().False(result)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterRevokeReactionTestSuite) TestErrorServiceNotFound() {
	ctx := context.Background()
	serviceId := uuid.New()

	payload := &adapter_types.RevokeReactionPayload{
		To:        "chat-id",
		MessageId: "message-id",
	}

	mockServiceRepository := &MockServiceRepository{}
	mockServiceRepository.On("FindById", ctx, serviceId).Return(nil, fmt.Errorf("service not found"))

	mockGrpcWppClient := &MockGrpcWppClient{}

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)

	result, err := adapter.RevokeReaction(ctx, serviceId, payload)

	suite.Require().EqualError(err, "failed to find service: service not found")
	suite.Require().False(result)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterRevokeReactionTestSuite) TestErrorRevokeReactionToMessage() {
	ctx := context.Background()
	serviceId := uuid.New()
	driverId := "driver-id"

	payload := &adapter_types.RevokeReactionPayload{
		To:        "chat-id",
		MessageId: "message-id",
	}

	mockServiceRepository := &MockServiceRepository{}
	mockServiceRepository.On("FindById", ctx, serviceId).Return(&models.Service{Data: &models.ServiceData{DriverId: driverId}}, nil)

	mockGrpcWppClient := &MockGrpcWppClient{}
	mockGrpcWppClient.On("SendReactionToMessage", ctx, mock.MatchedBy(func(req *common.SendReactionToMessageRequest) bool {
		return req.Payload.ChatId == payload.To &&
			req.Payload.MessageId == payload.MessageId &&
			req.Payload.Reaction == "" &&
			req.Metadata.ServiceId == driverId
	})).Return(nil, fmt.Errorf("failed to revoke reaction"))

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)
	result, err := adapter.RevokeReaction(ctx, serviceId, payload)

	suite.Require().EqualError(err, "failed to revoke reaction from message: failed to revoke reaction")
	suite.Require().False(result)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterRevokeReactionTestSuite) TestValidRevokeReaction() {
	ctx := context.Background()
	serviceId := uuid.New()
	driverId := "driver-id"

	payload := &adapter_types.RevokeReactionPayload{
		To:        "chat-id",
		MessageId: "message-id",
	}

	mockServiceRepository := &MockServiceRepository{}
	mockServiceRepository.On("FindById", ctx, serviceId).Return(&models.Service{Data: &models.ServiceData{DriverId: driverId}}, nil)

	mockGrpcWppClient := &MockGrpcWppClient{}
	mockGrpcWppClient.On("SendReactionToMessage", ctx, mock.MatchedBy(func(req *common.SendReactionToMessageRequest) bool {
		return req.Payload.ChatId == payload.To &&
			req.Payload.MessageId == payload.MessageId &&
			req.Payload.Reaction == "" &&
			req.Metadata.ServiceId == driverId
	})).Return(&common.SendReactionToMessageResponse{}, nil)

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)
	result, err := adapter.RevokeReaction(ctx, serviceId, payload)

	suite.Require().NoError(err)
	suite.Require().Equal(true, result)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func TestWhatsappAdapterRevokeReactionTestSuite(t *testing.T) {
	suite.Run(t, new(WhatsappAdapterRevokeReactionTestSuite))
}

/*
 * Test function: SendText
 */
type WhatsappAdapterSendTextTestSuite struct {
	suite.Suite
}

func (suite *WhatsappAdapterSendTextTestSuite) TestErrorEmptyPayloadTo() {
	ctx := context.Background()
	serviceId := uuid.New()

	payload := &adapter_types.SendTextPayload{
		To:   "",
		Text: "text",
	}

	mockServiceRepository := &MockServiceRepository{}

	mockGrpcWppClient := &MockGrpcWppClient{}

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)

	result, err := adapter.SendText(ctx, serviceId, payload)

	suite.Require().EqualError(err, "payload.To () and payload.Text (text) cannot be empty")
	suite.Require().Nil(result)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterSendTextTestSuite) TestErrorEmptyPayloadText() {
	ctx := context.Background()
	serviceId := uuid.New()

	payload := &adapter_types.SendTextPayload{
		To:   "chat-id",
		Text: "",
	}

	mockServiceRepository := &MockServiceRepository{}

	mockGrpcWppClient := &MockGrpcWppClient{}

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)

	result, err := adapter.SendText(ctx, serviceId, payload)

	suite.Require().EqualError(err, "payload.To (chat-id) and payload.Text () cannot be empty")
	suite.Require().Nil(result)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterSendTextTestSuite) TestErrorServiceNotFound() {
	ctx := context.Background()
	serviceId := uuid.New()

	payload := &adapter_types.SendTextPayload{
		To:   "chat-id",
		Text: "text",
	}

	mockServiceRepository := &MockServiceRepository{}
	mockServiceRepository.On("FindById", ctx, serviceId).Return(nil, fmt.Errorf("service not found"))

	mockGrpcWppClient := &MockGrpcWppClient{}

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)

	result, err := adapter.SendText(ctx, serviceId, payload)

	suite.Require().EqualError(err, "failed to find service: service not found")
	suite.Require().Nil(result)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterSendTextTestSuite) TestErrorSendMessage() {
	ctx := context.Background()
	serviceId := uuid.New()
	driverId := "driver-id"

	payload := &adapter_types.SendTextPayload{
		To:             "chat-id",
		ReplyMessageId: "message-id",
		Text:           "text",
	}

	mockServiceRepository := &MockServiceRepository{}
	mockServiceRepository.On("FindById", ctx, serviceId).Return(&models.Service{Data: &models.ServiceData{DriverId: driverId}}, nil)

	mockGrpcWppClient := &MockGrpcWppClient{}
	mockGrpcWppClient.On("SendMessage", ctx, mock.MatchedBy(func(req *common.SendMessageRequest) bool {
		return req.Payload.ChatId == payload.To &&
			req.Payload.QuotedMessageId == payload.ReplyMessageId &&
			req.Payload.Text == payload.Text &&
			req.Metadata.ServiceId == driverId
	})).Return(nil, fmt.Errorf("failed to send message"))

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)
	result, err := adapter.SendText(ctx, serviceId, payload)

	suite.Require().NoError(err)
	suite.Require().Equal("failed to send message", result.Error.OriginalError)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterSendTextTestSuite) TestValidSendText() {
	startDate := time.Now()
	ctx := context.Background()
	serviceId := uuid.New()
	driverId := "driver-id"
	respondeId := "responde-id"

	payload := &adapter_types.SendTextPayload{
		To:             "chat-id",
		ReplyMessageId: "message-id",
		Text:           "text",
	}

	mockServiceRepository := &MockServiceRepository{}
	mockServiceRepository.On("FindById", ctx, serviceId).Return(&models.Service{Data: &models.ServiceData{DriverId: driverId}}, nil)

	mockGrpcWppClient := &MockGrpcWppClient{}
	mockGrpcWppClient.On("SendMessage", ctx, mock.MatchedBy(func(req *common.SendMessageRequest) bool {
		return req.Payload.ChatId == payload.To &&
			req.Payload.QuotedMessageId == payload.ReplyMessageId &&
			req.Payload.Text == payload.Text &&
			req.Metadata.ServiceId == driverId
	})).Return(&common.SendMessageResponse{Id: respondeId}, nil)

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)
	result, err := adapter.SendText(ctx, serviceId, payload)

	suite.Require().NoError(err)
	suite.Require().Equal(respondeId, result.MessageId)
	suite.Require().WithinDuration(startDate, *result.Timestamp, 5*time.Second)
	suite.Require().Equal("0", result.Ack)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func TestWhatsappAdapterSendTextTestSuite(t *testing.T) {
	suite.Run(t, new(WhatsappAdapterSendTextTestSuite))
}

/*
 * Test function: SendAudio
 */
type WhatsappAdapterSendAudioTestSuite struct {
	suite.Suite
}

func (suite *WhatsappAdapterSendAudioTestSuite) TestErrorEmptyPayloadTo() {
	ctx := context.Background()
	serviceId := uuid.New()

	payload := &adapter_types.SendAudioPayload{
		To:  "",
		Url: "https://digisac.com.br/123.mp3",
	}

	mockServiceRepository := &MockServiceRepository{}

	mockGrpcWppClient := &MockGrpcWppClient{}

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)

	result, err := adapter.SendAudio(ctx, serviceId, payload)

	suite.Require().EqualError(err, "payload.To () and payload.Url (https://digisac.com.br/123.mp3) cannot be empty")
	suite.Require().Nil(result)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterSendAudioTestSuite) TestErrorEmptyPayloadUrl() {
	ctx := context.Background()
	serviceId := uuid.New()

	payload := &adapter_types.SendAudioPayload{
		To:  "chat-id",
		Url: "",
	}

	mockServiceRepository := &MockServiceRepository{}

	mockGrpcWppClient := &MockGrpcWppClient{}

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)

	result, err := adapter.SendAudio(ctx, serviceId, payload)

	suite.Require().EqualError(err, "payload.To (chat-id) and payload.Url () cannot be empty")
	suite.Require().Nil(result)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterSendAudioTestSuite) TestErrorServiceNotFound() {
	ctx := context.Background()
	serviceId := uuid.New()

	payload := &adapter_types.SendAudioPayload{
		To:  "chat-id",
		Url: "https://digisac.com.br/123.mp3",
	}

	mockServiceRepository := &MockServiceRepository{}
	mockServiceRepository.On("FindById", ctx, serviceId).Return(nil, fmt.Errorf("service not found"))

	mockGrpcWppClient := &MockGrpcWppClient{}

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)

	result, err := adapter.SendAudio(ctx, serviceId, payload)

	suite.Require().EqualError(err, "failed to find service: service not found")
	suite.Require().Nil(result)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterSendAudioTestSuite) TestErrorSendMessage() {
	ctx := context.Background()
	serviceId := uuid.New()
	driverId := "driver-id"

	payload := &adapter_types.SendAudioPayload{
		To:             "chat-id",
		ReplyMessageId: "message-id",
		Caption:        "text",
		Mimetype:       "audio/mp3",
		Url:            "https://digisac.com.br/123.mp3",
	}

	mockServiceRepository := &MockServiceRepository{}
	mockServiceRepository.On("FindById", ctx, serviceId).Return(&models.Service{Data: &models.ServiceData{DriverId: driverId}}, nil)

	mockGrpcWppClient := &MockGrpcWppClient{}
	mockGrpcWppClient.On("SendMessage", ctx, mock.MatchedBy(func(req *common.SendMessageRequest) bool {
		return req.Payload.ChatId == payload.To &&
			req.Payload.Text == payload.Caption &&
			req.Payload.QuotedMessageId == payload.ReplyMessageId &&
			req.Payload.File.Url == payload.Url &&
			req.Payload.File.Mimetype == payload.Mimetype &&
			req.Payload.File.AsPtt == true &&
			req.Metadata.ServiceId == driverId
	})).Return(nil, fmt.Errorf("failed to send message"))

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)
	result, err := adapter.SendAudio(ctx, serviceId, payload)

	suite.Require().NoError(err)
	suite.Require().Equal("failed to send message", result.Error.OriginalError)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterSendAudioTestSuite) TestValidSendAudio() {
	startDate := time.Now()
	ctx := context.Background()
	serviceId := uuid.New()
	driverId := "driver-id"
	respondeId := "responde-id"

	payload := &adapter_types.SendAudioPayload{
		To:             "chat-id",
		ReplyMessageId: "message-id",
		Caption:        "text",
		Mimetype:       "audio/mp3",
		Url:            "https://digisac.com.br/123.mp3",
	}

	mockServiceRepository := &MockServiceRepository{}
	mockServiceRepository.On("FindById", ctx, serviceId).Return(&models.Service{Data: &models.ServiceData{DriverId: driverId}}, nil)

	mockGrpcWppClient := &MockGrpcWppClient{}
	mockGrpcWppClient.On("SendMessage", ctx, mock.MatchedBy(func(req *common.SendMessageRequest) bool {
		return req.Payload.ChatId == payload.To &&
			req.Payload.Text == payload.Caption &&
			req.Payload.QuotedMessageId == payload.ReplyMessageId &&
			req.Payload.File.Url == payload.Url &&
			req.Payload.File.Mimetype == payload.Mimetype &&
			req.Payload.File.AsPtt == true &&
			req.Metadata.ServiceId == driverId
	})).Return(&common.SendMessageResponse{Id: respondeId}, nil)

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)
	result, err := adapter.SendAudio(ctx, serviceId, payload)

	suite.Require().NoError(err)
	suite.Require().Equal(respondeId, result.MessageId)
	suite.Require().WithinDuration(startDate, *result.Timestamp, 5*time.Second)
	suite.Require().Equal("0", result.Ack)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func TestWhatsappAdapterSendAudioTestSuite(t *testing.T) {
	suite.Run(t, new(WhatsappAdapterSendAudioTestSuite))
}

/*
 * Test function: SendImage
 */
type WhatsappAdapterSendImageTestSuite struct {
	suite.Suite
}

func (suite *WhatsappAdapterSendImageTestSuite) TestErrorEmptyPayloadTo() {
	ctx := context.Background()
	serviceId := uuid.New()

	payload := &adapter_types.SendImagePayload{
		To:  "",
		Url: "https://digisac.com.br/123.jpeg",
	}

	mockServiceRepository := &MockServiceRepository{}

	mockGrpcWppClient := &MockGrpcWppClient{}

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)

	result, err := adapter.SendImage(ctx, serviceId, payload)

	suite.Require().EqualError(err, "payload.To () and payload.Url (https://digisac.com.br/123.jpeg) cannot be empty")
	suite.Require().Nil(result)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterSendImageTestSuite) TestErrorEmptyPayloadUrl() {
	ctx := context.Background()
	serviceId := uuid.New()

	payload := &adapter_types.SendImagePayload{
		To:  "chat-id",
		Url: "",
	}

	mockServiceRepository := &MockServiceRepository{}

	mockGrpcWppClient := &MockGrpcWppClient{}

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)

	result, err := adapter.SendImage(ctx, serviceId, payload)

	suite.Require().EqualError(err, "payload.To (chat-id) and payload.Url () cannot be empty")
	suite.Require().Nil(result)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterSendImageTestSuite) TestErrorServiceNotFound() {
	ctx := context.Background()
	serviceId := uuid.New()

	payload := &adapter_types.SendImagePayload{
		To:  "chat-id",
		Url: "https://digisac.com.br/123.jpeg",
	}

	mockServiceRepository := &MockServiceRepository{}
	mockServiceRepository.On("FindById", ctx, serviceId).Return(nil, fmt.Errorf("service not found"))

	mockGrpcWppClient := &MockGrpcWppClient{}

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)

	result, err := adapter.SendImage(ctx, serviceId, payload)

	suite.Require().EqualError(err, "failed to find service: service not found")
	suite.Require().Nil(result)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterSendImageTestSuite) TestErrorSendMessage() {
	ctx := context.Background()
	serviceId := uuid.New()
	driverId := "driver-id"

	payload := &adapter_types.SendImagePayload{
		To:             "chat-id",
		ReplyMessageId: "message-id",
		Caption:        "text",
		Mimetype:       "image/jpeg",
		Filename:       "123.jpeg",
		Url:            "https://digisac.com.br/123.jpeg",
	}

	mockServiceRepository := &MockServiceRepository{}
	mockServiceRepository.On("FindById", ctx, serviceId).Return(&models.Service{Data: &models.ServiceData{DriverId: driverId}}, nil)

	mockGrpcWppClient := &MockGrpcWppClient{}
	mockGrpcWppClient.On("SendMessage", ctx, mock.MatchedBy(func(req *common.SendMessageRequest) bool {
		return req.Payload.ChatId == payload.To &&
			req.Payload.Text == payload.Caption &&
			req.Payload.QuotedMessageId == payload.ReplyMessageId &&
			req.Payload.File.Url == payload.Url &&
			req.Payload.File.Mimetype == payload.Mimetype &&
			req.Payload.File.Name == payload.Filename &&
			req.Metadata.ServiceId == driverId
	})).Return(nil, fmt.Errorf("failed to send message"))

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)
	result, err := adapter.SendImage(ctx, serviceId, payload)

	suite.Require().NoError(err)
	suite.Require().Equal("failed to send message", result.Error.OriginalError)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterSendImageTestSuite) TestValidSendImage() {
	startDate := time.Now()
	ctx := context.Background()
	serviceId := uuid.New()
	driverId := "driver-id"
	respondeId := "responde-id"

	payload := &adapter_types.SendImagePayload{
		To:             "chat-id",
		ReplyMessageId: "message-id",
		Caption:        "text",
		Mimetype:       "image/jpeg",
		Filename:       "123.jpeg",
		Url:            "https://digisac.com.br/123.jpeg",
	}

	mockServiceRepository := &MockServiceRepository{}
	mockServiceRepository.On("FindById", ctx, serviceId).Return(&models.Service{Data: &models.ServiceData{DriverId: driverId}}, nil)

	mockGrpcWppClient := &MockGrpcWppClient{}
	mockGrpcWppClient.On("SendMessage", ctx, mock.MatchedBy(func(req *common.SendMessageRequest) bool {
		return req.Payload.ChatId == payload.To &&
			req.Payload.Text == payload.Caption &&
			req.Payload.QuotedMessageId == payload.ReplyMessageId &&
			req.Payload.File.Url == payload.Url &&
			req.Payload.File.Mimetype == payload.Mimetype &&
			req.Payload.File.Name == payload.Filename &&
			req.Metadata.ServiceId == driverId
	})).Return(&common.SendMessageResponse{Id: respondeId}, nil)

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)
	result, err := adapter.SendImage(ctx, serviceId, payload)

	suite.Require().NoError(err)
	suite.Require().Equal(respondeId, result.MessageId)
	suite.Require().WithinDuration(startDate, *result.Timestamp, 5*time.Second)
	suite.Require().Equal("0", result.Ack)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func TestWhatsappAdapterSendImageTestSuite(t *testing.T) {
	suite.Run(t, new(WhatsappAdapterSendImageTestSuite))
}

/*
 * Test function: SendVideo
 */
type WhatsappAdapterSendVideoTestSuite struct {
	suite.Suite
}

func (suite *WhatsappAdapterSendVideoTestSuite) TestErrorEmptyPayloadTo() {
	ctx := context.Background()
	serviceId := uuid.New()

	payload := &adapter_types.SendVideoPayload{
		To:  "",
		Url: "https://digisac.com.br/123.mp4",
	}

	mockServiceRepository := &MockServiceRepository{}

	mockGrpcWppClient := &MockGrpcWppClient{}

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)

	result, err := adapter.SendVideo(ctx, serviceId, payload)

	suite.Require().EqualError(err, "payload.To () and payload.Url (https://digisac.com.br/123.mp4) cannot be empty")
	suite.Require().Nil(result)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterSendVideoTestSuite) TestErrorEmptyPayloadUrl() {
	ctx := context.Background()
	serviceId := uuid.New()

	payload := &adapter_types.SendVideoPayload{
		To:  "chat-id",
		Url: "",
	}

	mockServiceRepository := &MockServiceRepository{}

	mockGrpcWppClient := &MockGrpcWppClient{}

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)

	result, err := adapter.SendVideo(ctx, serviceId, payload)

	suite.Require().EqualError(err, "payload.To (chat-id) and payload.Url () cannot be empty")
	suite.Require().Nil(result)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterSendVideoTestSuite) TestErrorServiceNotFound() {
	ctx := context.Background()
	serviceId := uuid.New()

	payload := &adapter_types.SendVideoPayload{
		To:  "chat-id",
		Url: "https://digisac.com.br/123.mp4",
	}

	mockServiceRepository := &MockServiceRepository{}
	mockServiceRepository.On("FindById", ctx, serviceId).Return(nil, fmt.Errorf("service not found"))

	mockGrpcWppClient := &MockGrpcWppClient{}

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)

	result, err := adapter.SendVideo(ctx, serviceId, payload)

	suite.Require().EqualError(err, "failed to find service: service not found")
	suite.Require().Nil(result)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterSendVideoTestSuite) TestErrorSendMessage() {
	ctx := context.Background()
	serviceId := uuid.New()
	driverId := "driver-id"

	payload := &adapter_types.SendVideoPayload{
		To:             "chat-id",
		ReplyMessageId: "message-id",
		Caption:        "text",
		Mimetype:       "video/mp4",
		Filename:       "123.mp4",
		Url:            "https://digisac.com.br/123.mp4",
	}

	mockServiceRepository := &MockServiceRepository{}
	mockServiceRepository.On("FindById", ctx, serviceId).Return(&models.Service{Data: &models.ServiceData{DriverId: driverId}}, nil)

	mockGrpcWppClient := &MockGrpcWppClient{}
	mockGrpcWppClient.On("SendMessage", ctx, mock.MatchedBy(func(req *common.SendMessageRequest) bool {
		return req.Payload.ChatId == payload.To &&
			req.Payload.Text == payload.Caption &&
			req.Payload.QuotedMessageId == payload.ReplyMessageId &&
			req.Payload.File.Url == payload.Url &&
			req.Payload.File.Mimetype == payload.Mimetype &&
			req.Payload.File.Name == payload.Filename &&
			req.Metadata.ServiceId == driverId
	})).Return(nil, fmt.Errorf("failed to send message"))

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)
	result, err := adapter.SendVideo(ctx, serviceId, payload)

	suite.Require().NoError(err)
	suite.Require().Equal("failed to send message", result.Error.OriginalError)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterSendVideoTestSuite) TestValidSendVideo() {
	startDate := time.Now()
	ctx := context.Background()
	serviceId := uuid.New()
	driverId := "driver-id"
	respondeId := "responde-id"

	payload := &adapter_types.SendVideoPayload{
		To:             "chat-id",
		ReplyMessageId: "message-id",
		Caption:        "text",
		Mimetype:       "video/mp4",
		Filename:       "123.mp4",
		Url:            "https://digisac.com.br/123.mp4",
	}

	mockServiceRepository := &MockServiceRepository{}
	mockServiceRepository.On("FindById", ctx, serviceId).Return(&models.Service{Data: &models.ServiceData{DriverId: driverId}}, nil)

	mockGrpcWppClient := &MockGrpcWppClient{}
	mockGrpcWppClient.On("SendMessage", ctx, mock.MatchedBy(func(req *common.SendMessageRequest) bool {
		return req.Payload.ChatId == payload.To &&
			req.Payload.Text == payload.Caption &&
			req.Payload.QuotedMessageId == payload.ReplyMessageId &&
			req.Payload.File.Url == payload.Url &&
			req.Payload.File.Mimetype == payload.Mimetype &&
			req.Payload.File.Name == payload.Filename &&
			req.Metadata.ServiceId == driverId
	})).Return(&common.SendMessageResponse{Id: respondeId}, nil)

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)
	result, err := adapter.SendVideo(ctx, serviceId, payload)

	suite.Require().NoError(err)
	suite.Require().Equal(respondeId, result.MessageId)
	suite.Require().WithinDuration(startDate, *result.Timestamp, 5*time.Second)
	suite.Require().Equal("0", result.Ack)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func TestWhatsappAdapterSendVideoTestSuite(t *testing.T) {
	suite.Run(t, new(WhatsappAdapterSendVideoTestSuite))
}

/*
 * Test function: SendDocument
 */
type WhatsappAdapterSendDocumentTestSuite struct {
	suite.Suite
}

func (suite *WhatsappAdapterSendDocumentTestSuite) TestErrorEmptyPayloadTo() {
	ctx := context.Background()
	serviceId := uuid.New()

	payload := &adapter_types.SendDocumentPayload{
		To:  "",
		Url: "https://digisac.com.br/123.pdf",
	}

	mockServiceRepository := &MockServiceRepository{}

	mockGrpcWppClient := &MockGrpcWppClient{}

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)

	result, err := adapter.SendDocument(ctx, serviceId, payload)

	suite.Require().EqualError(err, "payload.To () and payload.Url (https://digisac.com.br/123.pdf) cannot be empty")
	suite.Require().Nil(result)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterSendDocumentTestSuite) TestErrorEmptyPayloadUrl() {
	ctx := context.Background()
	serviceId := uuid.New()

	payload := &adapter_types.SendDocumentPayload{
		To:  "chat-id",
		Url: "",
	}

	mockServiceRepository := &MockServiceRepository{}

	mockGrpcWppClient := &MockGrpcWppClient{}

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)

	result, err := adapter.SendDocument(ctx, serviceId, payload)

	suite.Require().EqualError(err, "payload.To (chat-id) and payload.Url () cannot be empty")
	suite.Require().Nil(result)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterSendDocumentTestSuite) TestErrorServiceNotFound() {
	ctx := context.Background()
	serviceId := uuid.New()

	payload := &adapter_types.SendDocumentPayload{
		To:  "chat-id",
		Url: "https://digisac.com.br/123.pdf",
	}

	mockServiceRepository := &MockServiceRepository{}
	mockServiceRepository.On("FindById", ctx, serviceId).Return(nil, fmt.Errorf("service not found"))

	mockGrpcWppClient := &MockGrpcWppClient{}

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)

	result, err := adapter.SendDocument(ctx, serviceId, payload)

	suite.Require().EqualError(err, "failed to find service: service not found")
	suite.Require().Nil(result)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterSendDocumentTestSuite) TestErrorSendMessage() {
	ctx := context.Background()
	serviceId := uuid.New()
	driverId := "driver-id"

	payload := &adapter_types.SendDocumentPayload{
		To:             "chat-id",
		ReplyMessageId: "message-id",
		Caption:        "text",
		Mimetype:       "application/pdf",
		Filename:       "123.pdf",
		Url:            "https://digisac.com.br/123.pdf",
	}

	mockServiceRepository := &MockServiceRepository{}
	mockServiceRepository.On("FindById", ctx, serviceId).Return(&models.Service{Data: &models.ServiceData{DriverId: driverId}}, nil)

	mockGrpcWppClient := &MockGrpcWppClient{}
	mockGrpcWppClient.On("SendMessage", ctx, mock.MatchedBy(func(req *common.SendMessageRequest) bool {
		return req.Payload.ChatId == payload.To &&
			req.Payload.Text == payload.Caption &&
			req.Payload.QuotedMessageId == payload.ReplyMessageId &&
			req.Payload.File.Url == payload.Url &&
			req.Payload.File.Mimetype == payload.Mimetype &&
			req.Payload.File.Name == payload.Filename &&
			req.Payload.File.AsDocument == true &&
			req.Metadata.ServiceId == driverId
	})).Return(nil, fmt.Errorf("failed to send message"))

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)
	result, err := adapter.SendDocument(ctx, serviceId, payload)

	suite.Require().NoError(err)
	suite.Require().Equal("failed to send message", result.Error.OriginalError)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func (suite *WhatsappAdapterSendDocumentTestSuite) TestValidSendDocument() {
	startDate := time.Now()
	ctx := context.Background()
	serviceId := uuid.New()
	driverId := "driver-id"
	respondeId := "responde-id"

	payload := &adapter_types.SendDocumentPayload{
		To:             "chat-id",
		ReplyMessageId: "message-id",
		Caption:        "text",
		Mimetype:       "application/pdf",
		Filename:       "123.pdf",
		Url:            "https://digisac.com.br/123.pdf",
	}

	mockServiceRepository := &MockServiceRepository{}
	mockServiceRepository.On("FindById", ctx, serviceId).Return(&models.Service{Data: &models.ServiceData{DriverId: driverId}}, nil)

	mockGrpcWppClient := &MockGrpcWppClient{}
	mockGrpcWppClient.On("SendMessage", ctx, mock.MatchedBy(func(req *common.SendMessageRequest) bool {
		return req.Payload.ChatId == payload.To &&
			req.Payload.Text == payload.Caption &&
			req.Payload.QuotedMessageId == payload.ReplyMessageId &&
			req.Payload.File.Url == payload.Url &&
			req.Payload.File.Mimetype == payload.Mimetype &&
			req.Payload.File.Name == payload.Filename &&
			req.Payload.File.AsDocument == true &&
			req.Metadata.ServiceId == driverId
	})).Return(&common.SendMessageResponse{Id: respondeId}, nil)

	adapter := NewWhatsappAdapter(mockServiceRepository, nil, mockGrpcWppClient)
	result, err := adapter.SendDocument(ctx, serviceId, payload)

	suite.Require().NoError(err)
	suite.Require().Equal(respondeId, result.MessageId)
	suite.Require().WithinDuration(startDate, *result.Timestamp, 5*time.Second)
	suite.Require().Equal("0", result.Ack)

	mockServiceRepository.AssertExpectations(suite.T())
	mockGrpcWppClient.AssertExpectations(suite.T())
}

func TestWhatsappAdapterSendDocumentTestSuite(t *testing.T) {
	suite.Run(t, new(WhatsappAdapterSendDocumentTestSuite))
}

/*
 * Test function: BuildContact
 */
func TestWhatsappAdapterBuildContact(t *testing.T) {
	id := "<EMAIL>"
	name := "Nome na agenda ou grupo"
	number := "5514999999999"
	profileName := "Meu nome no WhatsApp"
	avatarUrl := "https://digisac.com.br/"

	tests := []struct {
		testName string
		contact  *Contact
		expected *adapter_types.WebhookContact
	}{
		{
			testName: "test with contact nil",
			contact:  nil,
			expected: nil,
		},
		{
			testName: "test with contact empty",
			contact:  &Contact{},
			expected: &adapter_types.WebhookContact{Visible: true},
		},
		{
			testName: "test with contact fill",
			contact:  &Contact{Id: id, Name: name, Number: number, ProfileName: profileName, IsGroup: false, IsMe: false, AvatarUrl: avatarUrl},
			expected: &adapter_types.WebhookContact{Id: id, Name: profileName, AlternativeName: name, IsGroup: false, IsMe: false, Visible: true, AvatarUrl: avatarUrl},
		},
	}

	whatsappAdapter := NewWhatsappAdapter(nil, nil, nil)

	for _, testCase := range tests {
		t.Run(testCase.testName, func(t *testing.T) {
			result, err := whatsappAdapter.BuildContact(testCase.contact)

			require.NoError(t, err)
			require.Equal(t, testCase.expected, result)
		})
	}
}

/*
 * Test function: BuildFrom
 */
func TestWhatsappAdapterBuildFrom(t *testing.T) {
	id := "<EMAIL>"
	name := "Nome na agenda ou grupo"
	number := "5514999999999"
	profileName := "Meu nome no WhatsApp"
	avatarUrlFrom := "https://digisac.com.br/"

	tests := []struct {
		testName string
		from     *Contact
		expected *adapter_types.WebhookContact
	}{
		{
			testName: "test with contact empty",
			from:     &Contact{},
			expected: &adapter_types.WebhookContact{},
		},
		{
			testName: "test with contact fill",
			from:     &Contact{Id: id, Name: name, Number: number, ProfileName: profileName, IsGroup: false, IsMe: false, AvatarUrl: avatarUrlFrom},
			expected: &adapter_types.WebhookContact{Id: id, Name: profileName, AlternativeName: name, IsGroup: false, IsMe: false, Visible: false, AvatarUrl: avatarUrlFrom},
		},
	}

	whatsappAdapter := NewWhatsappAdapter(nil, nil, nil)

	for _, testCase := range tests {
		t.Run(testCase.testName, func(t *testing.T) {
			result, err := whatsappAdapter.BuildFrom(testCase.from)

			require.NoError(t, err)
			require.Equal(t, testCase.expected, result)
		})
	}
}

/*
 * Test function: BuildWebhook
 */
type WhatsappAdapterBuildWebhookTestSuite struct {
	suite.Suite
	whatsappAdapter *WhatsappAdapter
}

func (suite *WhatsappAdapterBuildWebhookTestSuite) SetupSuite() {
	suite.whatsappAdapter = NewWhatsappAdapter(nil, nil, nil)
}

func (suite *WhatsappAdapterBuildWebhookTestSuite) TestInvalidPayload() {
	result, err := suite.whatsappAdapter.BuildWebhook(context.Background(), uuid.Nil, "")

	suite.Require().ErrorContains(err, "failed to convert payload:")
	suite.Require().Equal(0, len(result))
}

func (suite *WhatsappAdapterBuildWebhookTestSuite) TestUnknownEvent() {
	payload := payloads.ReceiveWebhookPayload{
		Payload: map[string]interface{}{
			"Event": "unknown_event",
		},
	}

	result, err := suite.whatsappAdapter.BuildWebhook(context.Background(), uuid.Nil, payload)

	suite.Require().EqualError(err, "failed to get webhook type: unknown webhook event type unknown_event")
	suite.Require().Equal(0, len(result))
}

func TestWhatsappAdapterBuildWebhookTestSuite(t *testing.T) {
	suite.Run(t, new(WhatsappAdapterBuildWebhookTestSuite))
}

/*
 * Test function: BuildWebhook
 * Webhook type: Message
 */
type WhatsappAdapterBuildWebhookMessageTestSuite struct {
	suite.Suite
	whatsappAdapter *WhatsappAdapter
}

func (suite *WhatsappAdapterBuildWebhookMessageTestSuite) SetupSuite() {
	suite.whatsappAdapter = NewWhatsappAdapter(nil, nil, nil)
}

func (suite *WhatsappAdapterBuildWebhookMessageTestSuite) TestInvalidMessagePayload() {
	payload := payloads.ReceiveWebhookPayload{
		Payload: map[string]interface{}{
			"Event": "whatsapp.message.created",
			"Data":  "",
		},
	}

	result, err := suite.whatsappAdapter.BuildWebhook(context.Background(), uuid.Nil, payload)

	suite.Require().ErrorContains(err, "failed to convert payload data to slice of WebhookMessagePayload")
	suite.Require().Equal(0, len(result))
}

func (suite *WhatsappAdapterBuildWebhookMessageTestSuite) TestEmptyMessagePayload() {
	payload := payloads.ReceiveWebhookPayload{
		Payload: map[string]interface{}{
			"Event": "whatsapp.message.created",
			"Data":  map[string]interface{}{},
		},
	}

	result, err := suite.whatsappAdapter.BuildWebhook(context.Background(), uuid.Nil, payload)

	suite.Require().ErrorContains(err, "failed to build message single")
	suite.Require().Equal(0, len(result))
}

func (suite *WhatsappAdapterBuildWebhookMessageTestSuite) TestInvalidMessageType() {
	payload := payloads.ReceiveWebhookPayload{
		Payload: map[string]interface{}{
			"Event": "whatsapp.message.created",
			"Data": map[string]interface{}{
				"Type": "unknown",
			},
		},
	}

	result, err := suite.whatsappAdapter.BuildWebhook(context.Background(), uuid.Nil, payload)

	suite.Require().ErrorContains(err, "failed to build message single")
	suite.Require().ErrorContains(err, "failed to get webhook message type: webhook message type is unknown: unknown")
	suite.Require().Equal(0, len(result))
}

func (suite *WhatsappAdapterBuildWebhookMessageTestSuite) TestSingleMessageValid() {
	payload := payloads.ReceiveWebhookPayload{
		Payload: map[string]interface{}{
			"Event": "whatsapp.message.created",
			"Data": map[string]interface{}{
				"Type":    "chat",
				"Text":    "Hello world",
				"Data":    map[string]interface{}{},
				"Contact": map[string]interface{}{},
				"From":    map[string]interface{}{},
			},
		},
	}

	result, err := suite.whatsappAdapter.BuildWebhook(context.Background(), uuid.Nil, payload)

	suite.Require().NoError(err)
	suite.Require().Equal(1, len(result))
	suite.Require().Equal("chat", result[0].Message.Type)
	suite.Require().Equal("Hello world", result[0].Message.Text)
}

func (suite *WhatsappAdapterBuildWebhookMessageTestSuite) TestManyMessageEmpty() {
	payload := payloads.ReceiveWebhookPayload{
		Payload: map[string]interface{}{
			"Event": "whatsapp.message.created",
			"Data": []interface{}{
				map[string]interface{}{},
			},
		},
	}

	result, err := suite.whatsappAdapter.BuildWebhook(context.Background(), uuid.Nil, payload)

	suite.Require().NoError(err)
	suite.Require().Equal(0, len(result))
}

func (suite *WhatsappAdapterBuildWebhookMessageTestSuite) TestManyMessageInvalidType() {
	payload := payloads.ReceiveWebhookPayload{
		Payload: map[string]interface{}{
			"Event": "whatsapp.message.created",
			"Data": []interface{}{
				map[string]interface{}{
					"Type": "unknown",
				},
			},
		},
	}

	result, err := suite.whatsappAdapter.BuildWebhook(context.Background(), uuid.Nil, payload)

	suite.Require().NoError(err)
	suite.Require().Equal(0, len(result))
}

func (suite *WhatsappAdapterBuildWebhookMessageTestSuite) TestManyMessageValid() {
	payload := payloads.ReceiveWebhookPayload{
		Payload: map[string]interface{}{
			"Event": "whatsapp.message.created",
			"Data": []interface{}{
				map[string]interface{}{
					"Type":    "chat",
					"Text":    "Hello world",
					"Data":    map[string]interface{}{},
					"Contact": map[string]interface{}{},
					"From":    map[string]interface{}{},
				},
				map[string]interface{}{
					"Type":    "chat",
					"Text":    "Nice to meet you",
					"Data":    map[string]interface{}{},
					"Contact": map[string]interface{}{},
					"From":    map[string]interface{}{},
				},
			},
		},
	}

	result, err := suite.whatsappAdapter.BuildWebhook(context.Background(), uuid.Nil, payload)

	suite.Require().NoError(err)
	suite.Require().Equal(2, len(result))
	suite.Require().Equal("chat", result[0].Message.Type)
	suite.Require().Equal("Hello world", result[0].Message.Text)
	suite.Require().Equal("chat", result[1].Message.Type)
	suite.Require().Equal("Nice to meet you", result[1].Message.Text)
}

func (suite *WhatsappAdapterBuildWebhookMessageTestSuite) TestManyLocationMessageValid() {
	payload := payloads.ReceiveWebhookPayload{
		Payload: map[string]interface{}{
			"Event": "whatsapp.message.created",
			"Data": []interface{}{
				map[string]interface{}{
					"Type": "location",
					"Text": "",
					"Data": map[string]interface{}{
						"Location": map[string]interface{}{"Lat": -22.************, "Lng": -49.086854547512, "MapPreviewUrl": "data:image/jpeg;base64"},
					},
					"Contact": map[string]interface{}{},
					"From":    map[string]interface{}{},
				},
				map[string]interface{}{
					"Type": "location",
					"Text": "With text",
					"Data": map[string]interface{}{
						"Location": map[string]interface{}{"Lat": -22.************, "Lng": -49.086854547512, "MapPreviewUrl": "data:image/jpeg;base64"},
					},
					"Contact": map[string]interface{}{},
					"From":    map[string]interface{}{},
				},
			},
		},
	}

	result, err := suite.whatsappAdapter.BuildWebhook(context.Background(), uuid.Nil, payload)

	suite.Require().NoError(err)
	suite.Require().Equal(2, len(result))

	suite.Require().Equal("location", result[0].Message.Type)
	suite.Require().Equal("", result[0].Message.Text)
	suite.Require().Equal(-22.************, result[0].Message.Location.Lat)
	suite.Require().Equal(-49.086854547512, result[0].Message.Location.Lng)
	suite.Require().Equal("data:image/jpeg;base64", result[0].Message.Location.PreviewUrl)

	suite.Require().Equal("location", result[1].Message.Type)
	suite.Require().Equal("With text", result[1].Message.Text)
	suite.Require().Equal(-22.************, result[1].Message.Location.Lat)
	suite.Require().Equal(-49.086854547512, result[1].Message.Location.Lng)
	suite.Require().Equal("data:image/jpeg;base64", result[1].Message.Location.PreviewUrl)
}

func (suite *WhatsappAdapterBuildWebhookMessageTestSuite) TestManyMediaMessageValid() {
	payload := payloads.ReceiveWebhookPayload{
		Payload: map[string]interface{}{
			"Event": "whatsapp.message.created",
			"Data": []interface{}{
				map[string]interface{}{
					"Type":    "image",
					"Text":    "",
					"File":    map[string]interface{}{"Mimetype": "image/jpeg", "Url": "https://digisac.com.br/123.jpeg", "Filename": "123.jpeg"},
					"Data":    map[string]interface{}{},
					"Contact": map[string]interface{}{},
					"From":    map[string]interface{}{},
				},
				map[string]interface{}{
					"Type":    "image",
					"Text":    "With text",
					"File":    map[string]interface{}{"Mimetype": "image/jpeg", "Url": "https://digisac.com.br/123.jpeg", "Filename": "123.jpeg"},
					"Preview": map[string]interface{}{"Mimetype": "image/jpeg", "Url": "Base64"},
					"Data":    map[string]interface{}{},
					"Contact": map[string]interface{}{},
					"From":    map[string]interface{}{},
				},
				map[string]interface{}{
					"Type":    "video",
					"Text":    "",
					"File":    map[string]interface{}{"Mimetype": "video/mp4", "Url": "https://digisac.com.br/video.mp4", "Filename": "video.mp4"},
					"Data":    map[string]interface{}{},
					"Contact": map[string]interface{}{},
					"From":    map[string]interface{}{},
				},
				map[string]interface{}{
					"Type":    "video",
					"Text":    "With text and preview",
					"File":    map[string]interface{}{"Mimetype": "video/mp4", "Url": "https://digisac.com.br/video.mp4", "Filename": "video.mp4"},
					"Preview": map[string]interface{}{"Mimetype": "image/jpeg", "Url": "Base64"},
					"Data":    map[string]interface{}{},
					"Contact": map[string]interface{}{},
					"From":    map[string]interface{}{},
				},
				map[string]interface{}{
					"Type":    "document",
					"Text":    "",
					"File":    map[string]interface{}{"Mimetype": "application/pdf", "Url": "https://digisac.com.br/application.pdf", "Filename": "application.pdf"},
					"Data":    map[string]interface{}{},
					"Contact": map[string]interface{}{},
					"From":    map[string]interface{}{},
				},
				map[string]interface{}{
					"Type":    "ptt",
					"Text":    "",
					"File":    map[string]interface{}{"Mimetype": "audio/ogg; codecs=opus", "Url": "https://digisac.com.br/recording.ogg", "Filename": "recording.ogg"},
					"Data":    map[string]interface{}{},
					"Contact": map[string]interface{}{},
					"From":    map[string]interface{}{},
				},
				map[string]interface{}{
					"Type":    "audio",
					"Text":    "",
					"File":    map[string]interface{}{"Mimetype": "audio/mpeg", "Url": "https://digisac.com.br/recording.mp3", "Filename": "recording.mp3"},
					"Data":    map[string]interface{}{},
					"Contact": map[string]interface{}{},
					"From":    map[string]interface{}{},
				},
			},
		},
	}

	result, err := suite.whatsappAdapter.BuildWebhook(context.Background(), uuid.Nil, payload)

	suite.Require().NoError(err)
	suite.Require().Equal(7, len(result))

	suite.Require().Equal("image", result[0].Message.Type)
	suite.Require().Equal("", result[0].Message.Text)
	suite.Require().Equal("image/jpeg", result[0].Message.File.Mimetype)
	suite.Require().Equal("https://digisac.com.br/123.jpeg", result[0].Message.File.Url)
	suite.Require().Equal("123.jpeg", result[0].Message.File.Name)
	suite.Require().Nil(result[0].Message.Preview)

	suite.Require().Equal("image", result[1].Message.Type)
	suite.Require().Equal("With text", result[1].Message.Text)
	suite.Require().Equal("image/jpeg", result[1].Message.File.Mimetype)
	suite.Require().Equal("https://digisac.com.br/123.jpeg", result[1].Message.File.Url)
	suite.Require().Equal("123.jpeg", result[1].Message.File.Name)
	suite.Require().Nil(result[1].Message.Preview)

	suite.Require().Equal("video", result[2].Message.Type)
	suite.Require().Equal("", result[2].Message.Text)
	suite.Require().Equal("video/mp4", result[2].Message.File.Mimetype)
	suite.Require().Equal("https://digisac.com.br/video.mp4", result[2].Message.File.Url)
	suite.Require().Equal("video.mp4", result[2].Message.File.Name)
	suite.Require().Nil(result[2].Message.Preview)

	suite.Require().Equal("video", result[3].Message.Type)
	suite.Require().Equal("With text and preview", result[3].Message.Text)
	suite.Require().Equal("video/mp4", result[3].Message.File.Mimetype)
	suite.Require().Equal("https://digisac.com.br/video.mp4", result[3].Message.File.Url)
	suite.Require().Equal("video.mp4", result[3].Message.File.Name)
	suite.Require().Equal("image/jpeg", result[3].Message.Preview.Mimetype)
	suite.Require().Equal("Base64", result[3].Message.Preview.Base64)

	suite.Require().Equal("document", result[4].Message.Type)
	suite.Require().Equal("", result[4].Message.Text)
	suite.Require().Equal("application/pdf", result[4].Message.File.Mimetype)
	suite.Require().Equal("https://digisac.com.br/application.pdf", result[4].Message.File.Url)
	suite.Require().Equal("application.pdf", result[4].Message.File.Name)
	suite.Require().Nil(result[4].Message.Preview)

	suite.Require().Equal("audio", result[5].Message.Type)
	suite.Require().Equal("", result[5].Message.Text)
	suite.Require().Equal("audio/ogg; codecs=opus", result[5].Message.File.Mimetype)
	suite.Require().Equal("https://digisac.com.br/recording.ogg", result[5].Message.File.Url)
	suite.Require().Equal("recording.ogg", result[5].Message.File.Name)
	suite.Require().Nil(result[5].Message.Preview)

	suite.Require().Equal("audio", result[6].Message.Type)
	suite.Require().Equal("", result[6].Message.Text)
	suite.Require().Equal("audio/mpeg", result[6].Message.File.Mimetype)
	suite.Require().Equal("https://digisac.com.br/recording.mp3", result[6].Message.File.Url)
	suite.Require().Equal("recording.mp3", result[6].Message.File.Name)
	suite.Require().Nil(result[6].Message.Preview)
}

func (suite *WhatsappAdapterBuildWebhookMessageTestSuite) TestManyMessageSomeInvalid() {
	payload := payloads.ReceiveWebhookPayload{
		Payload: map[string]interface{}{
			"Event": "whatsapp.message.created",
			"Data": []interface{}{
				map[string]interface{}{
					"Type": "unknown",
				},
				map[string]interface{}{
					"Type":    "chat",
					"Text":    "Hello world",
					"Data":    map[string]interface{}{},
					"Contact": map[string]interface{}{},
					"From":    map[string]interface{}{},
				},
				map[string]interface{}{
					"Type": "unknown",
				},
				map[string]interface{}{
					"Type":    "chat",
					"Text":    "Nice to meet you",
					"Data":    map[string]interface{}{},
					"Contact": map[string]interface{}{},
					"From":    map[string]interface{}{},
				},
			},
		},
	}

	result, err := suite.whatsappAdapter.BuildWebhook(context.Background(), uuid.Nil, payload)

	suite.Require().NoError(err)
	suite.Require().Equal(2, len(result))
	suite.Require().Equal("chat", result[0].Message.Type)
	suite.Require().Equal("Hello world", result[0].Message.Text)
	suite.Require().Equal("chat", result[1].Message.Type)
	suite.Require().Equal("Nice to meet you", result[1].Message.Text)
}

func TestWhatsappAdapterBuildWebhookMessageTestSuite(t *testing.T) {
	suite.Run(t, new(WhatsappAdapterBuildWebhookMessageTestSuite))
}

/*
 * Test function: DownloadMedia
 */
type WhatsappAdapterDownloadMediaTestSuite struct {
	suite.Suite
	whatsappAdapter *WhatsappAdapter
}

func (suite *WhatsappAdapterDownloadMediaTestSuite) SetupSuite() {
	suite.whatsappAdapter = NewWhatsappAdapter(nil, nil, nil)
}

func (suite *WhatsappAdapterDownloadMediaTestSuite) TestEmptyMediaUrl() {
	result, err := suite.whatsappAdapter.DownloadMedia(context.Background(), uuid.Nil, "", "")

	suite.Require().EqualError(err, "empty media url")
	suite.Require().Nil(result)
}

func (suite *WhatsappAdapterDownloadMediaTestSuite) TestInvalidMediaUrl() {
	result, err := suite.whatsappAdapter.DownloadMedia(context.Background(), uuid.Nil, "", "https://")

	suite.Require().ErrorContains(err, "failed to download media from url")
	suite.Require().Nil(result)
}

func (suite *WhatsappAdapterDownloadMediaTestSuite) TestValidMediaUrl() {
	result, err := suite.whatsappAdapter.DownloadMedia(context.Background(), uuid.Nil, "", "https://digisac.com.br/")

	suite.Require().NoError(err)
	suite.Require().Equal("stream", result.Type)
	suite.Require().NotNil(result.Stream)
}

func TestWhatsappAdapterDownloadMediaTestSuite(t *testing.T) {
	suite.Run(t, new(WhatsappAdapterDownloadMediaTestSuite))
}

/*
 * Test function: SendTemplate
 */
func TestWhatsappAdapterSendTemplate(t *testing.T) {
	whatsappAdapter := NewWhatsappAdapter(nil, nil, nil)
	result, err := whatsappAdapter.SendTemplate(context.Background(), uuid.New(), &adapter_types.SendTemplatePayload{})

	require.NoError(t, err)
	require.Nil(t, result)
}

/*
 * Test function: SendInteractiveMessage
 */
func TestWhatsappAdapterSendInteractiveMessage(t *testing.T) {
	whatsappAdapter := NewWhatsappAdapter(nil, nil, nil)
	result, err := whatsappAdapter.SendInteractiveMessage(context.Background(), uuid.New(), &adapter_types.SendInteractiveMessagePayload{})

	require.NoError(t, err)
	require.Nil(t, result)
}

/*
 * Test function: GetTemplates
 */
func TestWhatsappAdapterGetTemplates(t *testing.T) {
	whatsappAdapter := NewWhatsappAdapter(nil, nil, nil)
	result, err := whatsappAdapter.GetTemplates(context.Background(), uuid.New())

	require.NoError(t, err)
	require.Nil(t, result)
}

/*
 * Test function: CreateTemplate
 */
func TestWhatsappAdapterCreateTemplate(t *testing.T) {
	whatsappAdapter := NewWhatsappAdapter(nil, nil, nil)
	result, err := whatsappAdapter.CreateTemplate(context.Background(), uuid.New(), uuid.New())

	require.NoError(t, err)
	require.Nil(t, result)
}

/*
 * Test function: DeleteTemplate
 */
func TestWhatsappAdapterDeleteTemplate(t *testing.T) {
	whatsappAdapter := NewWhatsappAdapter(nil, nil, nil)
	err := whatsappAdapter.DeleteTemplate(context.Background(), uuid.New(), "")

	require.NoError(t, err)
}
