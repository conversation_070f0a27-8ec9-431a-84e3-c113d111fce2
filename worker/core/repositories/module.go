package repositories

import (
	"go.uber.org/fx"
)

var Module = fx.Module("repositories", fx.Provide(NewAccountRepository,
	NewQuestionRepository,
	NewAnswerRepository,
	NewCampaignRepository,
	NewCampaignMessageRepository,
	NewCampaignMessageProgressRepository,
	NewContactRepository,
	NewDepartmentRepository,
	NewFileRepository,
	NewLinkRepository,
	NewMessageRepository,
	NewServiceRepository,
	NewStickerRepository,
	NewStickerUserRepository,
	NewTagRepository,
	NewTicketRepository,
	NewTicketTopicRepository,
	NewTicketTransferRepository,
	NewUserRepository,
	NewWhatsappBusinessTemplateRepository,
	NewWhatsappBusinessTemplateHistoryRepository,
	NewContactBlockListRepository,
	NewContactBlockListItemRepository,
	NewContactBlockListControlRepository,
	NewPersonRepository,
	NewOrganizationRepository,
))
