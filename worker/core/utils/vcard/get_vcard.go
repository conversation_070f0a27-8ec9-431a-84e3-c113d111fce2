package vcard

import (
	"fmt"
	"strings"
)

type ContactName struct {
	FirstName string `json:"first_name"`
	LastName  string `json:"last_name"`
}

type ContactPhones struct {
	Phone string `json:"phone"`
	Type  string `json:"type"`
	WaId  string `json:"wa_id"`
}

type VCardContact struct {
	Name   *ContactName     `json:"name"`
	Phones []*ContactPhones `json:"phones"`
}

func GetVCard(contact *VCardContact) (string, error) {
	if contact == nil {
		return "", fmt.<PERSON>rro<PERSON>("contact is nil")
	}

	var firstName, lastName string

	if contact.Name != nil {
		firstName = contact.Name.FirstName
		lastName = contact.Name.LastName
	}

	var phone, waId, phoneNumber string
	if len(contact.Phones) > 0 {
		firstPhone := contact.Phones[0]

		if firstPhone.WaId != "" {
			phone = firstPhone.WaId
		} else {
			phone = cleanNumber(firstPhone.Phone)
		}
		waId = phone

		phoneNumber = cleanNumber(firstPhone.Phone)
		if phoneNumber == "" {
			phoneNumber = firstPhone.WaId
		}
	}

	return fmt.Sprintf(
		"BEGIN:VCARD\nVERSION:3.0\nN:%s;%s;;;\nFN:%s %s\nitem1.TEL;waid=%s:%s\nitem1.X-ABLabel:Mobile\nEND:VCARD",
		lastName, firstName, firstName, lastName, waId, phoneNumber,
	), nil
}

func cleanNumber(number string) string {
	return strings.Map(func(r rune) rune {
		if r >= '0' && r <= '9' {
			return r
		}
		return -1
	}, number)
}
