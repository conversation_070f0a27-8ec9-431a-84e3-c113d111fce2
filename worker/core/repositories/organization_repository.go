package repositories

import (
	"digisac-go/worker/core/models"

	"gorm.io/gorm"
)

type OrganizationRepository interface {
	Repository[models.Organization]
}

type organizationRepository struct {
	*GormRepository[models.Organization]
}

func NewOrganizationRepository(db *gorm.DB) OrganizationRepository {
	return &organizationRepository{
		GormRepository: NewGormRepository[models.Organization](db),
	}
}
