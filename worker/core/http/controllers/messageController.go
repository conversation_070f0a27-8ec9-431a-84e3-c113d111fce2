package controllers

import (
	"digisac-go/worker/core/http/middlewares"
	"digisac-go/worker/core/http/payloads"
	message_forwarder "digisac-go/worker/core/services/message/message_forward"
	"digisac-go/worker/core/services/message/message_receiver"
	"digisac-go/worker/core/services/message/message_sender"
	httputils "digisac-go/worker/core/utils/http_utils"
	"fmt"
	"log/slog"
	"strings"

	"github.com/gofiber/fiber/v3"
)

type MessageController struct {
	messageReceiveService *message_receiver.ReceiveService
	messageSendService    *message_sender.SendService
	messageForwardService *message_forwarder.ForwardService
}

func NewMessageController(messageReceiveService *message_receiver.ReceiveService, messageSendService *message_sender.SendService, messageForwardService *message_forwarder.ForwardService,
) *MessageController {
	return &MessageController{
		messageReceiveService: messageReceiveService,
		messageSendService:    messageSendService,
		messageForwardService: messageForwardService,
	}
}

func (c *MessageController) SendMessageToBroker(ctx fiber.Ctx) (err error) {
	payload := new(payloads.SendMessageToBrokerRequest)

	if returned, err := httputils.ParseAndValidatePayload(ctx, payload); returned {
		if err != nil {
			slog.ErrorContext(ctx.Context(), "Failed to parse and validate payload", slog.Any("payload", payload), slog.String("error", err.Error()))
		}
		return fmt.Errorf("failed to parse and validate payload: %w", err)
	}

	result, err := c.messageSendService.SendMessageToBroker(middlewares.TracingCtx(ctx), payload.Payload.Payload)

	if err != nil {
		slog.ErrorContext(ctx.Context(), "Failed to send message to broker", slog.Any("payload", payload.Payload), slog.String("error", err.Error()))
		return c.handleErrors(ctx, err)
	}

	return ctx.JSON(result)
}

func (c *MessageController) SendVCards(ctx fiber.Ctx) (err error) {
	payload := new(payloads.SendVCardsRequest)

	if returned, err := httputils.ParseAndValidatePayload(ctx, payload); returned {
		if err != nil {
			slog.ErrorContext(ctx.Context(), "Failed to parse and validate payload", slog.Any("payload", payload), slog.String("error", err.Error()))
		}
		return fmt.Errorf("failed to parse and validate payload: %w", err)
	}

	result, err := c.messageSendService.SendVCards(middlewares.TracingCtx(ctx), payload.Payload)

	if err != nil {
		slog.ErrorContext(ctx.Context(), "Failed to send vcards", slog.Any("payload", payload.Payload), slog.String("error", err.Error()))
		return c.handleErrors(ctx, err)
	}

	return ctx.JSON(result)
}

func (c *MessageController) SendReaction(ctx fiber.Ctx) (err error) {
	payload := new(payloads.SendReactionRequest)

	if returned, err := httputils.ParseAndValidatePayload(ctx, payload); returned {
		if err != nil {
			slog.ErrorContext(ctx.Context(), "Failed to parse and validate payload", slog.Any("payload", payload), slog.String("error", err.Error()))
		}
		return fmt.Errorf("failed to parse and validate payload: %w", err)
	}

	result, err := c.messageSendService.SendReaction(middlewares.TracingCtx(ctx), payload.Payload)

	if err != nil {
		slog.ErrorContext(ctx.Context(), "Failed to send reaction", slog.Any("payload", payload.Payload), slog.String("error", err.Error()))
		return c.handleErrors(ctx, err)
	}

	return ctx.JSON(result)
}

func (c *MessageController) RevokeReaction(ctx fiber.Ctx) (err error) {
	payload := new(payloads.RevokeReactionRequest)

	if returned, err := httputils.ParseAndValidatePayload(ctx, payload); returned {
		if err != nil {
			slog.ErrorContext(ctx.Context(), "Failed to parse and validate payload", slog.Any("payload", payload), slog.String("error", err.Error()))
		}
		return fmt.Errorf("failed to parse and validate payload: %w", err)
	}

	result, err := c.messageSendService.RevokeReaction(middlewares.TracingCtx(ctx), payload.Payload)

	if err != nil {
		slog.ErrorContext(ctx.Context(), "Failed to revoke reaction", slog.Any("payload", payload.Payload), slog.String("error", err.Error()))
		return c.handleErrors(ctx, err)
	}

	return ctx.JSON(result)
}

func (c *MessageController) SendMessage(ctx fiber.Ctx) (err error) {
	payload := new(payloads.SendMessageRequest)

	if returned, err := httputils.ParseAndValidatePayload(ctx, payload); returned {
		if err != nil {
			slog.ErrorContext(ctx.Context(), "Failed to parse and validate payload", slog.Any("payload", payload), slog.String("error", err.Error()))
		}
		return fmt.Errorf("failed to parse and validate payload: %w", err)
	}

	result, err := c.messageSendService.SendMessage(middlewares.TracingCtx(ctx), payload.Payload)

	if err != nil {
		slog.ErrorContext(ctx.Context(), "Failed to send message", slog.Any("payload", payload.Payload), slog.String("error", err.Error()))
		return c.handleErrors(ctx, err)
	}

	return ctx.JSON(result)
}

func (c *MessageController) Webhook(ctx fiber.Ctx) (err error) {
	payload := new(payloads.WebhookRequest)

	if returned, err := httputils.ParseAndValidatePayload(ctx, payload); returned {
		if err != nil {
			slog.ErrorContext(ctx.Context(), "Failed to parse and validate payload", slog.Any("payload", payload), slog.String("error", err.Error()))
		}
		return fmt.Errorf("failed to parse and validate payload: %w", err)
	}

	err = c.messageReceiveService.Webhook(middlewares.TracingCtx(ctx), payload.Payload)

	if err != nil {
		slog.ErrorContext(ctx.Context(), "Failed to process webhook", slog.Any("payload", payload.Payload), slog.String("error", err.Error()))
		return ctx.SendStatus(500)
	}

	return ctx.SendStatus(200)
}

func (c *MessageController) ReceiveMessageWebhook(ctx fiber.Ctx) (err error) {
	payload := new(payloads.ReceiveMessageWebhookRequest)

	if returned, err := httputils.ParseAndValidatePayload(ctx, payload); returned {
		if err != nil {
			slog.ErrorContext(ctx.Context(), "Failed to parse and validate payload", slog.Any("payload", payload), slog.String("error", err.Error()))
		}
		return fmt.Errorf("failed to parse and validate payload: %w", err)
	}

	err = c.messageReceiveService.ReceiveMessage(middlewares.TracingCtx(ctx), payload.Payload.Payload)

	if err != nil {
		slog.ErrorContext(ctx.Context(), "Failed to receive webhook", slog.Any("payload", payload.Payload), slog.String("error", err.Error()))
		return ctx.SendStatus(500)
	}

	return ctx.SendStatus(200)
}

func (c *MessageController) ReceiveStatusWebhook(ctx fiber.Ctx) (err error) {
	payload := new(payloads.ReceiveStatusWebhookRequest)

	if returned, err := httputils.ParseAndValidatePayload(ctx, payload); returned {
		if err != nil {
			slog.ErrorContext(ctx.Context(), "Failed to parse and validate payload", slog.Any("payload", payload), slog.String("error", err.Error()))
		}
		return fmt.Errorf("failed to parse and validate payload: %w", err)
	}

	err = c.messageReceiveService.ReceiveStatuses(middlewares.TracingCtx(ctx), payload.Payload.Payload)

	if err != nil {
		slog.ErrorContext(ctx.Context(), "Failed to receive webhook", slog.Any("payload", payload.Payload), slog.String("error", err.Error()))
		return ctx.SendStatus(500)
	}

	return ctx.SendStatus(200)
}

func (c *MessageController) ReceiveServiceWebhook(ctx fiber.Ctx) (err error) {
	payload := new(payloads.ReceiveServiceWebhookRequest)

	if returned, err := httputils.ParseAndValidatePayload(ctx, payload); returned {
		if err != nil {
			slog.ErrorContext(ctx.Context(), "Failed to parse and validate payload", slog.Any("payload", payload), slog.String("error", err.Error()))
		}
		return fmt.Errorf("failed to parse and validate payload: %w", err)
	}

	err = c.messageReceiveService.ReceiveService(middlewares.TracingCtx(ctx), payload.Payload.Payload)

	if err != nil {
		slog.ErrorContext(ctx.Context(), "Failed to receive webhook", slog.Any("payload", payload.Payload), slog.String("error", err.Error()))
		return ctx.SendStatus(500)
	}

	return ctx.SendStatus(200)
}
func (c *MessageController) ReceiveTemplateWebhook(ctx fiber.Ctx) (err error) {
	payload := new(payloads.ReceiveTemplateWebhookRequest)

	if returned, err := httputils.ParseAndValidatePayload(ctx, payload); returned {
		if err != nil {
			slog.ErrorContext(ctx.Context(), "Failed to parse and validate payload", slog.Any("payload", payload), slog.String("error", err.Error()))
		}
		return fmt.Errorf("failed to parse and validate payload: %w", err)
	}

	err = c.messageReceiveService.ReceiveTemplateUpdate(middlewares.TracingCtx(ctx), payload.Payload.Payload)

	if err != nil {
		slog.ErrorContext(ctx.Context(), "Failed to receive webhook", slog.Any("payload", payload.Payload), slog.String("error", err.Error()))
		return ctx.SendStatus(500)
	}

	return ctx.SendStatus(200)
}

func (c *MessageController) ReceiveMedia(ctx fiber.Ctx) (err error) {
	payload := new(payloads.ReceiveMediaRequest)

	if returned, err := httputils.ParseAndValidatePayload(ctx, payload); returned {
		if err != nil {
			slog.ErrorContext(ctx.Context(), "Failed to parse and validate payload", slog.Any("payload", payload), slog.String("error", err.Error()))
		}
		return fmt.Errorf("failed to parse and validate payload: %w", err)
	}

	err = c.messageReceiveService.ReceiveMedia(middlewares.TracingCtx(ctx), payload.Payload.Payload)

	if err != nil {
		slog.ErrorContext(ctx.Context(), "Failed to receive media", slog.Any("payload", payload.Payload), slog.String("error", err.Error()))
		return ctx.SendStatus(500)
	}

	return ctx.SendStatus(200)
}

func (c *MessageController) ForwardMessages(ctx fiber.Ctx) (err error) {
	payload := new(payloads.ForwardMessagesRequest)

	if returned, err := httputils.ParseAndValidatePayload(ctx, payload); returned {
		if err != nil {
			slog.ErrorContext(ctx.Context(), "Failed to parse and validate payload", slog.Any("payload", payload), slog.String("error", err.Error()))
		}
		return fmt.Errorf("failed to parse and validate payload: %w", err)
	}

	result, err := c.messageForwardService.ForwardMessages(middlewares.TracingCtx(ctx), payload.Payload)

	if err != nil {
		slog.ErrorContext(ctx.Context(), "Failed to forward messages", slog.Any("payload", payload.Payload), slog.String("error", err.Error()))
		return c.handleErrors(ctx, err)
	}

	return ctx.JSON(result)
}

func (c *MessageController) handleErrors(ctx fiber.Ctx, err error) *fiber.Error {
	if strings.Contains(err.Error(), "service disconnected") {
		slog.ErrorContext(ctx.Context(), "Service disconnected", slog.String("error", err.Error()))
		return fiber.NewError(500, "Service disconnected.")
	}

	slog.ErrorContext(ctx.Context(), "Internal server error", slog.String("error", err.Error()))

	return fiber.NewError(fiber.ErrInternalServerError.Code, fmt.Sprintf("Internal server error: %s", err.Error()))
}
