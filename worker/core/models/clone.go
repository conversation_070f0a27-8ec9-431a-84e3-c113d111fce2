package models

import (
	"gorm.io/datatypes"
)

// <PERSON><PERSON> creates a deep copy of the AccountFlags struct
func (original *AccountFlags) Clone() *AccountFlags {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// <PERSON><PERSON> creates a deep copy of the AccountAiPlan struct
func (original *AccountAiPlan) Clone() *AccountAiPlan {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// <PERSON><PERSON> creates a deep copy of the AccountPlan struct
func (original *AccountPlan) Clone() *AccountPlan {
	if original == nil {
		return nil
	}
	// Create new instance and copy all simple fields
	clone := *original

	// Only handle JSONB fields that need deep cloning

	if original.Services != nil {
		clone.Services = make(map[string]int)
		for k, v := range original.Services {
			clone.Services[k] = v
		}
	}

	return &clone
}

// <PERSON><PERSON> creates a deep copy of the AccountCampaignSettings struct
func (original *AccountCampaignSettings) Clone() *AccountCampaignSettings {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// Clone creates a deep copy of the AccountSettings struct
func (original *AccountSettings) Clone() *AccountSettings {
	if original == nil {
		return nil
	}
	// Create new instance and copy all simple fields
	clone := *original

	// Only handle JSONB fields that need deep cloning

	if original.Flags != nil {
		clone.Flags = original.Flags.Clone()
	}

	if original.Campaign != nil {
		clone.Campaign = original.Campaign.Clone()
	}

	return &clone
}

// Clone creates a deep copy of the Account struct
func (original *Account) Clone() *Account {
	if original == nil {
		return nil
	}
	// Create new instance and copy all simple fields
	clone := *original

	// Only handle JSONB fields that need deep cloning

	if original.Settings != nil {
		clone.Settings = original.Settings.Clone()
	}

	if original.Data != nil {
		clone.Data = make(datatypes.JSON, len(original.Data))
		copy(clone.Data, original.Data)
	}

	if original.Plan != nil {
		clone.Plan = original.Plan.Clone()
	}

	return &clone
}

// Clone creates a deep copy of the Answer struct
func (original *Answer) Clone() *Answer {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// Clone creates a deep copy of the CampaignConfig struct
func (original *CampaignConfig) Clone() *CampaignConfig {
	if original == nil {
		return nil
	}
	// Create new instance and copy all simple fields
	clone := *original

	// Only handle JSONB fields that need deep cloning

	// TODO: Error (*interface{}) may need manual deep copy handling

	return &clone
}

// Clone creates a deep copy of the Campaign struct
func (original *Campaign) Clone() *Campaign {
	if original == nil {
		return nil
	}
	// Create new instance and copy all simple fields
	clone := *original

	// Only handle JSONB fields that need deep cloning

	if original.Config != nil {
		clone.Config = original.Config.Clone()
	}

	return &clone
}

// Clone creates a deep copy of the ExtraOptions struct
func (original *ExtraOptions) Clone() *ExtraOptions {
	if original == nil {
		return nil
	}
	// Create new instance and copy all simple fields
	clone := *original

	// Only handle JSONB fields that need deep cloning

	if original.Parameters != nil {
		clone.Parameters = make([]*HsmParameters, len(original.Parameters))
		copy(clone.Parameters, original.Parameters)
	}

	return &clone
}

// Clone creates a deep copy of the CampaignMessage struct
func (original *CampaignMessage) Clone() *CampaignMessage {
	if original == nil {
		return nil
	}
	// Create new instance and copy all simple fields
	clone := *original

	// Only handle JSONB fields that need deep cloning

	if original.ExtraOptions != nil {
		clone.ExtraOptions = original.ExtraOptions.Clone()
	}

	return &clone
}

// Clone creates a deep copy of the CampaignMessageProgress struct
func (original *CampaignMessageProgress) Clone() *CampaignMessageProgress {
	if original == nil {
		return nil
	}
	// Create new instance and copy all simple fields
	clone := *original

	// Only handle JSONB fields that need deep cloning

	if original.Parameters != nil {
		clone.Parameters = make(datatypes.JSON, len(original.Parameters))
		copy(clone.Parameters, original.Parameters)
	}

	return &clone
}

// Clone creates a deep copy of the ContactDataSurvey struct
func (original *ContactDataSurvey) Clone() *ContactDataSurvey {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// Clone creates a deep copy of the ContactDataBlock struct
func (original *ContactDataBlock) Clone() *ContactDataBlock {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// Clone creates a deep copy of the ContactData struct
func (original *ContactData) Clone() *ContactData {
	if original == nil {
		return nil
	}
	// Create new instance and copy all simple fields
	clone := *original

	// Only handle JSONB fields that need deep cloning

	if original.Survey != nil {
		clone.Survey = original.Survey.Clone()
	}

	return &clone
}

// Clone creates a deep copy of the Contact struct
func (original *Contact) Clone() *Contact {
	if original == nil {
		return nil
	}
	// Create new instance and copy all simple fields
	clone := *original

	// Only handle JSONB fields that need deep cloning

	if original.Data != nil {
		clone.Data = original.Data.Clone()
	}

	if original.DataBlock != nil {
		clone.DataBlock = original.DataBlock.Clone()
	}

	return &clone
}

// Clone creates a deep copy of the ContactBlockList struct
func (original *ContactBlockList) Clone() *ContactBlockList {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// Clone creates a deep copy of the ContactBlockListControl struct
func (original *ContactBlockListControl) Clone() *ContactBlockListControl {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// Clone creates a deep copy of the ContactBlockListItem struct
func (original *ContactBlockListItem) Clone() *ContactBlockListItem {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// Clone creates a deep copy of the Department struct
func (original *Department) Clone() *Department {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// Clone creates a deep copy of the File struct
func (original *File) Clone() *File {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// Clone creates a deep copy of the Link struct
func (original *Link) Clone() *Link {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// Clone creates a deep copy of the MessageError struct
func (original *MessageError) Clone() *MessageError {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// Clone creates a deep copy of the FileDownload struct
func (original *FileDownload) Clone() *FileDownload {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// Clone creates a deep copy of the Location struct
func (original *Location) Clone() *Location {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// Clone creates a deep copy of the CtwaContext struct
func (original *CtwaContext) Clone() *CtwaContext {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// Clone creates a deep copy of the MessageHsmParameterImage struct
func (original *MessageHsmParameterImage) Clone() *MessageHsmParameterImage {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// Clone creates a deep copy of the MessageHsmParameters struct
func (original *MessageHsmParameters) Clone() *MessageHsmParameters {
	if original == nil {
		return nil
	}
	// Create new instance and copy all simple fields
	clone := *original

	// Only handle JSONB fields that need deep cloning

	if original.Image != nil {
		clone.Image = original.Image.Clone()
	}

	return &clone
}

// Clone creates a deep copy of the HsmParameters struct
func (original *HsmParameters) Clone() *HsmParameters {
	if original == nil {
		return nil
	}
	// Create new instance and copy all simple fields
	clone := *original

	// Only handle JSONB fields that need deep cloning

	if original.Parameters != nil {
		clone.Parameters = make([]*MessageHsmParameters, len(original.Parameters))
		copy(clone.Parameters, original.Parameters)
	}

	return &clone
}

// Clone creates a deep copy of the InteractiveMessage struct
func (original *InteractiveMessage) Clone() *InteractiveMessage {
	if original == nil {
		return nil
	}
	// Create new instance and copy all simple fields
	clone := *original

	// Only handle JSONB fields that need deep cloning

	if original.Header != nil {
		clone.Header = original.Header.Clone()
	}

	if original.Body != nil {
		clone.Body = original.Body.Clone()
	}

	if original.Footer != nil {
		clone.Footer = original.Footer.Clone()
	}

	if original.Action != nil {
		clone.Action = original.Action.Clone()
	}

	return &clone
}

// Clone creates a deep copy of the InteractiveHeader struct
func (original *InteractiveHeader) Clone() *InteractiveHeader {
	if original == nil {
		return nil
	}
	// Create new instance and copy all simple fields
	clone := *original

	// Only handle JSONB fields that need deep cloning

	// TODO: Image (*interface{}) may need manual deep copy handling

	// TODO: Video (*interface{}) may need manual deep copy handling

	// TODO: Document (*interface{}) may need manual deep copy handling

	return &clone
}

// Clone creates a deep copy of the InteractiveBody struct
func (original *InteractiveBody) Clone() *InteractiveBody {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// Clone creates a deep copy of the InteractiveFooter struct
func (original *InteractiveFooter) Clone() *InteractiveFooter {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// Clone creates a deep copy of the InteractiveAction struct
func (original *InteractiveAction) Clone() *InteractiveAction {
	if original == nil {
		return nil
	}
	// Create new instance and copy all simple fields
	clone := *original

	// Only handle JSONB fields that need deep cloning

	if original.Buttons != nil {
		clone.Buttons = make([]*InteractiveButton, len(original.Buttons))
		copy(clone.Buttons, original.Buttons)
	}

	if original.Sections != nil {
		clone.Sections = make([]*InteractiveSection, len(original.Sections))
		copy(clone.Sections, original.Sections)
	}

	return &clone
}

// Clone creates a deep copy of the InteractiveButton struct
func (original *InteractiveButton) Clone() *InteractiveButton {
	if original == nil {
		return nil
	}
	// Create new instance and copy all simple fields
	clone := *original

	// Only handle JSONB fields that need deep cloning

	if original.Reply != nil {
		clone.Reply = original.Reply.Clone()
	}

	return &clone
}

// Clone creates a deep copy of the InteractiveReplyButton struct
func (original *InteractiveReplyButton) Clone() *InteractiveReplyButton {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// Clone creates a deep copy of the InteractiveSection struct
func (original *InteractiveSection) Clone() *InteractiveSection {
	if original == nil {
		return nil
	}
	// Create new instance and copy all simple fields
	clone := *original

	// Only handle JSONB fields that need deep cloning

	if original.Rows != nil {
		clone.Rows = make([]*InteractiveRow, len(original.Rows))
		copy(clone.Rows, original.Rows)
	}

	return &clone
}

// Clone creates a deep copy of the InteractiveRow struct
func (original *InteractiveRow) Clone() *InteractiveRow {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// Clone creates a deep copy of the MessageData struct
func (original *MessageData) Clone() *MessageData {
	if original == nil {
		return nil
	}
	// Create new instance and copy all simple fields
	clone := *original

	// Only handle JSONB fields that need deep cloning

	// TODO: Ack (interface{}) may need manual deep copy handling

	if original.Error != nil {
		clone.Error = original.Error.Clone()
	}

	if original.FileDownload != nil {
		clone.FileDownload = original.FileDownload.Clone()
	}

	if original.Location != nil {
		clone.Location = original.Location.Clone()
	}

	// TODO: VCard (interface{}) may need manual deep copy handling

	if original.CtwaContext != nil {
		clone.CtwaContext = original.CtwaContext.Clone()
	}

	if original.HsmParameters != nil {
		clone.HsmParameters = make([]*HsmParameters, len(original.HsmParameters))
		copy(clone.HsmParameters, original.HsmParameters)
	}

	if original.Interactive != nil {
		clone.Interactive = original.Interactive.Clone()
	}

	return &clone
}

// Clone creates a deep copy of the Message struct
func (original *Message) Clone() *Message {
	if original == nil {
		return nil
	}
	// Create new instance and copy all simple fields
	clone := *original

	// Only handle JSONB fields that need deep cloning

	if original.Data != nil {
		clone.Data = original.Data.Clone()
	}

	return &clone
}

// Clone creates a deep copy of the Organization struct
func (original *Organization) Clone() *Organization {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// Clone creates a deep copy of the Person struct
func (original *Person) Clone() *Person {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// Clone creates a deep copy of the Question struct
func (original *Question) Clone() *Question {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// Clone creates a deep copy of the WebchatFile struct
func (original *WebchatFile) Clone() *WebchatFile {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// Clone creates a deep copy of the WebchatData struct
func (original *WebchatData) Clone() *WebchatData {
	if original == nil {
		return nil
	}
	// Create new instance and copy all simple fields
	clone := *original

	// Only handle JSONB fields that need deep cloning

	clone.File = *(&original.File).Clone()

	return &clone
}

// Clone creates a deep copy of the ServiceSettings struct
func (original *ServiceSettings) Clone() *ServiceSettings {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// Clone creates a deep copy of the ServiceDataStatus struct
func (original *ServiceDataStatus) Clone() *ServiceDataStatus {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// Clone creates a deep copy of the ServiceData struct
func (original *ServiceData) Clone() *ServiceData {
	if original == nil {
		return nil
	}
	// Create new instance and copy all simple fields
	clone := *original

	// Only handle JSONB fields that need deep cloning

	if original.Status != nil {
		clone.Status = original.Status.Clone()
	}

	return &clone
}

// Clone creates a deep copy of the ServiceInternalData struct
func (original *ServiceInternalData) Clone() *ServiceInternalData {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// Clone creates a deep copy of the Service struct
func (original *Service) Clone() *Service {
	if original == nil {
		return nil
	}
	// Create new instance and copy all simple fields
	clone := *original

	// Only handle JSONB fields that need deep cloning

	if original.Data != nil {
		clone.Data = original.Data.Clone()
	}

	if original.InternalData != nil {
		clone.InternalData = original.InternalData.Clone()
	}

	if original.Settings != nil {
		clone.Settings = original.Settings.Clone()
	}

	return &clone
}

// Clone creates a deep copy of the Sticker struct
func (original *Sticker) Clone() *Sticker {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// Clone creates a deep copy of the StickerUser struct
func (original *StickerUser) Clone() *StickerUser {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// Clone creates a deep copy of the Tag struct
func (original *Tag) Clone() *Tag {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// Clone creates a deep copy of the TicketMetrics struct
func (original *TicketMetrics) Clone() *TicketMetrics {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// Clone creates a deep copy of the Ticket struct
func (original *Ticket) Clone() *Ticket {
	if original == nil {
		return nil
	}
	// Create new instance and copy all simple fields
	clone := *original

	// Only handle JSONB fields that need deep cloning

	if original.Metrics != nil {
		clone.Metrics = original.Metrics.Clone()
	}

	return &clone
}

// Clone creates a deep copy of the TicketTicketTopic struct
func (original *TicketTicketTopic) Clone() *TicketTicketTopic {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// Clone creates a deep copy of the TicketTopic struct
func (original *TicketTopic) Clone() *TicketTopic {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// Clone creates a deep copy of the TransferMetrics struct
func (original *TransferMetrics) Clone() *TransferMetrics {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}

// Clone creates a deep copy of the TicketTransfer struct
func (original *TicketTransfer) Clone() *TicketTransfer {
	if original == nil {
		return nil
	}
	// Create new instance and copy all simple fields
	clone := *original

	// Only handle JSONB fields that need deep cloning

	if original.Metrics != nil {
		clone.Metrics = original.Metrics.Clone()
	}

	return &clone
}

// Clone creates a deep copy of the User struct
func (original *User) Clone() *User {
	if original == nil {
		return nil
	}
	// Create new instance and copy all simple fields
	clone := *original

	// Only handle JSONB fields that need deep cloning

	if original.Data != nil {
		clone.Data = make(datatypes.JSON, len(original.Data))
		copy(clone.Data, original.Data)
	}

	if original.ClientsStatus != nil {
		clone.ClientsStatus = make(datatypes.JSON, len(original.ClientsStatus))
		copy(clone.ClientsStatus, original.ClientsStatus)
	}

	return &clone
}

// Clone creates a deep copy of the WhatsappBusinessComponentParameterButton struct
func (original *WhatsappBusinessComponentParameterButton) Clone() *WhatsappBusinessComponentParameterButton {
	if original == nil {
		return nil
	}
	// Create new instance and copy all simple fields
	clone := *original

	// Only handle JSONB fields that need deep cloning

	if original.Example != nil {
		clone.Example = make([]string, len(original.Example))
		copy(clone.Example, original.Example)
	}

	return &clone
}

// Clone creates a deep copy of the WhatsappBusinessComponentExample struct
func (original *WhatsappBusinessComponentExample) Clone() *WhatsappBusinessComponentExample {
	if original == nil {
		return nil
	}
	// Create new instance and copy all simple fields
	clone := *original

	// Only handle JSONB fields that need deep cloning

	if original.HeaderHandle != nil {
		clone.HeaderHandle = make([]string, len(original.HeaderHandle))
		copy(clone.HeaderHandle, original.HeaderHandle)
	}

	if original.HeaderText != nil {
		clone.HeaderText = make([]string, len(original.HeaderText))
		copy(clone.HeaderText, original.HeaderText)
	}

	if original.BodyText != nil {
		clone.BodyText = make([][]string, len(original.BodyText))
		copy(clone.BodyText, original.BodyText)
	}

	return &clone
}

// Clone creates a deep copy of the WhatsappBusinessComponent struct
func (original *WhatsappBusinessComponent) Clone() *WhatsappBusinessComponent {
	if original == nil {
		return nil
	}
	// Create new instance and copy all simple fields
	clone := *original

	// Only handle JSONB fields that need deep cloning

	if original.Buttons != nil {
		clone.Buttons = make([]*WhatsappBusinessComponentParameterButton, len(original.Buttons))
		copy(clone.Buttons, original.Buttons)
	}

	if original.Example != nil {
		clone.Example = original.Example.Clone()
	}

	if original.Params != nil {
		clone.Params = make([]string, len(original.Params))
		copy(clone.Params, original.Params)
	}

	return &clone
}

// Clone creates a deep copy of the WhatsappBusinessTemplate struct
func (original *WhatsappBusinessTemplate) Clone() *WhatsappBusinessTemplate {
	if original == nil {
		return nil
	}
	// Create new instance and copy all simple fields
	clone := *original

	// Only handle JSONB fields that need deep cloning

	// TODO: Components ([]*WhatsappBusinessComponent) may need manual deep copy handling

	return &clone
}

// Clone creates a deep copy of the WhatsappBusinessTemplateHistory struct
func (original *WhatsappBusinessTemplateHistory) Clone() *WhatsappBusinessTemplateHistory {
	if original == nil {
		return nil
	}
	// Create new instance - all fields are simple types
	clone := *original
	return &clone
}
