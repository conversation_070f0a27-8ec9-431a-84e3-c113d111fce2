package models

import (
	"time"

	"github.com/google/uuid"
)

type StickerUser struct {
	StickerId  uuid.UUID  `gorm:"type:uuid;primaryKey" json:"stickerId,omitempty"`
	UserId     uuid.UUID  `gorm:"type:uuid;primaryKey" json:"userId,omitempty"`
	AccountId  uuid.UUID  `gorm:"type:uuid;not null" json:"accountId,omitempty"`
	LastSendAt *time.Time `json:"lastSendAt,omitempty"`
	CreatedAt  *time.Time `json:"createdAt,omitempty"`
	UpdatedAt  *time.Time `json:"updatedAt,omitempty"`
	Sticker    *Sticker   `gorm:"foreignKey:StickerId" json:"sticker,omitempty"`
	User       *User      `gorm:"foreignKey:UserId" json:"user,omitempty"`
	Account    *Account   `gorm:"foreignKey:AccountId" json:"account,omitempty"`
	File       *File      `gorm:"foreignKey:AttachedId;references:StickerId;scope:AttachedType=sticker.file" json:"file,omitempty"`
}

func (StickerUser) TableName() string {
	return "sticker_users"
}
