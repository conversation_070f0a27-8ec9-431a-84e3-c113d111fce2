package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Person struct {
	Id        uuid.UUID       `gorm:"type:uuid;primary_key" json:"id,omitempty"`
	Name      string          `gorm:"type:text" json:"name,omitempty"`
	Document  string          `gorm:"type:text" json:"document,omitempty"`
	AccountId uuid.UUID       `gorm:"type:uuid" json:"accountId,omitempty"`
	Account   *Account        `gorm:"foreignKey:AccountId" json:"account,omitempty"`
	Contacts  []*Contact      `gorm:"foreignKey:PersonId" json:"contacts,omitempty"`
	CreatedAt *time.Time      `json:"createdAt,omitempty"`
	UpdatedAt *time.Time      `json:"updatedAt,omitempty"`
	DeletedAt *gorm.DeletedAt `gorm:"index" json:"deletedAt,omitempty"`
}

func (Person) TableName() string {
	return "people"
}

func (p *Person) BeforeCreate(tx *gorm.DB) (err error) {
	p.Id = uuid.New()
	return
}
