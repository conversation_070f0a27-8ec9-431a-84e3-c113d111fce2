package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

type UserStatusEnum string

const (
	StatusPending  UserStatusEnum = "online"
	StatusApproved UserStatusEnum = "offline"
	StatusRejected UserStatusEnum = "away"
)

type User struct {
	Id                   uuid.UUID      `gorm:"type:uuid;primary_key" json:"id,omitempty"`
	Name                 string         `gorm:"type:text;not null" json:"name,omitempty"`
	Email                string         `gorm:"type:text;not null;uniqueIndex:uq_users_email" json:"email,omitempty"`
	PhoneNumber          string         `gorm:"type:string" json:"phoneNumber,omitempty"`
	Branch               string         `gorm:"type:string" json:"branch,omitempty"`
	Password             string         `gorm:"type:string;not null" json:"password,omitempty"`
	ActivationToken      string         `gorm:"type:string" json:"activationToken,omitempty"`
	ResetPasswordToken   string         `gorm:"type:string" json:"resetPasswordToken,omitempty"`
	IsSuperAdmin         bool           `gorm:"default:false" json:"isSuperAdmin,omitempty"`
	IsClientUser         bool           `gorm:"default:false" json:"isClientUser,omitempty"`
	Active               bool           `gorm:"default:false" json:"active,omitempty"`
	IsFirstLogin         bool           `gorm:"default:true" json:"isFirstLogin,omitempty"`
	AccountId            uuid.UUID      `gorm:"type:uuid;not null" json:"accountId,omitempty"`
	Account              *Account       `gorm:"foreignKey:AccountId" json:"account,omitempty"`
	Departments          []*Department  `gorm:"many2many:user_departments;foreignKey:Id;joinForeignKey:UserId;References:Id;joinReferences:DepartmentId" json:"departments,omitempty"`
	DepartmentsDefault   []*Department  `gorm:"many2many:user_departments;foreignKey:Id;joinForeignKey:UserId;References:Id;joinReferences:DepartmentId" json:"departmentsDefault,omitempty"`
	TimetableId          uuid.UUID      `gorm:"type:uuid;default:null" json:"timetableId,omitempty"`
	Data                 datatypes.JSON `gorm:"type:jsonb;serializer:json;default:'{}'" json:"data,omitempty"`
	InternalChatToken    string         `gorm:"type:text" json:"interndepartmentsDefaultalChatToken,omitempty"`
	ArchivedAt           *time.Time     `gorm:"type:timestamptz" json:"archivedAt,omitempty"`
	CreatedAt            *time.Time     `json:"createdAt,omitempty"`
	UpdatedAt            *time.Time     `json:"updatedAt,omitempty"`
	Status               UserStatusEnum `gorm:"type:enum_users_status; default:'offline'" json:"status,omitempty"`
	ClientsStatus        datatypes.JSON `gorm:"column:clientsStatus;type:jsonb;serializer:json;default:'{}'" json:"clientsStatus,omitempty"`
	OfflineAt            *time.Time     `gorm:"type:timestamptz" json:"offlineAt,omitempty"`
	IsActiveInternalChat bool           `gorm:"default:false" json:"isActiveInternalChat,omitempty"`
	PasswordExpiresAt    *time.Time     `gorm:"type:timestamptz" json:"passwordExpiresAt,omitempty"`
	HasPasswordExpired   bool           `gorm:"-" json:"hasPasswordExpired,omitempty"`
	OtpSecretKey         string         `gorm:"type:string" json:"otpSecretKey,omitempty"`
	OtpAuthActive        bool           `gorm:"default:false" json:"otpAuthActive,omitempty"`
	Language             string         `gorm:"type:string;default:'pt-BR'" json:"language,omitempty"`
	CountTickets         int            `gorm:"-" json:"countTickets,omitempty"`
	//AuthHistory          []*AuthHistory  `gorm:"foreignKey:UserId" json:"authHistory,omitempty"`
	//Schedules            []*Schedule     `gorm:"foreignKey:UserId" json:"schedules,omitempty"`
	//Permissions          []*Permission   `gorm:"-" json:"permissions,omitempty"`
	//Roles                []*Role         `gorm:"many2many:user_roles;foreignKey:Id;joinForeignKey:UserId;References:Id;joinReferences:RoleId" json:"roles,omitempty"`
	//Timetable            *Timetable      `gorm:"foreignKey:TimetableId" json:"timetable,omitempty"`
}

func (User) CreateEnums(db *gorm.DB) error {
	return CreateEnumIfNotExists(db, "enum_users_status", []string{"online", "offline", "away"})
}

func (User) TableName() string {
	return "users"
}

func (u *User) BeforeCreate(tx *gorm.DB) (err error) {
	u.Id = uuid.New()
	return
}
