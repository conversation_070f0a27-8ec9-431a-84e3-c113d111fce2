package models

import (
	"database/sql/driver"
	"testing"
)

func TestJsonbStringSlice_Scan(t *testing.T) {
	tests := []struct {
		name    string
		value   interface{}
		want    JsonbStringSlice
		wantErr bool
	}{
		{
			name:    "scan nil value",
			value:   nil,
			want:    JsonbStringSlice{},
			wantErr: false,
		},
		{
			name:    "scan valid json array",
			value:   []byte(`["item1", "item2", "item3"]`),
			want:    JsonbStringSlice{"item1", "item2", "item3"},
			wantErr: false,
		},
		{
			name:    "scan empty json array",
			value:   []byte(`[]`),
			want:    JsonbStringSlice{},
			wantErr: false,
		},
		{
			name:    "scan invalid type (not []byte)",
			value:   "not a byte slice",
			want:    JsonbStringSlice{},
			wantErr: true,
		},
		{
			name:    "scan invalid json",
			value:   []byte(`invalid json`),
			want:    JsonbStringSlice{},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			var j JsonbStringSlice
			err := j.Scan(tt.value)

			if (err != nil) != tt.wantErr {
				t.<PERSON><PERSON><PERSON>("JsonbStringSlice.Scan() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr {
				if len(j) != len(tt.want) {
					t.Errorf("JsonbStringSlice.Scan() = %v, want %v", j, tt.want)
					return
				}

				for i, v := range j {
					if v != tt.want[i] {
						t.Errorf("JsonbStringSlice.Scan() = %v, want %v", j, tt.want)
						return
					}
				}
			}
		})
	}
}

func TestJsonbStringSlice_Value(t *testing.T) {
	tests := []struct {
		name    string
		j       JsonbStringSlice
		want    driver.Value
		wantErr bool
	}{
		{
			name:    "empty slice",
			j:       JsonbStringSlice{},
			want:    []byte(`[]`),
			wantErr: false,
		},
		{
			name:    "slice with items",
			j:       JsonbStringSlice{"item1", "item2", "item3"},
			want:    []byte(`["item1","item2","item3"]`),
			wantErr: false,
		},
		{
			name:    "nil slice",
			j:       nil,
			want:    []byte(`null`),
			wantErr: false,
		},
		{
			name:    "single item slice",
			j:       JsonbStringSlice{"single"},
			want:    []byte(`["single"]`),
			wantErr: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := tt.j.Value()

			if (err != nil) != tt.wantErr {
				t.Errorf("JsonbStringSlice.Value() error = %v, wantErr %v", err, tt.wantErr)
				return
			}

			if !tt.wantErr {
				gotBytes, ok := got.([]byte)
				if !ok {
					t.Errorf("JsonbStringSlice.Value() returned %T, want []byte", got)
					return
				}

				wantBytes, ok := tt.want.([]byte)
				if !ok {
					t.Errorf("Test case want value is %T, expected []byte", tt.want)
					return
				}

				if string(gotBytes) != string(wantBytes) {
					t.Errorf("JsonbStringSlice.Value() = %s, want %s", string(gotBytes), string(wantBytes))
				}
			}
		})
	}
}

func TestJsonbStringSlice_RoundTrip(t *testing.T) {
	// Teste de ida e volta para garantir que Scan e Value funcionam juntos
	original := JsonbStringSlice{"test1", "test2", "test3"}

	// Converte para valor do driver
	value, err := original.Value()
	if err != nil {
		t.Fatalf("Value() failed: %v", err)
	}

	// Converte de volta para JsonbStringSlice
	var result JsonbStringSlice
	err = result.Scan(value)
	if err != nil {
		t.Fatalf("Scan() failed: %v", err)
	}

	// Verifica se os valores são iguais
	if len(original) != len(result) {
		t.Errorf("Round trip failed: lengths differ. Original: %d, Result: %d", len(original), len(result))
		return
	}

	for i, v := range original {
		if v != result[i] {
			t.Errorf("Round trip failed: values differ at index %d. Original: %s, Result: %s", i, v, result[i])
		}
	}
}
