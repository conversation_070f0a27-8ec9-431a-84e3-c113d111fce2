package telegram_adapter

import (
	"bytes"
	"context"
	"digisac-go/worker/core/adapters/adapter_types"
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/repositories"
	"digisac-go/worker/core/utils/common"
	"errors"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"strconv"
	"time"

	"github.com/PaulSonOfLars/gotgbot/v2"
	"github.com/google/uuid"
	"golang.org/x/sync/singleflight"
	"gorm.io/gorm"
)

type TelegramWebhookPayload struct {
	AccountId string `json:"accountId"`
	ServiceId string `json:"serviceId"`
	Payload   struct {
		EditedMessage   *gotgbot.Message                `json:"edited_message,omitempty"`
		Message         *gotgbot.Message                `json:"message,omitempty"`
		MyChatMember    *any                            `json:"my_chat_member,omitempty"`
		MessageReaction *gotgbot.MessageReactionUpdated `json:"message_reaction,omitempty"`
		UpdateId        int                             `json:"update_id,omitempty"`
	} `json:"payload"`
}

type TelegramAdapter struct {
	ServiceRepo repositories.ServiceRepository
	Clients     map[uuid.UUID]*gotgbot.Bot
	group       singleflight.Group
}

func NewTelegramAdapter(serviceRepo repositories.ServiceRepository) *TelegramAdapter {
	return &TelegramAdapter{
		ServiceRepo: serviceRepo,
		Clients:     make(map[uuid.UUID]*gotgbot.Bot),
		group:       singleflight.Group{},
	}
}

func (a *TelegramAdapter) MakeClient(ctx context.Context, serviceId uuid.UUID) (interface{}, error) {
	slog.InfoContext(ctx, "MakeClient called", slog.String("ServiceId", serviceId.String()))

	service, err := a.ServiceRepo.FindById(ctx, serviceId, repositories.WithQuery(func(db *gorm.DB) *gorm.DB {
		return db.Select("internalData")
	}))
	if err != nil {
		slog.ErrorContext(ctx, "Error finding service", slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return nil, fmt.Errorf("failed to find service with serviceId %s: %w", serviceId, err)
	}

	token := service.InternalData.Token

	client, err := gotgbot.NewBot(token, &gotgbot.BotOpts{
		DisableTokenCheck: true,
	})
	if err != nil {
		slog.ErrorContext(ctx, "Error creating new bot", slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return nil, fmt.Errorf("failed to create bot for serviceId %s: %w", serviceId, err)
	}

	return client, nil
}

func (a *TelegramAdapter) getClient(ctx context.Context, serviceId uuid.UUID) (*gotgbot.Bot, error) {
	// singleflight.Group garante que apenas uma execução aconteça ao mesmo tempo para o mesmo serviceId
	client, err, _ := a.group.Do(serviceId.String(), func() (interface{}, error) {
		if _, ok := a.Clients[serviceId]; !ok {
			client, err := a.MakeClient(ctx, serviceId)
			if err != nil {
				return nil, err
			}

			a.Clients[serviceId] = client.(*gotgbot.Bot)
		}

		return a.Clients[serviceId], nil
	})
	if err != nil {
		slog.ErrorContext(ctx, "Error obtaining client from group", slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return nil, fmt.Errorf("failed to obtain client for serviceId %s: %w", serviceId, err)
	}
	return client.(*gotgbot.Bot), nil
}

func (a *TelegramAdapter) SetWebhook(ctx context.Context, serviceId uuid.UUID, url string) (err error) {
	client, err := a.getClient(ctx, serviceId)
	if err != nil {
		slog.ErrorContext(ctx, "Error getting client in SetWebhook", slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return fmt.Errorf("failed to get client for serviceId %s: %w", serviceId, err)
	}

	slog.InfoContext(ctx, "Deleting webhook", slog.String("serviceId", serviceId.String()))

	ok, err := client.DeleteWebhook(nil)
	if err != nil {
		slog.ErrorContext(ctx, "Error deleting webhook", slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return fmt.Errorf("failed to delete webhook for serviceId %s: %w", serviceId, err)
	}

	if !ok {
		err = errors.New("error deleting webhook")
		slog.ErrorContext(ctx, "Error deleting webhook", slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return fmt.Errorf("failed to delete webhook for serviceId %s: %w", serviceId, err)
	}

	slog.InfoContext(ctx, "Setting up webhook", slog.String("serviceId", serviceId.String()), slog.String("url", url))

	ok, err = client.SetWebhook(url, &gotgbot.SetWebhookOpts{
		RequestOpts:    &gotgbot.RequestOpts{Timeout: 2 * time.Minute},
		AllowedUpdates: []string{"message", "edited_channel_post", "callback_query", "chat_member", "message_reaction", "message_reaction_count"},
	})
	if err != nil {
		slog.ErrorContext(ctx, "Error setting webhook", slog.String("serviceId", serviceId.String()), slog.String("url", url), slog.Any("error", err))
		return fmt.Errorf("failed to set webhook for serviceId %s with url %s: %w", serviceId, url, err)
	}

	if !ok {
		err = errors.New("error setting up webhook")
		slog.ErrorContext(ctx, "Error setting webhook", slog.String("serviceId", serviceId.String()), slog.String("url", url), slog.Any("error", err))
		return fmt.Errorf("failed to set webhook for serviceId %s with url %s: %w", serviceId, url, err)
	}

	return nil
}
func (a *TelegramAdapter) Refresh(ctx context.Context, serviceId uuid.UUID) error  { return nil }
func (a *TelegramAdapter) NewToken(ctx context.Context, serviceId uuid.UUID) error { return nil }
func (a *TelegramAdapter) Takeover(ctx context.Context, serviceId uuid.UUID) error { return nil }

// Telegram não possibilita revogar o token atual.
func (a *TelegramAdapter) Logout(ctx context.Context, serviceId uuid.UUID) (err error) {
	return nil
}

func (a *TelegramAdapter) Shutdown(ctx context.Context, serviceId uuid.UUID) error {
	if a.Clients[serviceId] != nil {
		delete(a.Clients, serviceId)
	}
	return nil
}

func (a *TelegramAdapter) Start(ctx context.Context, serviceId uuid.UUID) (err error) {
	_, err = a.getClient(ctx, serviceId)
	if err != nil {
		slog.ErrorContext(ctx, "Error starting Telegram adapter", slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return fmt.Errorf("failed to start adapter for serviceId %s: %w", serviceId, err)
	}

	return nil
}

func (a *TelegramAdapter) SendReaction(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.SendReactionPayload) (response bool, err error) {
	chatId, err := strconv.ParseInt(payload.To, 10, 64)
	if err != nil {
		slog.ErrorContext(ctx, "Error parsing chatId in SendReaction", slog.String("To", payload.To), slog.Any("error", err))
		return false, fmt.Errorf("failed to parse chatId from payload.To %s: %w", payload.To, err)
	}

	messageId, err := strconv.ParseInt(payload.MessageId, 10, 64)
	if err != nil {
		slog.ErrorContext(ctx, "Error parsing messageId in SendReaction", slog.String("MessageId", payload.MessageId), slog.Any("error", err))
		return false, fmt.Errorf("failed to parse messageId from payload.MessageId %s: %w", payload.MessageId, err)
	}

	client, err := a.getClient(ctx, serviceId)
	if err != nil {
		slog.ErrorContext(ctx, "Error getting client in SendReaction", slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return false, fmt.Errorf("failed to get client for serviceId %s: %w", serviceId, err)
	}

	reaction := []gotgbot.ReactionType{}

	reaction = append(reaction, gotgbot.ReactionTypeEmoji{Emoji: payload.Reaction})

	_, err = client.SetMessageReaction(chatId, messageId, &gotgbot.SetMessageReactionOpts{IsBig: false, Reaction: reaction})
	if err != nil {
		slog.ErrorContext(ctx, "Error setting message reaction", slog.String("serviceId", serviceId.String()), slog.Int64("chatId", chatId), slog.Int64("messageId", messageId), slog.Any("error", err))
		return false, fmt.Errorf("failed to set message reaction for serviceId %s, chatId %d, messageId %d: %w", serviceId, chatId, messageId, err)
	}

	return true, nil
}

func (a *TelegramAdapter) RevokeReaction(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.RevokeReactionPayload) (response bool, err error) {
	chatId, err := strconv.ParseInt(payload.To, 10, 64)
	if err != nil {
		slog.ErrorContext(ctx, "Error parsing chatId in RevokeReaction", slog.String("To", payload.To), slog.Any("error", err))
		return false, fmt.Errorf("failed to parse chatId from payload.To %s: %w", payload.To, err)
	}

	messageId, err := strconv.ParseInt(payload.MessageId, 10, 64)
	if err != nil {
		slog.ErrorContext(ctx, "Error parsing messageId in RevokeReaction", slog.String("MessageId", payload.MessageId), slog.Any("error", err))
		return false, fmt.Errorf("failed to parse messageId from payload.MessageId %s: %w", payload.MessageId, err)
	}

	client, err := a.getClient(ctx, serviceId)
	if err != nil {
		slog.ErrorContext(ctx, "Error getting client in RevokeReaction", slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return false, fmt.Errorf("failed to get client for serviceId %s: %w", serviceId, err)
	}

	response, err = client.SetMessageReaction(chatId, messageId, nil)
	if err != nil {
		slog.ErrorContext(ctx, "Error revoking message reaction", slog.String("serviceId", serviceId.String()), slog.Int64("chatId", chatId), slog.Int64("messageId", messageId), slog.Any("error", err))
		return false, fmt.Errorf("failed to revoke message reaction for serviceId %s, chatId %d, messageId %d: %w", serviceId, chatId, messageId, err)
	}

	return response, nil
}

func (a *TelegramAdapter) SendText(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.SendTextPayload) (sendMessageResponse *adapter_types.SendMessageResponse, err error) {
	chatId, err := strconv.ParseInt(payload.To, 10, 64)
	if err != nil {
		slog.ErrorContext(ctx, "Error parsing chatId in SendText", slog.String("To", payload.To), slog.Any("error", err))
		return sendMessageResponse, fmt.Errorf("failed to parse chatId from payload.To %s: %w", payload.To, err)
	}

	replyParameters, err := getReplyParameters(ctx, payload.ReplyMessageId)
	if err != nil {
		slog.ErrorContext(ctx, "Error getting reply parameters in SendText", slog.String("ReplyMessageId", payload.ReplyMessageId), slog.Any("error", err))
		return sendMessageResponse, fmt.Errorf("failed to get reply parameters for replyMessageId %s: %w", payload.ReplyMessageId, err)
	}

	client, err := a.getClient(ctx, serviceId)
	if err != nil {
		slog.ErrorContext(ctx, "Error getting client in SendText", slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return nil, fmt.Errorf("failed to get client for serviceId %s: %w", serviceId, err)
	}

	response, err := client.SendMessage(chatId, payload.Text, &gotgbot.SendMessageOpts{ReplyParameters: replyParameters})
	if err != nil {
		slog.ErrorContext(ctx, "Error sending text message", slog.String("serviceId", serviceId.String()), slog.Int64("chatId", chatId), slog.Any("error", err))
		return getErrorResponse(ctx, err)
	}

	timestamp := time.Unix(response.Date, 0)

	return &adapter_types.SendMessageResponse{
		MessageId: strconv.FormatInt(response.MessageId, 10),
		Timestamp: &timestamp,
		Ack:       "2",
	}, err
}

func (a *TelegramAdapter) SendAudio(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.SendAudioPayload) (sendMessageResponse *adapter_types.SendMessageResponse, err error) {
	chatId, err := strconv.ParseInt(payload.To, 10, 64)
	if err != nil {
		slog.ErrorContext(ctx, "Error parsing chatId in SendAudio", slog.String("To", payload.To), slog.Any("error", err))
		return nil, fmt.Errorf("failed to parse chatId from payload.To %s: %w", payload.To, err)
	}

	replyParameters, err := getReplyParameters(ctx, payload.ReplyMessageId)
	if err != nil {
		slog.ErrorContext(ctx, "Error getting reply parameters in SendAudio", slog.String("ReplyMessageId", payload.ReplyMessageId), slog.Any("error", err))
		return nil, fmt.Errorf("failed to get reply parameters for replyMessageId %s: %w", payload.ReplyMessageId, err)
	}

	client, err := a.getClient(ctx, serviceId)
	if err != nil {
		slog.ErrorContext(ctx, "Error getting client in SendAudio", slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return nil, fmt.Errorf("failed to get client for serviceId %s: %w", serviceId, err)
	}

	response, err := client.SendVoice(
		chatId,
		gotgbot.InputFileByURL(payload.Url),
		&gotgbot.SendVoiceOpts{
			ReplyParameters: replyParameters,
			Caption:         payload.Caption,
		})
	if err != nil {
		slog.ErrorContext(ctx, "Error sending audio message", slog.String("serviceId", serviceId.String()), slog.Int64("chatId", chatId), slog.String("url", payload.Url), slog.Any("error", err))
		return getErrorResponse(ctx, err)
	}

	timestamp := time.Unix(response.Date, 0)

	return &adapter_types.SendMessageResponse{
		MessageId: strconv.FormatInt(response.MessageId, 10),
		Timestamp: &timestamp,
		Ack:       "2",
	}, nil
}

func (a *TelegramAdapter) SendImage(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.SendImagePayload) (sendMessageResponse *adapter_types.SendMessageResponse, err error) {
	chatId, err := strconv.ParseInt(payload.To, 10, 64)
	if err != nil {
		slog.ErrorContext(ctx, "Error parsing chatId in SendImage", slog.String("To", payload.To), slog.Any("error", err))
		return nil, fmt.Errorf("failed to parse chatId from payload.To %s: %w", payload.To, err)
	}

	replyParameters, err := getReplyParameters(ctx, payload.ReplyMessageId)
	if err != nil {
		slog.ErrorContext(ctx, "Error getting reply parameters in SendImage", slog.String("ReplyMessageId", payload.ReplyMessageId), slog.Any("error", err))
		return nil, fmt.Errorf("failed to get reply parameters for replyMessageId %s: %w", payload.ReplyMessageId, err)
	}

	client, err := a.getClient(ctx, serviceId)
	if err != nil {
		slog.ErrorContext(ctx, "Error getting client in SendImage", slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return nil, fmt.Errorf("failed to get client for serviceId %s: %w", serviceId, err)
	}

	response, err := client.SendPhoto(
		chatId,
		gotgbot.InputFileByURL(payload.Url),
		&gotgbot.SendPhotoOpts{
			ReplyParameters: replyParameters,
			Caption:         payload.Caption,
		})
	if err != nil {
		slog.ErrorContext(ctx, "Error sending image message", slog.String("serviceId", serviceId.String()), slog.Int64("chatId", chatId), slog.String("url", payload.Url), slog.Any("error", err))
		return getErrorResponse(ctx, err)
	}

	timestamp := time.Unix(response.Date, 0)

	return &adapter_types.SendMessageResponse{
		MessageId: strconv.FormatInt(response.MessageId, 10),
		Timestamp: &timestamp,
		Ack:       "2",
	}, nil
}
func (a *TelegramAdapter) SendVideo(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.SendVideoPayload) (sendMessageResponse *adapter_types.SendMessageResponse, err error) {
	chatId, err := strconv.ParseInt(payload.To, 10, 64)
	if err != nil {
		slog.ErrorContext(ctx, "Error parsing chatId in SendVideo", slog.String("To", payload.To), slog.Any("error", err))
		return nil, fmt.Errorf("failed to parse chatId from payload.To %s: %w", payload.To, err)
	}

	replyParameters, err := getReplyParameters(ctx, payload.ReplyMessageId)
	if err != nil {
		slog.ErrorContext(ctx, "Error getting reply parameters in SendVideo", slog.String("ReplyMessageId", payload.ReplyMessageId), slog.Any("error", err))
		return nil, fmt.Errorf("failed to get reply parameters for replyMessageId %s: %w", payload.ReplyMessageId, err)
	}

	client, err := a.getClient(ctx, serviceId)
	if err != nil {
		slog.ErrorContext(ctx, "Error getting client in SendVideo", slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return nil, fmt.Errorf("failed to get client for serviceId %s: %w", serviceId, err)
	}

	response, err := client.SendVideo(
		chatId,
		gotgbot.InputFileByURL(payload.Url),
		&gotgbot.SendVideoOpts{
			ReplyParameters: replyParameters,
			Caption:         payload.Caption,
		})
	if err != nil {
		slog.ErrorContext(ctx, "Error sending video message", slog.String("serviceId", serviceId.String()), slog.Int64("chatId", chatId), slog.String("url", payload.Url), slog.Any("error", err))
		return getErrorResponse(ctx, err)
	}

	timestamp := time.Unix(response.Date, 0)

	return &adapter_types.SendMessageResponse{
		MessageId: strconv.FormatInt(response.MessageId, 10),
		Timestamp: &timestamp,
		Ack:       "2",
	}, nil
}

func (a *TelegramAdapter) SendDocument(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.SendDocumentPayload) (sendMessageResponse *adapter_types.SendMessageResponse, err error) {
	chatId, err := strconv.ParseInt(payload.To, 10, 64)
	if err != nil {
		slog.ErrorContext(ctx, "Error parsing chatId in SendDocument", slog.String("To", payload.To), slog.Any("error", err))
		return nil, fmt.Errorf("failed to parse chatId from payload.To %s: %w", payload.To, err)
	}

	replyParameters, err := getReplyParameters(ctx, payload.ReplyMessageId)
	if err != nil {
		slog.ErrorContext(ctx, "Error getting reply parameters in SendDocument", slog.String("ReplyMessageId", payload.ReplyMessageId), slog.Any("error", err))
		return nil, fmt.Errorf("failed to get reply parameters for replyMessageId %s: %w", payload.ReplyMessageId, err)
	}

	client, err := a.getClient(ctx, serviceId)
	if err != nil {
		slog.ErrorContext(ctx, "Error getting client in SendDocument", slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return nil, fmt.Errorf("failed to get client for serviceId %s: %w", serviceId, err)
	}

	response, err := client.SendDocument(
		chatId,
		gotgbot.InputFileByURL(payload.Url),
		&gotgbot.SendDocumentOpts{
			ReplyParameters: replyParameters,
			Caption:         payload.Caption,
		})

	if err != nil {
		slog.ErrorContext(ctx, "Error sending document message", slog.String("serviceId", serviceId.String()), slog.Int64("chatId", chatId), slog.String("url", payload.Url), slog.Any("error", err))
		return getErrorResponse(ctx, err)
	}

	timestamp := time.Unix(response.Date, 0)

	return &adapter_types.SendMessageResponse{
		MessageId: strconv.FormatInt(response.MessageId, 10),
		Timestamp: &timestamp,
		Ack:       "2",
	}, nil
}

func (a *TelegramAdapter) BuildWebhook(ctx context.Context, serviceId uuid.UUID, payload interface{}) ([]*adapter_types.BuiltWebhook, error) {
	var p *TelegramWebhookPayload

	err := common.ToStruct(ctx, payload, &p)
	if err != nil {
		slog.ErrorContext(ctx, "Error converting payload to TelegramWebhookPayload", slog.Any("payload", payload), slog.Any("error", err))
		return nil, fmt.Errorf("failed to convert payload to TelegramWebhookPayload: %w", err)
	}

	msgType, err := getWebhookMessageType(p)
	if err != nil {
		slog.ErrorContext(ctx, "Error determining webhook message type", slog.Any("payload", p), slog.Any("error", err))
		return nil, fmt.Errorf("failed to determine webhook message type: %w", err)
	}

	if p.Payload.EditedMessage != nil {
		p.Payload.Message = p.Payload.EditedMessage
	}

	if msgType == adapter_types.MESSAGE_TYPE_REACTION {
		msgId := strconv.FormatInt(p.Payload.MessageReaction.MessageId, 10)
		msgTimestamp := time.Unix(p.Payload.MessageReaction.Date, 0)

		contactWebhook := p.Payload.MessageReaction.Chat
		fromWebhook := p.Payload.MessageReaction.User

		contactId := strconv.FormatInt(contactWebhook.Id, 10)
		fromId := strconv.FormatInt(fromWebhook.Id, 10)

		contactName := contactWebhook.FirstName
		fromName := fromWebhook.FirstName

		if contactWebhook.LastName != "" {
			contactName += " " + contactWebhook.LastName
		}

		webhookContact := &adapter_types.WebhookContact{
			Id:              contactId,
			IsGroup:         false,
			Name:            contactName,
			AlternativeName: contactName,
			IsMe:            false,
			Visible:         true,
		}

		webhookFrom := &adapter_types.WebhookContact{
			Id:              fromId,
			IsGroup:         false,
			Name:            contactName,
			AlternativeName: fromName,
			IsMe:            false,
			Visible:         false,
		}

		newReactions := make([]*adapter_types.Reaction, len(p.Payload.MessageReaction.NewReaction))

		for i, newReaction := range p.Payload.MessageReaction.NewReaction {
			newReactions[i] = &adapter_types.Reaction{
				Emoji: newReaction.MergeReactionType().Emoji,
			}
		}

		// Necessário para remoção da reação
		if len(newReactions) == 0 {
			newReactions = append(newReactions, &adapter_types.Reaction{})
		}

		return []*adapter_types.BuiltWebhook{{
			Reaction: &adapter_types.WebhookReaction{
				NewReactions: newReactions,
				Timestamp:    &msgTimestamp,
				// IsFromMe: , Não recebemos informação suficiente para isso
			},
			Message: &adapter_types.WebhookMessage{
				Id:       msgId,
				Type:     "reaction",
				IsFromMe: false, // Sempre é false, só recebemos webhooks de reações do contato
			},
			Contact: webhookContact,
			From:    webhookFrom,
		}}, nil
	}

	contactWebhook := p.Payload.Message.Chat
	fromWebhook := p.Payload.Message.From

	contactId := strconv.FormatInt(contactWebhook.Id, 10)
	fromId := strconv.FormatInt(fromWebhook.Id, 10)

	contactName := ""
	fromName := fromWebhook.FirstName

	if contactWebhook.Type == "group" || contactWebhook.Type == "supergroup" {
		contactName = contactWebhook.Title
	} else {
		contactName = contactWebhook.FirstName

		if contactWebhook.LastName != "" {
			contactName += " " + contactWebhook.LastName
		}
	}

	webhookContact := &adapter_types.WebhookContact{
		Id:              contactId,
		IsGroup:         contactWebhook.Type == "group",
		Name:            contactName,
		AlternativeName: contactName,
		IsMe:            false,
		Visible:         true,
	}

	webhookFrom := &adapter_types.WebhookContact{
		Id:              fromId,
		IsGroup:         false,
		Name:            fromName,
		AlternativeName: fromName,
		IsMe:            false,
		Visible:         false,
	}

	msgId := strconv.FormatInt(p.Payload.Message.GetMessageId(), 10)
	msgText := p.Payload.Message.GetText()
	msgTimestamp := time.Unix(p.Payload.Message.GetDate(), 0)

	var replyMessageId string

	if p.Payload.Message.ReplyToMessage != nil {
		replyMessageId = strconv.FormatInt(p.Payload.Message.ReplyToMessage.MessageId, 10)
	}

	file, err := a.getFile(ctx, serviceId, p, msgType)
	if err != nil {
		slog.ErrorContext(ctx, "Error getting file for webhook", slog.String("serviceId", serviceId.String()), slog.Any("msgType", msgType), slog.Any("error", err))
		return nil, fmt.Errorf("failed to get file for serviceId %s and message type %s: %w", serviceId, msgType, err)
	}

	var location *adapter_types.WebhookMessageLocation

	if msgType == "location" {
		location = &adapter_types.WebhookMessageLocation{
			Lat:        p.Payload.Message.Location.Latitude,
			Lng:        p.Payload.Message.Location.Longitude,
			PreviewUrl: "",
		}
	}

	var vCard string

	// No telegram multi_vcard é enviado separadamente
	if string(msgType) == "vcard" {
		vCard = p.Payload.Message.Contact.Vcard
	}

	webhook := []*adapter_types.BuiltWebhook{{
		Message: &adapter_types.WebhookMessage{
			Id:             msgId,
			Type:           string(msgType),
			Text:           msgText,
			Timestamp:      &msgTimestamp,
			File:           file,
			ReplyMessageId: replyMessageId,
			IsFromMe:       fromId == contactId && fromWebhook.IsBot,
			Location:       location,
			VCard:          []*models.MessageDataVCard{{VCard: vCard}},
		},
		Contact: webhookContact,
		From:    webhookFrom,
	}}

	return webhook, nil
}

func (a *TelegramAdapter) DownloadMedia(ctx context.Context, serviceId uuid.UUID, mediaId string, mediaUrl string) (*adapter_types.DownloadMediaResponse, error) {
	client, err := a.getClient(ctx, serviceId)
	if err != nil {
		slog.ErrorContext(ctx, "Error getting client in DownloadMedia", slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return nil, fmt.Errorf("failed to get client for serviceId %s: %w", serviceId, err)
	}

	file, err := client.GetFile(mediaId, &gotgbot.GetFileOpts{
		RequestOpts: &gotgbot.RequestOpts{Timeout: 2 * time.Minute},
	})
	if err != nil {
		slog.ErrorContext(ctx, "Error getting file", slog.String("mediaId", mediaId), slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return nil, fmt.Errorf("failed to get file for mediaId %s on serviceId %s: %w", mediaId, serviceId, err)
	}

	url := file.URL(client, &gotgbot.RequestOpts{
		Timeout: 2 * time.Minute,
	})

	resp, err := http.Get(url)
	if err != nil {
		slog.ErrorContext(ctx, "Error downloading media", slog.String("url", url), slog.Any("error", err))
		return nil, fmt.Errorf("failed to download media from url %s: %w", url, err)
	}

	defer func() {
		if err := resp.Body.Close(); err != nil {
			fmt.Println("Error closing response body:", err)
		}
	}()

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		slog.ErrorContext(ctx, "Error reading response body", slog.String("url", url), slog.Any("error", err))
		return nil, fmt.Errorf("failed to read media data from url %s: %w", url, err)
	}

	response := &adapter_types.DownloadMediaResponse{
		Type:   "stream",
		Stream: bytes.NewReader(data),
	}

	return response, nil
}

func (a *TelegramAdapter) LoadEarlierMessages(ctx context.Context, serviceId uuid.UUID, loadPayload *adapter_types.LoadEarlierMessagesPayload) ([]*adapter_types.BuiltWebhook, error) {
	return []*adapter_types.BuiltWebhook{}, nil
}

func (a *TelegramAdapter) ForwardMessage(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.ForwardMessagePayload) (*adapter_types.ForwardMessageResponse, error) {
	return nil, nil
}

func (a *TelegramAdapter) SendTemplate(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.SendTemplatePayload) (*adapter_types.SendMessageResponse, error) {
	return nil, nil
}

func (a *TelegramAdapter) SendInteractiveMessage(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.SendInteractiveMessagePayload) (*adapter_types.SendMessageResponse, error) {
	return nil, nil
}

func (a *TelegramAdapter) SendSticker(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.SendStickerPayload) (*adapter_types.SendMessageResponse, error) {
	return nil, nil
}

func (a *TelegramAdapter) GetTemplates(ctx context.Context, serviceId uuid.UUID) ([]*models.WhatsappBusinessTemplate, error) {
	return nil, nil
}

func (a *TelegramAdapter) CreateTemplate(ctx context.Context, serviceId uuid.UUID, templateId uuid.UUID) (*models.WhatsappBusinessTemplate, error) {
	return nil, nil
}

func (a *TelegramAdapter) DeleteTemplate(ctx context.Context, serviceId uuid.UUID, templateId string) error {
	return nil
}

func (a *TelegramAdapter) SendVCards(ctx context.Context, serviceId uuid.UUID, payload *adapter_types.SendVCardsPayload) (*adapter_types.SendMessageResponse, error) {
	return nil, nil
}
