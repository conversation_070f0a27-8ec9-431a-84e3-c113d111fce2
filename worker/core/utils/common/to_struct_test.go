//go:build unit

package common

import (
	"context"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
)

func TestToStruct(t *testing.T) {
	type TargetStruct struct {
		Name string `json:"name"`
		Age  int    `json:"age"`
	}

	type SourceStruct struct {
		Name string `json:"name"`
		Age  int    `json:"age"`
	}

	tests := []struct {
		name        string
		payload     any
		target      any
		wantErr     bool
		expected    any
		checkResult bool // Flag to indicate if we should check the result
	}{
		{
			name:    "successful conversion from map",
			payload: map[string]interface{}{"name": "<PERSON>", "age": 30},
			target:  &TargetStruct{},
			wantErr: false,
			expected: &TargetStruct{
				Name: "John",
				Age:  30,
			},
			checkResult: true,
		},
		{
			name:        "successful conversion from struct",
			payload:     SourceStruct{Name: "Jane", Age: 25},
			target:      &TargetStruct{},
			wantErr:     false,
			expected:    &TargetStruct{Name: "Jane", Age: 25},
			checkResult: true,
		},
		{
			name:        "marshal error with unmarshallable type",
			payload:     make(chan int),
			target:      &TargetStruct{},
			wantErr:     true,
			checkResult: false,
		},
		{
			name:        "unmarshal error with type mismatch",
			payload:     map[string]interface{}{"name": "Peter", "age": "thirty"},
			target:      &TargetStruct{},
			wantErr:     true,
			checkResult: false,
		},
		{
			name:        "nil payload",
			payload:     nil,
			target:      &TargetStruct{},
			wantErr:     false,
			expected:    &TargetStruct{}, // Expect zero-valued struct
			checkResult: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			err := ToStruct(context.Background(), tt.payload, tt.target)

			if tt.wantErr {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
				if tt.checkResult {
					assert.Equal(t, tt.expected, tt.target)
				}
			}
		})
	}
}
