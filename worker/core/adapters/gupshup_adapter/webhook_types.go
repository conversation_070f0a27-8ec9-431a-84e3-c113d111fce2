package gupshup_adapter

type GupshupTemplateEventType string

const (
	<PERSON>upshupTemplateEventTypeStatusUpdate  GupshupTemplateEventType = "status-update"
	GupshupTemplateEventTypeQualityUpdate GupshupTemplateEventType = "quality-update"
)

type GupshupTemplateWebhookPayload struct {
	Id             string                   `json:"id"`           // Id do template no Gupshup
	ElementName    string                   `json:"elementName"`  // Nome do template
	LanguageCode   string                   `json:"languageCode"` // Código do idioma
	Type           GupshupTemplateEventType `json:"type"`         // Tipo do evento
	Status         string                   `json:"status,omitempty"`
	RejectedReason string                   `json:"rejectedReason,omitempty"`
	Quality        string                   `json:"quality,omitempty"`
}

type GupshupWebhookType string

const (
	GupshupWebhookTypeTemplateEvent GupshupWebhookType = "template-event"
)

// GupshupTemplateWebhook representa o payload de webhook para atualizações de template
type GupshupTemplateWebhook struct {
	App       string                         `json:"app"`       // Nome da aplicação
	Timestamp int64                          `json:"timestamp"` // Timestamp do evento
	Version   int                            `json:"version"`   // Versão do webhook
	Type      GupshupWebhookType             `json:"type"`      // Tipo do webhook: "template-event"
	Payload   *GupshupTemplateWebhookPayload `json:"payload"`   // Payload do evento
}
