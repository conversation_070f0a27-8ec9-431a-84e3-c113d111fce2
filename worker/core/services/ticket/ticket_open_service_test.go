//go:build integration
// +build integration

package ticket_test

import (
	"context"
	"fmt"
	"testing"
	"time"

	"digisac-go/worker/core/models"
	"digisac-go/worker/core/repositories"
	ticketServices "digisac-go/worker/core/services/ticket"

	"github.com/go-faker/faker/v4"
	"github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/suite"
)

type TicketOpenServiceIntegrationTestSuite struct {
	suite.Suite
	ticketOpenService    *ticketServices.TicketOpenService
	ticketService        *ticketServices.TicketService
	redisClient          *redis.Client
	accountRepository    repositories.AccountRepository
	messageRepository    repositories.MessageRepository
	contactRepository    repositories.ContactRepository
	ticketRepository     repositories.TicketRepository
	serviceRepository    repositories.ServiceRepository
	departmentRepository repositories.DepartmentRepository
	userRepository       repositories.UserRepository
}

func (suite *TicketOpenServiceIntegrationTestSuite) SetupSuite() {
	// Skip tests that require database connection
	suite.T().Skip("Skipping test that requires database connection")
}

func (suite *TicketOpenServiceIntegrationTestSuite) TestGenerateProtocolAndCount() {
	account, err := suite.accountRepository.FindOne(context.Background())
	suite.Require().NoError(err)

	var max int

	max, _ = suite.redisClient.Get(context.Background(), "tickets-protocol-count:"+account.Id.String()).Int()

	suite.Require().NoError(err)

	if max == 0 {
		max, _ = suite.ticketRepository.Max(context.Background(), "count", repositories.WithQueryStruct(
			map[string]interface{}{
				"accountId": account.Id,
			},
		))
	}

	suite.Require().NoError(err)

	tx := suite.accountRepository.BeginTransaction()
	defer tx.Rollback()

	protocol, count, err := suite.ticketOpenService.GenerateProtocolAndCount(context.Background(), account, tx)

	suite.Require().NoError(err)

	suite.Require().NoError(err)
	suite.Require().Equal(time.Now().Format("********")+fmt.Sprint(max+1), protocol)
	suite.Require().Equal(max+1, count)
}

func (suite *TicketOpenServiceIntegrationTestSuite) TestOpenTicket() {
	var err error
	tx := suite.accountRepository.BeginTransaction()
	defer tx.Rollback()

	account, _ := suite.accountRepository.FindOne(context.Background())

	service, _ := suite.serviceRepository.FindOne(context.Background(), repositories.WithQueryStruct(map[string]interface{}{
		"accountId": account.Id,
	}))
	department, _ := suite.departmentRepository.FindOne(context.Background(), repositories.WithQueryStruct(map[string]interface{}{
		"accountId": account.Id,
	}))
	user, _ := suite.userRepository.FindOne(context.Background(), repositories.WithQueryStruct(map[string]interface{}{
		"accountId": account.Id,
	}))

	contact := &models.Contact{
		Name:      faker.Name(),
		AccountId: account.Id,
		ServiceId: service.Id,
		Origin:    models.ContactOriginWeb,
	}
	err = suite.contactRepository.Create(context.Background(), contact, nil, repositories.WithTx(tx))
	suite.Require().NoError(err)

	now := time.Now()

	message := &models.Message{
		Text:          "This is a test message",
		IdFromService: faker.Phonenumber(),
		Type:          "chat",
		Timestamp:     &now,
		FromId:        contact.Id,
		ToId:          contact.Id,
		ContactId:     contact.Id,
		AccountId:     account.Id,
		ServiceId:     service.Id,
	}
	err = suite.messageRepository.Create(context.Background(), message, nil, repositories.WithTx(tx))
	suite.Require().NoError(err)

	data := &ticketServices.OpenData{
		Origin:       "manual",
		Contact:      contact,
		Message:      message,
		DepartmentId: department.Id,
		ByUserId:     user.Id,
	}
	result, err := suite.ticketService.TicketOpenService.OpenTicket(context.Background(), data, tx, nil)

	suite.Require().NoError(err)
	suite.Require().NotNil(result)
	suite.Require().Equal(contact.Id, result.UpdatedContact.Id)
}

func TestTicketOpenServiceIntegrationTestSuite(t *testing.T) {
	suite.Run(t, new(TicketOpenServiceIntegrationTestSuite))
}
