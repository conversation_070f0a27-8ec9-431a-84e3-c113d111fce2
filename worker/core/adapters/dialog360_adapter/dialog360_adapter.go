package dialog360_adapter

import (
	"bytes"
	"context"
	"digisac-go/worker/config"
	"digisac-go/worker/core/adapters/adapter_types"
	"digisac-go/worker/core/adapters/base_waba_adapter"
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/repositories"
	"digisac-go/worker/core/services/storage"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"strings"

	"github.com/google/uuid"
)

type MediaResponse struct {
	MessagingProduct string `json:"messaging_product"`
	URL              string `json:"url"`
	MimeType         string `json:"mime_type"`
	Sha256           string `json:"sha256"`
	FileSize         int    `json:"file_size"`
	Id               string `json:"id"`
}

type Dialog360Adapter struct {
	*base_waba_adapter.BaseWabaAdapter
	apiUrl             string
	templateRepository repositories.WhatsappBusinessTemplateRepository
	fileRepository     repositories.FileRepository
	storageService     *storage.StorageService
}

func NewDialog360Adapter(
	serviceRepository repositories.ServiceRepository,
	templateRepository repositories.WhatsappBusinessTemplateRepository,
	fileRepository repositories.FileRepository,
	config *config.Config,
	storageService *storage.StorageService,
) *Dialog360Adapter {
	dialog360Adapter := &Dialog360Adapter{
		apiUrl:             "https://waba-v2.360dialog.io",
		templateRepository: templateRepository,
		fileRepository:     fileRepository,
		storageService:     storageService,
	}

	dialog360Adapter.BaseWabaAdapter = base_waba_adapter.NewBaseWabaAdapter(
		serviceRepository,
		templateRepository,
		fileRepository,
		storageService,
		config,
		dialog360Adapter)

	return dialog360Adapter
}

func (a *Dialog360Adapter) GetMessageApiUrl(ctx context.Context, service *models.Service) (string, error) {
	return a.apiUrl + "/messages", nil
}

func (a *Dialog360Adapter) GetMessageApiHeaders(ctx context.Context, service *models.Service) (map[string]string, error) {
	return map[string]string{
		"Content-Type": "application/json",
		"D360-API-KEY": service.InternalData.Token,
	}, nil
}

// https://docs.360dialog.com/docs/waba-messaging/template-messaging#get-template-list
func (a *Dialog360Adapter) GetTemplatesApiUrl(ctx context.Context, service *models.Service) (string, error) {
	return a.apiUrl + "/v1/configs/templates", nil
}

// https://docs.360dialog.com/docs/waba-messaging/template-messaging#delete-a-template
func (a *Dialog360Adapter) DeleteTemplateApiUrl(ctx context.Context, service *models.Service, templateName string) (string, error) {
	return a.apiUrl + "/v1/configs/templates/" + templateName, nil
}

func (a *Dialog360Adapter) DeleteTemplateApiHeaders(ctx context.Context, service *models.Service) (map[string]string, error) {
	return map[string]string{
		"D360-API-KEY": service.InternalData.Token,
	}, nil
}

// https://docs.360dialog.com/docs/waba-messaging/webhook#set-webhook-url-for-phone-number
func (a *Dialog360Adapter) SetWebhook(ctx context.Context, serviceId uuid.UUID, url string) (err error) {
	slog.InfoContext(ctx, "SetWebhook called", slog.String("serviceId", serviceId.String()), slog.String("url", url))

	service, err := a.ServiceRepository.FindById(ctx, serviceId)

	if err != nil {
		return fmt.Errorf("failed to find service with serviceId %s: %w", serviceId, err)
	}

	client, err := a.GetClient(ctx, serviceId)

	if err != nil {
		return fmt.Errorf("failed to get client: %w", err)
	}

	requestUrl := a.apiUrl + "/v1/configs/webhook"

	reqBody := struct {
		Url string `json:"url"`
	}{
		Url: url,
	}

	jsonBody, err := json.Marshal(reqBody)

	if err != nil {
		return fmt.Errorf("failed to marshal request body: %w", err)
	}

	req, err := http.NewRequest("POST", requestUrl, bytes.NewBuffer(jsonBody))

	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Add("accept", "application/json")
	req.Header.Add("D360-API-KEY", service.InternalData.Token)
	req.Header.Add("Content-Type", "application/json")

	response, err := client.Do(req)

	if err != nil {
		return fmt.Errorf("failed to send request: %w", err)
	}

	defer func() {
		if err := response.Body.Close(); err != nil {
			fmt.Println("Error closing response body:", err)
		}
	}()

	body, err := io.ReadAll(response.Body)

	if err != nil {
		return fmt.Errorf("failed to read response body: %w", err)
	}

	slog.DebugContext(ctx, "SetWebhook response", slog.String("body", string(body)))

	if response.StatusCode != 200 {
		return fmt.Errorf("failed to set webhook: %d", response.StatusCode)
	}

	return nil
}

func (a *Dialog360Adapter) DownloadMedia(ctx context.Context, serviceId uuid.UUID, mediaId string, mediaUrl string) (*adapter_types.DownloadMediaResponse, error) {
	client, err := a.GetClient(ctx, serviceId)
	if err != nil {
		return nil, fmt.Errorf("failed to get client for serviceId %s: %w", serviceId, err)
	}

	service, err := a.ServiceRepository.FindById(ctx, serviceId)
	if err != nil {
		return nil, fmt.Errorf("failed to find service with serviceId %s: %w", serviceId, err)
	}

	url := a.apiUrl + "/" + mediaId

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Add("accept", "application/json")
	req.Header.Add("D360-API-KEY", service.InternalData.Token)
	req.Header.Add("Content-Type", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}

	defer func() {
		if err := resp.Body.Close(); err != nil {
			fmt.Println("Error closing response body:", err)
		}
	}()

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	var mediaResponse *MediaResponse
	err = json.Unmarshal(data, &mediaResponse)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal response body: %w", err)
	}

	urlParts := strings.Split(mediaResponse.URL, "?")
	if len(urlParts) < 2 {
		return nil, fmt.Errorf("invalid media URL format: %s", mediaResponse.URL)
	}

	newUrl := a.apiUrl + "/whatsapp_business/attachments/?" + urlParts[1]

	req, err = http.NewRequest("GET", newUrl, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create download request: %w", err)
	}

	req.Header.Add("D360-API-KEY", service.InternalData.Token)
	req.Header.Set("Accept", "*/*")

	resp, err = client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to download media from url %s: %w", newUrl, err)
	}

	defer func() {
		if err := resp.Body.Close(); err != nil {
			fmt.Println("Error closing response body:", err)
		}
	}()

	data, err = io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read media data: %w", err)
	}

	response := &adapter_types.DownloadMediaResponse{
		Type:   "stream",
		Stream: bytes.NewReader(data),
	}

	return response, nil
}

func (a *Dialog360Adapter) GetMediaExampleUrl(ctx context.Context, serviceId uuid.UUID, templateId uuid.UUID) (url string, err error) {
	file, err := a.fileRepository.FindOne(ctx, repositories.WithQueryStruct(map[string]interface{}{
		"attachedId":   templateId,
		"attachedType": "hsm.file",
	}))

	if err != nil {
		return "", fmt.Errorf("failed to find file: %w", err)
	}

	fileUrl, err := a.storageService.GetPresignedUrl(ctx, file, nil)

	if err != nil {
		return "", fmt.Errorf("failed to get presigned URL for template example: %w", err)
	}

	return fileUrl, nil
}
