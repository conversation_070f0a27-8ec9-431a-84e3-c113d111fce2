package crypto

import (
	"context"
	"crypto/cipher"
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"log/slog"

	configApp "digisac-go/worker/config"
	"digisac-go/worker/core/models"
	cryptor "digisac-go/worker/core/utils/crypto_utils"
)

var AppEncryptionKey []byte

type AccountCryptor struct {
	config *configApp.Config
}

func NewAccountCryptor(config *configApp.Config) *AccountCryptor {
	return &AccountCryptor{
		config: config,
	}
}

func (a *AccountCryptor) GetAppEncryptionKey(ctx context.Context) ([]byte, error) {
	if len(AppEncryptionKey) > 0 {
		return AppEncryptionKey, nil
	}

	encryptionKeyHex := a.config.EncryptionKey

	AppEncryptionKey, err := hex.DecodeString(encryptionKeyHex)

	if err != nil {
		slog.InfoContext(ctx, "Failed to decode encryption key",
			slog.String("encryptionKeyHex", encryptionKeyHex),
			slog.String("AppEncryptionKey", string(AppEncryptionKey)),
			slog.String("error", err.Error()))

		return nil, fmt.Errorf("failed to decode encryption key: %w", err)
	}

	return AppEncryptionKey, nil
}

func (a *AccountCryptor) CreateEncryptionKey(ctx context.Context) ([]byte, error) {
	key := make([]byte, 32)
	_, err := rand.Read(key)
	if err != nil {
		slog.InfoContext(ctx, "Failed to generate encryption key", slog.String("key", string(key)), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to generate encryption key: %w", err)
	}
	return key, nil
}

func (a *AccountCryptor) CreateEncryptedEncryptionKey(ctx context.Context) ([]byte, error) {
	encryptionKey, err := a.CreateEncryptionKey(ctx)
	if err != nil {
		slog.InfoContext(ctx, "Failed to create encryption key", slog.String("encryptionKey", string(encryptionKey)))
		return nil, fmt.Errorf("failed to create encryption key: %w", err)
	}

	appEncryptionKey, err := a.GetAppEncryptionKey(ctx)

	if err != nil {
		slog.InfoContext(ctx, "Failed to get app encryption key",
			slog.String("encryptionKey", string(encryptionKey)),
			slog.String("appEncryptionKey", string(appEncryptionKey)))

		return nil, fmt.Errorf("failed to get app encryption key: %w", err)
	}

	encryptedKey, err := cryptor.EncryptBufferWithConcatenatedIv(encryptionKey, appEncryptionKey)

	if err != nil {
		slog.InfoContext(ctx, "Failed to encrypt buffer with appEncryptionKey",
			slog.String("encryptionKey", string(encryptedKey)),
			slog.String("appEncryptionKey", string(appEncryptionKey)),
			slog.String("err", err.Error()),
		)

		return nil, fmt.Errorf("failed to encrypt key: %w", err)
	}

	return encryptedKey, nil

}

func (a *AccountCryptor) DecryptEncryptionKey(ctx context.Context, encryptedKey []byte) ([]byte, error) {
	encryptionKeyHex := a.config.EncryptionKey

	appEncryptionKey, err := hex.DecodeString(encryptionKeyHex)

	if err != nil {
		slog.InfoContext(ctx, "Failed to decrypt encryption key",
			slog.String("encryptionKey", string(encryptedKey)),
			slog.String("appEncryptionKey", string(appEncryptionKey)),
			slog.String("err", err.Error()),
		)
		return nil, fmt.Errorf("failed to decrypt encryption key: %w", err)
	}

	decryptedEncryptionKey, err := cryptor.DecryptBufferWithConcatenatedIv(encryptedKey, appEncryptionKey)

	if err != nil {
		slog.InfoContext(ctx, "Failed to decrypt encryption key with appEncryptionKey",
			slog.String("encryptionKey", string(encryptedKey)),
			slog.String("appEncryptionKey", string(appEncryptionKey)),
			slog.String("err", err.Error()),
		)
		return nil, fmt.Errorf("failed to decrypt encryption key with appEncryptionKey: %w", err)
	}

	return decryptedEncryptionKey, nil
}

func (a *AccountCryptor) GetAccountEncryptionKey(ctx context.Context, account *models.Account) ([]byte, error) {
	if account == nil {
		slog.InfoContext(ctx, "Account is nil", slog.String("error", "account is required"))
		return nil, fmt.Errorf("account is required")
	}

	if account.EncryptionKey == "" {
		if !account.Settings.EncryptionDisabled {
			slog.InfoContext(ctx, "No encryptionKey on account", slog.String("accountId", account.Id.String()))
			return nil, fmt.Errorf("no encryptionKey on account #%s", account.Id)
		}
	}

	encryptedKey, err := hex.DecodeString(account.EncryptionKey)

	if err != nil {
		slog.InfoContext(ctx, "Failed to decode account encryption key",
			slog.String("accountId", account.Id.String()),
			slog.String("encryptionKey", account.EncryptionKey),
			slog.String("err", err.Error()))
		return nil, fmt.Errorf("failed to decode account encryption key %v %w", account.EncryptionKey, err)
	}

	return a.DecryptEncryptionKey(ctx, encryptedKey)
}

func (a *AccountCryptor) EncryptTextForAccount(ctx context.Context, account *models.Account, text string, force bool) (string, error) {
	if !force && account.Settings.EncryptionDisabled {
		return text, nil
	}

	encryptionKey, err := a.GetAccountEncryptionKey(ctx, account)

	if err != nil {
		slog.InfoContext(ctx, "Failed to get account encryption key",
			slog.String("accountId", account.Id.String()),
			slog.String("error", err.Error()))
		return "", fmt.Errorf("failed to get account encryption key, accountId %s, error %w", account.Id, err)
	}

	encryptedText, err := cryptor.EncryptText(text, encryptionKey)
	if err != nil {
		slog.InfoContext(ctx, "Failed to encrypt text for account",
			slog.String("accountId", account.Id.String()),
			slog.String("text", text),
			slog.String("error", err.Error()))
		return "", fmt.Errorf("failed to encrypt text %s %w", text, err)
	}

	return encryptedText, nil
}

func (a *AccountCryptor) DecryptTextForAccount(ctx context.Context, account *models.Account, text string, force bool) (string, error) {
	if !force && account.Settings.EncryptionDisabled {
		return text, nil
	}

	encryptionKey, err := a.GetAccountEncryptionKey(ctx, account)
	if err != nil {
		slog.InfoContext(ctx, "Failed to get account encryption key", slog.String("accountId", account.Id.String()), slog.String("error", err.Error()))
		return "", fmt.Errorf("failed to get account encryption key: %w", err)
	}

	decryptedText, err := cryptor.DecryptText(text, encryptionKey)
	if err != nil {
		slog.InfoContext(ctx, "Failed to decrypt text for account",
			slog.String("accountId", account.Id.String()),
			slog.String("text", text),
			slog.String("error", err.Error()))
		return "", fmt.Errorf("failed to decrypt text: %w", err)
	}

	return decryptedText, nil
}

func (a *AccountCryptor) EncryptBufferForAccountWithConcatenatedIv(ctx context.Context, account *models.Account, buffer []byte) ([]byte, error) {
	encryptionKey, err := a.GetAccountEncryptionKey(ctx, account)
	if err != nil {
		slog.InfoContext(ctx, "Failed to get account encryption key", slog.String("accountId", account.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to get account encryption key: %w", err)
	}

	encryptedBuffer, err := cryptor.EncryptBufferWithConcatenatedIv(buffer, encryptionKey)
	if err != nil {
		slog.InfoContext(ctx, "Failed to encrypt buffer for account with concatenated IV",
			slog.String("accountId", account.Id.String()),
			slog.String("buffer", string(buffer)),
			slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to encrypt buffer: %w", err)
	}

	return encryptedBuffer, nil
}

func (a *AccountCryptor) DecryptBufferForAccountWithConcatenatedIv(ctx context.Context, account *models.Account, buffer []byte) ([]byte, error) {
	encryptionKey, err := a.GetAccountEncryptionKey(ctx, account)
	if err != nil {
		slog.InfoContext(ctx, "Failed to get account encryption key", slog.String("accountId", account.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to get account encryption key: %w", err)
	}

	decryptedBuffer, err := cryptor.DecryptBufferWithConcatenatedIv(buffer, encryptionKey)
	if err != nil {
		slog.InfoContext(ctx, "Failed to decrypt buffer for account with concatenated IV",
			slog.String("accountId", account.Id.String()),
			slog.String("buffer", string(buffer)),
			slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to decrypt buffer: %w", err)
	}

	return decryptedBuffer, nil
}

func (a *AccountCryptor) EncryptBufferForAccountWithSeparatedIv(ctx context.Context, account *models.Account, buffer []byte) ([]byte, []byte, error) {
	encryptionKey, err := a.GetAccountEncryptionKey(ctx, account)
	if err != nil {
		slog.InfoContext(ctx, "Failed to get account encryption key", slog.String("accountId", account.Id.String()), slog.String("error", err.Error()))
		return nil, nil, fmt.Errorf("failed to get account encryption key: %w", err)
	}

	encryptedBuffer, iv, err := cryptor.EncryptBufferWithSeparatedIv(buffer, encryptionKey)
	if err != nil {
		slog.InfoContext(ctx, "Failed to encrypt buffer for account with separated IV",
			slog.String("accountId", account.Id.String()),
			slog.String("buffer", string(buffer)),
			slog.String("error", err.Error()))
		return nil, nil, fmt.Errorf("failed to encrypt buffer: %w", err)
	}

	return encryptedBuffer, iv, nil
}

func (a *AccountCryptor) DecryptBufferForAccountWithSeparatedIv(ctx context.Context, account *models.Account, buffer []byte, iv []byte) ([]byte, error) {
	encryptionKey, err := a.GetAccountEncryptionKey(ctx, account)
	if err != nil {
		slog.InfoContext(ctx, "Failed to get account encryption key", slog.String("accountId", account.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to get account encryption key: %w", err)
	}

	decryptedBuffer, err := cryptor.DecryptBufferWithSeparatedIv(buffer, iv, encryptionKey)

	if err != nil {
		slog.InfoContext(ctx, "Failed to decrypt buffer for account with separated IV",
			slog.String("accountId", account.Id.String()),
			slog.String("buffer", string(buffer)),
			slog.String("iv", string(iv)),
			slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to decrypt buffer: %w", err)
	}

	return decryptedBuffer, nil
}

func (a *AccountCryptor) CreateCipherWithIvForAccount(ctx context.Context, account *models.Account) (cipher.Stream, []byte, error) {
	encryptionKey, err := a.GetAccountEncryptionKey(ctx, account)
	if err != nil {
		slog.InfoContext(ctx, "Failed to get account encryption key", slog.String("accountId", account.Id.String()), slog.String("error", err.Error()))
		return nil, nil, fmt.Errorf("failed to get account encryption key: %w", err)
	}

	streamCipher, iv, err := cryptor.CreateCipherWithIv(encryptionKey)
	if err != nil {
		slog.InfoContext(ctx, "Failed to create cipher with IV for account",
			slog.String("accountId", account.Id.String()),
			slog.String("error", err.Error()))
		return nil, nil, fmt.Errorf("failed to create cipher with IV: %w", err)
	}

	return streamCipher, iv, nil
}

func (a *AccountCryptor) CreateDecipherForAccount(ctx context.Context, iv []byte, account *models.Account) (cipher.Stream, error) {
	encryptionKey, err := a.GetAccountEncryptionKey(ctx, account)
	if err != nil {
		slog.InfoContext(ctx, "Failed to get account encryption key", slog.String("accountId", account.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to get account encryption key: %w", err)
	}

	decipher, err := cryptor.CreateDecipher(iv, encryptionKey)
	if err != nil {
		slog.InfoContext(ctx, "Failed to create decipher for account",
			slog.String("accountId", account.Id.String()),
			slog.String("iv", string(iv)),
			slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to create decipher: %w", err)
	}

	return decipher, nil
}
