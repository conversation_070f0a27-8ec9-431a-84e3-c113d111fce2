//go:build integration
// +build integration

package message_sender_test

import (
	"context"
	"testing"
	"time"

	"github.com/go-faker/faker/v4"
	"github.com/stretchr/testify/suite"

	"digisac-go/worker/core/adapters/telegram_adapter"
	"digisac-go/worker/core/adapters/whatsapp_adapter"
	"digisac-go/worker/core/http/payloads"
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/repositories"
	"digisac-go/worker/core/services/message/message_sender"
)

type MessageServiceTestSuite struct {
	suite.Suite
	telegram_adapter   *telegram_adapter.TelegramAdapter
	whatsapp_adapter   *whatsapp_adapter.WhatsappAdapter
	messageSendService *message_sender.SendService
	accountRepository  repositories.AccountRepository
	contactRepository  repositories.ContactRepository
	serviceRepository  repositories.ServiceRepository
	userRepository     repositories.UserRepository
}

func (suite *MessageServiceTestSuite) SetupSuite() {
	// Skip tests that require database connection
	suite.T().Skip("Skipping test that requires database connection")
}

func (suite *MessageServiceTestSuite) TestGetMessageType() {
	var file *models.File
	var messageType string

	messageType = suite.messageSendService.GetMessageType(file)

	suite.Require().Equal("chat", messageType)

	file = &models.File{
		Mimetype: "audio/aac",
	}

	messageType = suite.messageSendService.GetMessageType(file)

	suite.Require().Equal("audio", messageType)

	file = &models.File{
		Mimetype: "image/png",
	}

	messageType = suite.messageSendService.GetMessageType(file)

	suite.Require().Equal("image", messageType)

	file = &models.File{
		Mimetype: "video/mp4",
	}

	messageType = suite.messageSendService.GetMessageType(file)

	suite.Require().Equal("video", messageType)

	file = &models.File{
		Mimetype: "text/csv",
	}

	messageType = suite.messageSendService.GetMessageType(file)

	suite.Require().Equal("document", messageType)
}

func (suite *MessageServiceTestSuite) TestGetContactByIdOrNumber() {
	var err error
	var createMessageData *payloads.SendMessagePayload
	var contact *models.Contact

	tx := suite.accountRepository.BeginTransaction()
	defer tx.Rollback()

	account, _ := suite.accountRepository.FindOne(context.Background())

	service, _ := suite.serviceRepository.FindOne(context.Background(), repositories.WithQueryStruct(map[string]interface{}{
		"accountId": account.Id,
	}))

	user, _ := suite.userRepository.FindOne(context.Background(), repositories.WithQueryStruct(map[string]interface{}{
		"accountId": account.Id,
	}))

	contact = &models.Contact{
		Name:          faker.Name(),
		IdFromService: faker.Phonenumber(),
		AccountId:     account.Id,
		ServiceId:     service.Id,
		Origin:        models.ContactOriginWeb,
	}

	err = suite.contactRepository.Create(context.Background(), contact, nil, repositories.WithTx(tx))

	suite.Require().NoError(err)

	createMessageData = &payloads.SendMessagePayload{
		Text:      "Essa é uma mensagem de teste",
		Type:      "text",
		ContactId: contact.Id,
		ServiceId: service.Id,
		AccountId: account.Id,
		Origin:    "chat",
		UserId:    user.Id,
	}

	byContactIdContact, err := suite.messageSendService.GetContactByIdOrNumber(context.Background(), createMessageData, tx)

	suite.Require().NoError(err)

	suite.Require().Equal(byContactIdContact.Id, contact.Id)

	createMessageData = &payloads.SendMessagePayload{
		Text:      "Essa é uma mensagem de teste",
		Type:      "text",
		Number:    contact.IdFromService,
		ServiceId: service.Id,
		AccountId: account.Id,
		Origin:    "chat",
		UserId:    user.Id,
	}

	byNumberContact, err := suite.messageSendService.GetContactByIdOrNumber(context.Background(), createMessageData, tx)

	suite.Require().NoError(err)

	suite.Require().Equal(byNumberContact.Id, contact.Id)

	now := time.Now()

	// Contato arquivado
	contact = &models.Contact{
		Name:          faker.Name(),
		IdFromService: faker.Phonenumber(),
		AccountId:     account.Id,
		ServiceId:     service.Id,
		Origin:        models.ContactOriginWeb,
		ArchivedAt:    &now,
	}

	err = suite.contactRepository.Create(context.Background(), contact, nil, repositories.WithTx(tx))

	suite.Require().NoError(err)

	createMessageData = &payloads.SendMessagePayload{
		Text:      "Essa é uma mensagem de teste",
		Type:      "text",
		ContactId: contact.Id,
		ServiceId: service.Id,
		AccountId: account.Id,
		Origin:    "chat",
		UserId:    user.Id,
	}

	arquivedContact, err := suite.messageSendService.GetContactByIdOrNumber(context.Background(), createMessageData, tx)

	suite.Require().Error(err)

	suite.Require().NotEqual(arquivedContact, nil)

}

func TestMessageServiceTestSuite(t *testing.T) {
	suite.Run(t, new(MessageServiceTestSuite))
}
