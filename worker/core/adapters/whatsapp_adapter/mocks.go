package whatsapp_adapter

import (
	"context"
	pb "digisac-go/common/grpc/api"
	common "digisac-go/common/grpc/common"
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/repositories"

	"github.com/google/uuid"
	gormRepository "github.com/ikateclab/gorm-repository"
	"github.com/stretchr/testify/mock"
	"google.golang.org/grpc"
)

type MockGrpcWppClient struct {
	pb.PublicServiceClient
	mock.Mock
}

func (m *MockGrpcWppClient) SendMessage(ctx context.Context, in *common.SendMessageRequest, opts ...grpc.CallOption) (*common.SendMessageResponse, error) {
	args := m.Called(ctx, in)

	if args.Get(0) == nil {
		return nil, args.Error(1)
	}

	return args.Get(0).(*common.SendMessageResponse), args.Error(1)
}

func (m *MockGrpcWppClient) Logout(ctx context.Context, in *common.ActionPayload, opts ...grpc.CallOption) (*common.ActionResponse, error) {
	args := m.Called(ctx, in)

	if args.Get(0) == nil {
		return nil, args.Error(1)
	}

	return args.Get(0).(*common.ActionResponse), args.Error(1)
}

func (m *MockGrpcWppClient) Stop(ctx context.Context, in *common.ActionPayload, opts ...grpc.CallOption) (*common.ActionResponse, error) {
	args := m.Called(ctx, in)

	if args.Get(0) == nil {
		return nil, args.Error(1)
	}

	return args.Get(0).(*common.ActionResponse), args.Error(1)
}

func (m *MockGrpcWppClient) SendReactionToMessage(ctx context.Context, in *common.SendReactionToMessageRequest, opts ...grpc.CallOption) (*common.SendReactionToMessageResponse, error) {
	args := m.Called(ctx, in)

	if args.Get(0) == nil {
		return nil, args.Error(1)
	}

	return args.Get(0).(*common.SendReactionToMessageResponse), args.Error(1)
}

type MockServiceRepository struct {
	repositories.ServiceRepository
	mock.Mock
}

func (m *MockServiceRepository) FindById(ctx context.Context, id uuid.UUID, options ...gormRepository.Option) (*models.Service, error) {
	args := m.Called(ctx, id)

	if args.Get(0) == nil {
		return nil, args.Error(1)
	}

	return args.Get(0).(*models.Service), args.Error(1)
}
