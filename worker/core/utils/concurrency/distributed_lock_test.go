//go:build integration
// +build integration

package concurrency_test

import (
	"context"
	"errors"
	"fmt"
	"sync"
	"testing"
	"time"

	"digisac-go/worker/core/utils/concurrency"

	redisClient "github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/suite"
	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/modules/redis"
	"github.com/testcontainers/testcontainers-go/wait"
)

type DistributedLockSuite struct {
	suite.Suite
	RedisClient            *redisClient.Client
	DistributedLockFactory concurrency.DistributedLockFactory
	redisContainer         *redis.RedisContainer
}

func (suite *DistributedLockSuite) SetupSuite() {
	// Usar um timeout curto para inicialização
	ctx, cancel := context.WithTimeout(context.Background(), 3*time.Second)
	defer cancel()

	// Iniciar um contêiner Redis usando testcontainers com opções otimizadas
	redisContainer, err := redis.Run(ctx, "redis:7-alpine",
		testcontainers.WithWaitStrategy(
			wait.ForLog("Ready to accept connections").WithStartupTimeout(2*time.Second),
		),
	)

	if err != nil {
		suite.T().Fatalf("Falha ao iniciar o contêiner Redis: %s", err)
	}

	// Configurar o cliente Redis
	host, err := redisContainer.Host(ctx)
	if err != nil {
		suite.T().Fatalf("Falha ao obter o host do Redis: %s", err)
	}

	port, err := redisContainer.MappedPort(ctx, "6379/tcp")
	if err != nil {
		suite.T().Fatalf("Falha ao obter a porta do Redis: %s", err)
	}

	// Criar o cliente Redis com timeouts reduzidos
	suite.RedisClient = redisClient.NewClient(&redisClient.Options{
		Addr:         host + ":" + port.Port(),
		DialTimeout:  50 * time.Millisecond,
		ReadTimeout:  50 * time.Millisecond,
		WriteTimeout: 50 * time.Millisecond,
		PoolTimeout:  50 * time.Millisecond,
		PoolSize:     2, // Reduzir o tamanho do pool para testes
	})

	// Verificar conexão com o Redis
	pingCtx, pingCancel := context.WithTimeout(context.Background(), 500*time.Millisecond)
	defer pingCancel()

	_, err = suite.RedisClient.Ping(pingCtx).Result()
	if err != nil {
		suite.T().Fatalf("Falha ao conectar ao Redis: %s", err)
	}

	// Criar o CachedRedsync
	cachedRedsync := concurrency.NewCachedRedsync(suite.RedisClient)

	// Criar o DistributedLockFactory
	suite.DistributedLockFactory = concurrency.NewDistributedLockFactory(cachedRedsync)

	// Armazenar o contêiner para limpeza posterior
	suite.redisContainer = redisContainer
}

func (suite *DistributedLockSuite) TearDownSuite() {
	// Fechar a conexão com o Redis
	if suite.RedisClient != nil {
		suite.RedisClient.Close()
	}

	// Parar e remover o contêiner Redis com timeout
	if suite.redisContainer != nil {
		ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
		defer cancel()
		if err := suite.redisContainer.Terminate(ctx); err != nil {
			suite.T().Logf("Erro ao encerrar o contêiner Redis: %s", err)
		}
	}
}

func (suite *DistributedLockSuite) TestTaskQueueBasic() {
	// Usar timeout de 100ms
	queue := suite.DistributedLockFactory("basic-task-queue", time.Duration(100)*time.Millisecond)

	task := func() (interface{}, error) {
		// Tarefa rápida de 1ms
		time.Sleep(time.Duration(1) * time.Millisecond)
		return "task complete", nil
	}

	result, err := queue.Run(context.Background(), task)

	suite.Require().NoError(err)
	suite.Require().Equal("task complete", result)
}

func (suite *DistributedLockSuite) TestTaskQueueTimeout() {
	// Usar timeout de 50ms (tempo suficiente para adquirir o lock, mas não para completar a tarefa)
	queue := suite.DistributedLockFactory("timeout-task-queue", time.Duration(50)*time.Millisecond)

	task := func() (interface{}, error) {
		// Tarefa lenta de 200ms (maior que o timeout)
		time.Sleep(time.Duration(200) * time.Millisecond)
		return "should timeout", nil
	}

	result, err := queue.Run(context.Background(), task)

	suite.Require().Error(err)
	suite.Require().Nil(result)
	suite.Require().Equal(concurrency.ErrTimeoutDistributedLock, err)
}

func (suite *DistributedLockSuite) TestTaskQueueLock() {
	// Usar timeout de 100ms
	queue := suite.DistributedLockFactory("lock-task-queue", time.Duration(200)*time.Millisecond)

	// Usar um canal para sincronizar os testes
	done := make(chan struct{})

	executionOrder := []string{}

	task1 := func() (interface{}, error) {
		// Tarefa de 10ms
		time.Sleep(time.Duration(100) * time.Millisecond)
		executionOrder = append(executionOrder, "task 1")
		return "task 1 complete", nil
	}

	task2 := func() (interface{}, error) {
		// Tarefa de 5ms
		time.Sleep(time.Duration(50) * time.Millisecond)
		executionOrder = append(executionOrder, "task 2")

		return "task 2 complete", nil
	}

	go func() {
		result, err := queue.Run(context.Background(), task1)
		suite.Require().NoError(err)
		suite.Require().Equal("task 1 complete", result)
		close(done)
	}()

	// Esperar um pouco para garantir que a primeira tarefa começou
	time.Sleep(time.Duration(10) * time.Millisecond)

	result2, err2 := queue.Run(context.Background(), task2)
	suite.Require().NoError(err2)
	suite.Require().Equal("task 2 complete", result2)

	// Esperar a goroutine terminar com timeout
	select {
	case <-done:
		// OK
	case <-time.After(200 * time.Millisecond):
		suite.T().Fatal("Timeout esperando pela goroutine")
	}

	suite.Require().Equal([]string{"task 1", "task 2"}, executionOrder)
}

func (suite *DistributedLockSuite) TestTaskQueueFailure() {
	// Usar timeout de 100ms
	queue := suite.DistributedLockFactory("failure-task-queue", time.Duration(100)*time.Millisecond)

	task := func() (interface{}, error) {
		return nil, errors.New("task failed")
	}

	result, err := queue.Run(context.Background(), task)

	suite.Require().Error(err)
	suite.Require().Nil(result)
	suite.Require().Equal("task failed", err.Error())
}

func (suite *DistributedLockSuite) TestTaskQueueWithContextCancellation() {
	// Usar timeout de 100ms
	queue := suite.DistributedLockFactory("context-task-queue", time.Duration(100)*time.Millisecond)

	task := func() (interface{}, error) {
		// Criar um contexto com timeout de 5ms
		ctx, cancel := context.WithTimeout(context.Background(), time.Duration(5)*time.Millisecond)
		defer cancel()

		select {
		// Tarefa que levaria 20ms
		case <-time.After(time.Duration(20) * time.Millisecond):
			return "task finished", nil
		case <-ctx.Done():
			return nil, ctx.Err()
		}
	}

	result, err := queue.Run(context.Background(), task)

	suite.Require().Error(err)
	suite.Require().Nil(result)
	suite.Require().Equal(context.DeadlineExceeded, err)
}

// Teste de múltiplas filas concorrentes
func (suite *DistributedLockSuite) TestTaskQueueWithManyQueues() {
	// Usar um WaitGroup para sincronizar as goroutines
	wg := sync.WaitGroup{}
	wg.Add(3) // 3 goroutines

	// Criar 3 locks diferentes
	queue1 := suite.DistributedLockFactory("queue-1", time.Duration(100)*time.Millisecond)
	queue2 := suite.DistributedLockFactory("queue-2", time.Duration(100)*time.Millisecond)
	queue3 := suite.DistributedLockFactory("queue-3", time.Duration(100)*time.Millisecond)

	// Tarefa rápida
	task := func() (interface{}, error) {
		time.Sleep(time.Duration(5) * time.Millisecond)
		return "task complete", nil
	}

	start := time.Now()

	// Executar tarefas em paralelo em diferentes filas
	go func() {
		defer wg.Done()
		result, err := queue1.Run(context.Background(), task)
		suite.Require().NoError(err)
		suite.Require().Equal("task complete", result)
	}()

	go func() {
		defer wg.Done()
		result, err := queue2.Run(context.Background(), task)
		suite.Require().NoError(err)
		suite.Require().Equal("task complete", result)
	}()

	go func() {
		defer wg.Done()
		result, err := queue3.Run(context.Background(), task)
		suite.Require().NoError(err)
		suite.Require().Equal("task complete", result)
	}()

	// Esperar todas as goroutines terminarem
	wg.Wait()

	duration := time.Since(start)

	// Como são filas diferentes, devem executar em paralelo e terminar rapidamente
	suite.Require().Less(duration, 50*time.Millisecond, "O tempo total deve ser menor que 50ms")
}

// Teste de concorrência na mesma fila
func (suite *DistributedLockSuite) TestTaskQueueConcurrency() {
	// Usar um WaitGroup para sincronizar as goroutines
	wg := sync.WaitGroup{}
	wg.Add(3) // 3 goroutines

	// Criar uma única fila
	queue := suite.DistributedLockFactory("concurrency-queue", time.Duration(100)*time.Millisecond)

	// Canal para registrar a ordem de execução
	orderCh := make(chan int, 3)

	// Tarefa que registra sua ordem de execução
	task1 := func() (interface{}, error) {
		orderCh <- 1
		time.Sleep(time.Duration(5) * time.Millisecond)
		return "task 1 complete", nil
	}

	task2 := func() (interface{}, error) {
		orderCh <- 2
		time.Sleep(time.Duration(5) * time.Millisecond)
		return "task 2 complete", nil
	}

	task3 := func() (interface{}, error) {
		orderCh <- 3
		time.Sleep(time.Duration(5) * time.Millisecond)
		return "task 3 complete", nil
	}

	// Executar tarefas em paralelo na mesma fila
	go func() {
		defer wg.Done()
		result, err := queue.Run(context.Background(), task1)
		suite.Require().NoError(err)
		suite.Require().Equal("task 1 complete", result)
	}()

	go func() {
		defer wg.Done()
		result, err := queue.Run(context.Background(), task2)
		suite.Require().NoError(err)
		suite.Require().Equal("task 2 complete", result)
	}()

	go func() {
		defer wg.Done()
		result, err := queue.Run(context.Background(), task3)
		suite.Require().NoError(err)
		suite.Require().Equal("task 3 complete", result)
	}()

	// Esperar todas as goroutines terminarem
	wg.Wait()

	// Verificar que todas as tarefas foram executadas
	suite.Require().Equal(3, len(orderCh))

	// Ler a ordem de execução
	var order []int
	for i := 0; i < 3; i++ {
		order = append(order, <-orderCh)
	}

	// Como é a mesma fila, as tarefas devem ser executadas sequencialmente
	// mas não podemos garantir a ordem exata, apenas que foram 3 tarefas diferentes
	suite.Require().Equal(3, len(order))
}

// Teste de recuperação após falha
func (suite *DistributedLockSuite) TestTaskQueueRecovery() {
	// Criar uma fila
	queue := suite.DistributedLockFactory("recovery-queue", time.Duration(100)*time.Millisecond)

	// Primeira tarefa falha
	task1 := func() (interface{}, error) {
		return nil, errors.New("task failed")
	}

	// Segunda tarefa sucede
	task2 := func() (interface{}, error) {
		return "task succeeded", nil
	}

	// Executar a primeira tarefa (que falha)
	result1, err1 := queue.Run(context.Background(), task1)
	suite.Require().Error(err1)
	suite.Require().Nil(result1)
	suite.Require().Equal("task failed", err1.Error())

	// Executar a segunda tarefa (que deve suceder)
	result2, err2 := queue.Run(context.Background(), task2)
	suite.Require().NoError(err2)
	suite.Require().Equal("task succeeded", result2)
}

// Teste de locks aninhados
func (suite *DistributedLockSuite) TestNestedLocks() {
	// Criar duas filas
	queue1 := suite.DistributedLockFactory("outer-lock", time.Duration(100)*time.Millisecond)
	queue2 := suite.DistributedLockFactory("inner-lock", time.Duration(100)*time.Millisecond)

	// Tarefa aninhada
	outerTask := func() (interface{}, error) {
		// Executar uma tarefa dentro de outra
		innerTask := func() (interface{}, error) {
			return "inner task complete", nil
		}

		// Executar a tarefa interna
		innerResult, err := queue2.Run(context.Background(), innerTask)
		if err != nil {
			return nil, err
		}

		return "outer task complete: " + innerResult.(string), nil
	}

	// Executar a tarefa externa
	result, err := queue1.Run(context.Background(), outerTask)

	suite.Require().NoError(err)
	suite.Require().Equal("outer task complete: inner task complete", result)
}

// Teste de timeout padrão
func (suite *DistributedLockSuite) TestDefaultTimeout() {
	// Criar uma fila com timeout 0 (deve usar o padrão de 60s)
	queue := suite.DistributedLockFactory("default-timeout-queue", 0)

	// Tarefa rápida
	task := func() (interface{}, error) {
		return "task complete", nil
	}

	// Executar a tarefa
	result, err := queue.Run(context.Background(), task)

	suite.Require().NoError(err)
	suite.Require().Equal("task complete", result)
}

// Teste de cache de mutexes
func (suite *DistributedLockSuite) TestMutexCache() {
	// Criar o CachedRedsync diretamente para testar o cache
	cachedRedsync := concurrency.NewCachedRedsync(suite.RedisClient)

	// Obter o mesmo mutex duas vezes
	mutex1 := cachedRedsync.GetMutex("cache-test", 100*time.Millisecond)
	mutex2 := cachedRedsync.GetMutex("cache-test", 100*time.Millisecond)

	// Verificar se é o mesmo objeto (mesmo endereço de memória)
	suite.Require().Equal(fmt.Sprintf("%p", mutex1), fmt.Sprintf("%p", mutex2), "Os mutexes devem ser o mesmo objeto")

	// Obter um mutex diferente
	mutex3 := cachedRedsync.GetMutex("different-key", 100*time.Millisecond)

	// Verificar se é um objeto diferente
	suite.Require().NotEqual(fmt.Sprintf("%p", mutex1), fmt.Sprintf("%p", mutex3), "Os mutexes devem ser objetos diferentes")
}

func TestDistributedLockSuite(t *testing.T) {
	suite.Run(t, new(DistributedLockSuite))
}
