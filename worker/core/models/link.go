package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Link struct {
	Id        uuid.UUID  `gorm:"type:uuid;primary_key" json:"id,omitempty"`
	MessageId uuid.UUID  `gorm:"type:uuid;not null" json:"messageId,omitempty"`
	CreatedAt *time.Time `json:"createdAt,omitempty"`
	UpdatedAt *time.Time `json:"updatedAt,omitempty"`
	Message   *Message   `gorm:"foreignKey:MessageId" json:"message,omitempty"`
}

func (Link) TableName() string {
	return "links"
}

func (l *Link) BeforeCreate(tx *gorm.DB) (err error) {
	l.Id = uuid.New()
	return
}
