package middlewares

import (
	"context"
	"fmt"
	"log/slog"
	"time"

	"github.com/gofiber/fiber/v3"
)

type contextKey string

const (
	RequestIdKey     contextKey = "request_id"
	ImpersonateKey   contextKey = "impersonate"
	ClientKey        contextKey = "client"
	UserAgentKey     contextKey = "user_agent"
	SourceIpKey      contextKey = "source_ip"
	PlatformKey      contextKey = "platform"
	RequestUriKey    contextKey = "request_uri"
	RequestMethodKey contextKey = "request_method"
)

// Função para popular o contexto com dados da request
func TracingCtx(c fiber.Ctx) context.Context {
	requestId := "req-" + time.Now().Format("20060102150405")

	impersonate := c.Get("Impersonate") == "true"
	userAgent := c.Get("User-Agent")
	platform := c.Get("Platform")
	sourceIP := c.Get("X-Real-IP")
	if sourceIP == "" {
		sourceIP = c.IP()
	}

	ctx := context.WithValue(c.Context(), RequestIdKey, requestId)
	ctx = context.WithValue(ctx, ImpersonateKey, impersonate)
	ctx = context.WithValue(ctx, UserAgentKey, userAgent)
	ctx = context.WithValue(ctx, PlatformKey, platform)
	ctx = context.WithValue(ctx, SourceIpKey, sourceIP)
	ctx = context.WithValue(ctx, RequestUriKey, c.OriginalURL())
	ctx = context.WithValue(ctx, RequestMethodKey, c.Method())

	// Retorna o novo contexto
	return ctx
}

// Middleware para popular o contexto inicial
func TracingMiddleware() fiber.Handler {

	return func(c fiber.Ctx) error {

		ctx := TracingCtx(c)

		// Substitui o contexto da requisição com o novo contexto
		c.SetContext(ctx)
		// Loga o início da transação
		requestId, _ := ctx.Value(RequestIdKey).(string)
		userAgent, _ := ctx.Value(UserAgentKey).(string)
		sourceIP, _ := ctx.Value(SourceIpKey).(string)
		platform, _ := ctx.Value(PlatformKey).(string)
		requestURI, _ := ctx.Value(RequestUriKey).(string)
		requestMethod, _ := ctx.Value(RequestMethodKey).(string)

		slog.InfoContext(ctx, "Starting transaction",
			slog.String("request_id", requestId),
			slog.String("user_agent", userAgent),
			slog.String("source_ip", sourceIP),
			slog.String("platform", platform),
			slog.String("request_uri", requestURI),
			slog.String("request_method", requestMethod),
		)

		// Chama o próximo middleware ou handler
		err := c.Next()
		if err != nil {
			requestId, _ := ctx.Value(RequestIdKey).(string)
			slog.ErrorContext(ctx, "Error during transaction",
				slog.String("request_id", requestId),
				slog.String("error", fmt.Sprintf("next middleware error: %v", err)),
			)
			return fmt.Errorf("error during transaction: %w", err)
		}

		// Loga o fim da transação
		requestId, _ = ctx.Value(RequestIdKey).(string)
		slog.InfoContext(ctx, "Finished transaction",
			slog.String("request_id", requestId),
			slog.Int("status_code", c.Response().StatusCode()),
		)

		return nil
	}
}
