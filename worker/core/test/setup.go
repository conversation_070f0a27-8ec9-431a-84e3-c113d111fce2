package test

import (
	"context"
	"fmt"
	"log/slog"

	"digisac-go/worker/config"
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/orm"

	queueDispatcher "digisac-go/worker/core/services/dispatcher/queue"

	"go.uber.org/fx"
	"gorm.io/gorm"
)

// TestSetup contém todas as dependências necessárias para os testes de integração
type TestSetup struct {
	Config            *config.Config
	DB                *gorm.DB
	PostgresContainer *PostgresContainer
	queueDispatcher   *queueDispatcher.QueueJobsDispatcherService
	Ctx               context.Context
}

// NewTestSetup cria uma nova instância de TestSetup com todas as dependências inicializadas
func NewTestSetup(ctx context.Context) (*TestSetup, error) {
	// Inicializar o contexto
	if ctx == nil {
		ctx = context.Background()
	}

	// Inicializar a configuração
	cfg, err := config.NewConfig()
	if err != nil {
		return nil, fmt.Errorf("falha ao criar configuração: %w", err)
	}

	// Definir ambiente de teste
	cfg.Deployment = "test"

	// Inicializar o contêiner PostgreSQL
	postgresContainer, err := NewPostgresContainer(ctx, cfg)
	if err != nil {
		return nil, fmt.Errorf("falha ao iniciar contêiner PostgreSQL: %w", err)
	}

	// Atualizar configuração com detalhes do contêiner PostgreSQL
	cfg.DbHost = postgresContainer.Host
	cfg.DbPort = postgresContainer.Port
	cfg.DbName = postgresContainer.Database
	cfg.DbUsername = postgresContainer.Username
	cfg.DbPassword = postgresContainer.Password

	// Configurar conexão com o banco de dados
	db, err := orm.SetupDb(cfg)
	if err != nil {
		return nil, fmt.Errorf("falha ao conectar ao PostgreSQL: %w", err)
	}

	err = db.AutoMigrate(
		&models.Account{},
		&models.Answer{},
		&models.Campaign{},
		&models.CampaignMessage{},
		&models.CampaignMessageProgress{},
		&models.Contact{},
		&models.Department{},
		&models.File{},
		&models.Message{},
		&models.Question{},
		&models.Service{},
		&models.Tag{},
		&models.Ticket{},
		&models.TicketTopic{},
		&models.TicketTransfer{},
		&models.User{},
		&models.WhatsappBusinessTemplate{},
		&models.WhatsappBusinessTemplateHistory{},
	)
	if err != nil {
		return nil, fmt.Errorf("falha ao migrar modelos: %w", err)
	}

	return &TestSetup{
		Config:            cfg,
		DB:                db,
		PostgresContainer: postgresContainer,
		Ctx:               ctx,
	}, nil
}

// Cleanup limpa todos os recursos utilizados pelo TestSetup
func (ts *TestSetup) Cleanup() error {
	// Fechar conexão com o banco de dados
	if ts.DB != nil {
		db, err := ts.DB.DB()
		if err == nil && db != nil {
			if err := db.Close(); err != nil {
				fmt.Println("Error closing db:", err)
			}
		}
	}

	// Parar o contêiner do PostgreSQL
	if ts.PostgresContainer != nil {
		err := ts.PostgresContainer.Terminate(ts.Ctx)
		if err != nil {
			slog.ErrorContext(ts.Ctx, "Erro ao parar o contêiner do PostgreSQL", slog.Any("error", err))
		}
	}

	return nil
}

// Module retorna um módulo fx para injeção de dependência em testes
func Module(ctx context.Context) fx.Option {
	return fx.Options(
		fx.Provide(
			func() context.Context {
				return ctx
			},
			func(ctx context.Context) (*TestSetup, error) {
				return NewTestSetup(ctx)
			},
			func(ts *TestSetup) *config.Config {
				return ts.Config
			},
			func(ts *TestSetup) *gorm.DB {
				return ts.DB
			},
			func(ts *TestSetup) *queueDispatcher.QueueJobsDispatcherService {
				return ts.queueDispatcher
			},
		),
	)
}
