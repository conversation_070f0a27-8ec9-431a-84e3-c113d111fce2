package common

import (
	"context"
	"encoding/json"
	"log/slog"
)

// ToStruct converts a payload of any type to a target struct
// It works by marshaling the payload to JSON and then unmarshaling it to the target struct
//
// Parameters:
//   - payload: The source data to convert
//   - t: The target struct to convert to (must be a pointer)
//
// Returns:
//   - error: Any error that occurred during conversion
func ToStruct(ctx context.Context, payload any, t any) error {
	jsonData, err := json.Marshal(payload)
	if err != nil {
		slog.DebugContext(ctx, "Failed to convert payload to JSON", slog.Any("error", err.Error()))
		return err
	}

	err = json.Unmarshal(jsonData, t)
	if err != nil {
		slog.DebugContext(ctx, "Failed to deserialize JSON to struct", slog.Any("error", err.Error()))
		return err
	}

	return nil
}
