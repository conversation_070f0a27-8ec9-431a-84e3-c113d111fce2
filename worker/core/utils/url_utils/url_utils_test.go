package url

import (
	"reflect"
	"testing"
)

func TestExtractURLs(t *testing.T) {
	tests := []struct {
		name     string
		text     string
		expected []string
	}{
		{
			name:     "Empty text",
			text:     "",
			expected: []string{},
		},
		{
			name:     "No URLs",
			text:     "This is a text without any URLs",
			expected: []string{},
		},
		{
			name:     "Single URL with http",
			text:     "Check out http://example.com for more info",
			expected: []string{"http://example.com"},
		},
		{
			name:     "Single URL with https",
			text:     "Check out https://example.com for more info",
			expected: []string{"https://example.com"},
		},
		{
			name:     "Single URL without protocol",
			text:     "Check out example.com for more info",
			expected: []string{"example.com"},
		},
		{
			name:     "Multiple URLs",
			text:     "Visit http://example.com and https://another-example.org for more info",
			expected: []string{"http://example.com", "https://another-example.org"},
		},
		{
			name:     "URL with path",
			text:     "Visit https://example.com/path/to/resource for more info",
			expected: []string{"https://example.com/path/to/resource"},
		},
		{
			name:     "URL with query parameters",
			text:     "Visit https://example.com/search?q=test&page=1 for more info",
			expected: []string{"https://example.com/search?q=test&page=1"},
		},
		{
			name:     "URL with www",
			text:     "Visit www.example.com for more info",
			expected: []string{"www.example.com"},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := ExtractURLs(tt.text)
			if !reflect.DeepEqual(result, tt.expected) {
				t.Errorf("extractURLs() = %v, want %v", result, tt.expected)
			}
		})
	}
}
