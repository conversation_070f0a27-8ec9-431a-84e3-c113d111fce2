package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type BlockListStatus string

const (
	BlockListStatusReady      BlockListStatus = "ready"
	BlockListStatusProcessing BlockListStatus = "processing"
	BlockListStatusDone       BlockListStatus = "done"
	BlockListStatusError      BlockListStatus = "error"
)

type ContactBlockList struct {
	Id         uuid.UUID       `gorm:"primaryKey" json:"id"`
	Name       string          `gorm:"type:string;not null" json:"name"`
	Status     BlockListStatus `gorm:"type:string;not null;default:'ready'" json:"status"`
	DefaultDDI *string         `gorm:"type:string" json:"defaultDDI"`
	SaveCount  *int            `gorm:"type:integer" json:"saveCount"`
	ValidCount *int            `gorm:"type:integer" json:"validCount"`
	TotalCount *int            `gorm:"type:integer" json:"totalCount"`
	AccountId  uuid.UUID       `gorm:"type:uuid;not null" json:"accountId"`
	Account    *Account        `gorm:"foreignKey:AccountId" json:"account"`
	UserId     uuid.UUID       `gorm:"type:uuid;not null" json:"userId"`
	User       *User           `gorm:"foreignKey:UserId" json:"user"`
	CreatedAt  time.Time       `gorm:"type:timestamptz;not null;default:now()" json:"createdAt"`
	UpdatedAt  time.Time       `gorm:"type:timestamptz;not null;default:now()" json:"updatedAt"`
	DeletedAt  *gorm.DeletedAt `gorm:"index" json:"deletedAt,omitempty"`
}

func (ContactBlockList) TableName() string {
	return "contact_block_lists"
}

func (c *ContactBlockList) BeforeCreate(tx *gorm.DB) (err error) {
	c.Id = uuid.New()
	return
}
