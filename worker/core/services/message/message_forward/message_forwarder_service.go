package message_forwarder

import (
	"context"
	"digisac-go/worker/core/http/payloads"
	"digisac-go/worker/core/managers/service_manager"
	"digisac-go/worker/core/repositories"
	"digisac-go/worker/core/services/crypto"
	fileService "digisac-go/worker/core/services/file"
	"digisac-go/worker/core/services/message/message_sender"
	"digisac-go/worker/core/services/storage"
	"fmt"
	"log/slog"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type ForwardMessagesResponse struct {
	Id          uuid.UUID   `json:"id"`
	MessagesIds []uuid.UUID `json:"messagesIds"`
}

type ForwardService struct {
	adapterManager        *service_manager.ServiceManager
	contactRepository     repositories.ContactRepository
	messageRepository     repositories.MessageRepository
	accountRepository     repositories.AccountRepository
	fileRepository        repositories.FileRepository
	messageSendService    *message_sender.SendService
	accountCryptorService *crypto.AccountCryptor
	storageService        *storage.StorageService
	fileService           *fileService.FileService
}

func NewForwardService(
	adapterManager *service_manager.ServiceManager,
	contactRepository repositories.ContactRepository,
	messageRepository repositories.MessageRepository,
	accountRepository repositories.AccountRepository,
	fileRepository repositories.FileRepository,
	messageSendService *message_sender.SendService,
	accountCryptorService *crypto.AccountCryptor,
	storageService *storage.StorageService,
	fileService *fileService.FileService,
) *ForwardService {
	return &ForwardService{
		adapterManager:        adapterManager,
		contactRepository:     contactRepository,
		messageRepository:     messageRepository,
		messageSendService:    messageSendService,
		fileRepository:        fileRepository,
		accountRepository:     accountRepository,
		accountCryptorService: accountCryptorService,
		storageService:        storageService,
		fileService:           fileService,
	}
}

func (f *ForwardService) ForwardMessages(ctx context.Context, forwardData *payloads.ForwardMessagesPayload) (response []*ForwardMessagesResponse, err error) {
	contacts, err := f.contactRepository.FindMany(ctx, repositories.WithQuery(func(d *gorm.DB) *gorm.DB {
		return d.Where("id IN ?", forwardData.ContactsIds)
	}))

	if err != nil {
		slog.ErrorContext(ctx, "Failed to find contacts", slog.Any("error", err), slog.Any("forwardData", forwardData))
		return nil, fmt.Errorf("failed to find contacts: %w", err)
	}

	messages, err := f.messageRepository.FindMany(ctx, repositories.WithQuery(func(d *gorm.DB) *gorm.DB {
		return d.Where("id IN ?", forwardData.MessagesIds).Order("timestamp asc")
	}))

	if err != nil {
		slog.ErrorContext(ctx, "Failed to find messages", slog.Any("error", err), slog.Any("forwardData", forwardData))
		return nil, fmt.Errorf("failed to find messages: %w", err)
	}

	for index, msg := range messages {
		file, err := f.fileRepository.FindOne(ctx, repositories.WithQueryStruct(map[string]interface{}{
			"attachedId": msg.Id,
			"accountId":  msg.AccountId,
		}))

		if err != nil && err.Error() != "record not found" {
			slog.ErrorContext(ctx, "Failed to find file", slog.Any("error", err), slog.Any("message", msg))
			return nil, fmt.Errorf("failed to find file: %w", err)
		}

		messages[index].File = file
	}

	for _, contact := range contacts {
		account, err := f.accountRepository.FindById(ctx, contact.AccountId)

		if err != nil {
			slog.ErrorContext(ctx, "Failed to find account", slog.Any("error", err), slog.Any("contact", contact))
			return nil, fmt.Errorf("failed to find account: %w", err)
		}

		var sentMessagesIds []uuid.UUID

		for _, message := range messages {
			text, err := f.accountCryptorService.DecryptTextForAccount(ctx, account, message.Text, false)

			if err != nil {
				slog.ErrorContext(ctx, "Failed to decrypt text", slog.Any("error", err), slog.Any("message", message), slog.Any("account", account))
				return nil, fmt.Errorf("failed to decrypt text: %w", err)
			}

			if text == "" {
				if message.Data.Location != nil {
					text = fmt.Sprintf(`https://www.google.com/maps?q=%v,%v`, message.Data.Location.Lat, message.Data.Location.Lng)
				} else {
					text = " "
				}
			}

			var fileId uuid.UUID

			if message.File != nil {
				stream, err := f.storageService.GetStream(ctx, message.File)

				if err != nil {
					slog.ErrorContext(ctx, "Failed to get stream from file", slog.Any("error", err), slog.Any("file", message.File))
					return nil, fmt.Errorf("failed to get stream from file: %w", err)
				}

				file, err := f.fileService.CreateFile(ctx, &fileService.CreateFilePayload{
					Name: message.File.Name,
					// AttachedId:   message.Id,
					AttachedType: "message.file",
					Mimetype:     message.File.Mimetype,
					AccountId:    message.AccountId,
					Stream:       stream,
				}, nil)
				if err != nil {
					slog.ErrorContext(ctx, "Failed to create file", slog.Any("error", err), slog.Any("file", file))
					return nil, fmt.Errorf("failed to create file: %w", err)
				}

				fileId = file.Id
			}

			sendMessagePayload := &payloads.SendMessagePayload{
				ContactId:       contact.Id,
				Type:            message.Type,
				ServiceId:       contact.ServiceId,
				QuotedMessageId: message.QuotedMessageId,
				UserId:          forwardData.UserId,
				Origin:          "forward",
				AccountId:       message.AccountId,
				DontOpenTicket:  false,
				Text:            text,
				FileId:          fileId,
				HsmId:           message.HsmId,
				HsmFileId:       message.HsmFileId,
				Parameters:      message.Data.HsmParameters,
			}

			sentMessage, err := f.messageSendService.SendMessage(ctx, sendMessagePayload)

			if err != nil {
				slog.ErrorContext(ctx, "Failed to send message", slog.Any("error", err), slog.Any("payload", sendMessagePayload))
				return nil, fmt.Errorf("failed to send message: %w", err)
			}

			sentMessagesIds = append(sentMessagesIds, sentMessage.Id)
		}

		response = append(response, &ForwardMessagesResponse{
			Id:          contact.Id,
			MessagesIds: sentMessagesIds,
		})
	}

	return response, nil
}
