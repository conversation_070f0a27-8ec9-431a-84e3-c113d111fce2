package http

import (
	"context"
	"fmt"
	"log/slog"
	"reflect"

	"github.com/go-playground/validator/v10"
	"github.com/gofiber/fiber/v3"
)

var validate = validator.New()

func ParseAndValidatePayload[T interface{}](ctx fiber.Ctx, payload *T) (returned bool, err error) {
	defer handlePanic(&returned, &err)

	if err = ctx.Bind().Body(payload); err != nil {
		slog.ErrorContext(ctx.(context.Context), "parse failed", slog.Any("err", err))
		err = fiber.ErrBadRequest
		return true, err
	}

	if err = validate.Struct(payload); err != nil {
		slog.ErrorContext(ctx.(context.Context), "validation failed", slog.Any("err", err), slog.Any("payload", payload))
		validationErrors := FormatValidationErrors(err, reflect.TypeOf(payload).Elem())
		err = ctx.Status(fiber.StatusBadRequest).JSON(validationErrors)
		return true, err
	}

	return false, nil
}

func handlePanic(returned *bool, err *error) {
	if r := recover(); r != nil {
		if rErr, ok := r.(error); ok {
			*err = rErr
			return
		}
		*returned = true
		*err = fmt.Errorf("recovered from panic: %v", r)
	}
}
