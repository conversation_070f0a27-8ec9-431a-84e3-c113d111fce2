package repositories

import (
	"digisac-go/worker/core/models"

	"gorm.io/gorm"
)

type WhatsappBusinessTemplateRepository interface {
	Repository[models.WhatsappBusinessTemplate]
}

type whatsappBusinessTemplateRepository struct {
	*GormRepository[models.WhatsappBusinessTemplate]
}

func NewWhatsappBusinessTemplateRepository(db *gorm.DB) WhatsappBusinessTemplateRepository {
	return &whatsappBusinessTemplateRepository{
		GormRepository: NewGormRepository[models.WhatsappBusinessTemplate](db),
	}
}
