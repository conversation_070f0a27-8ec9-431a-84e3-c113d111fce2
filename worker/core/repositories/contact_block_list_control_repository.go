package repositories

import (
	"digisac-go/worker/core/models"

	"gorm.io/gorm"
)

type ContactBlockListControlRepository interface {
	Repository[models.ContactBlockListControl]
}

type contactBlockListControlRepository struct {
	*GormRepository[models.ContactBlockListControl]
}

func NewContactBlockListControlRepository(db *gorm.DB) ContactBlockListControlRepository {
	return &contactBlockListControlRepository{
		GormRepository: NewGormRepository[models.ContactBlockListControl](db),
	}
}
