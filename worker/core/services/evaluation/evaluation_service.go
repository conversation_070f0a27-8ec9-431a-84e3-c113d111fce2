package evaluation

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"digisac-go/worker/core/http/payloads"
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/repositories"
	"digisac-go/worker/core/services/answer"
	"digisac-go/worker/core/services/event"
	"digisac-go/worker/core/services/message/message_sender"
	"log/slog"

	gormrepository "github.com/ikateclab/gorm-repository"
)

type EvaluationService struct {
	contactRepository  repositories.ContactRepository
	answerRepository   repositories.AnswerRepository
	questionRepository repositories.QuestionRepository
	messageSendService *message_sender.SendService
	answerService      *answer.AnswerService
	eventService       *event.EventService
}

func NewEvaluationService(
	contactRepository repositories.ContactRepository,
	answerRepository repositories.AnswerRepository,
	questionRepository repositories.QuestionRepository,
	messageSendService *message_sender.SendService,
	answerService *answer.AnswerService,
	eventService *event.EventService,
) *EvaluationService {
	return &EvaluationService{
		contactRepository:  contactRepository,
		answerRepository:   answerRepository,
		questionRepository: questionRepository,
		messageSendService: messageSendService,
		answerService:      answerService,
		eventService:       eventService,
	}
}

func (e *EvaluationService) SendMessage(ctx context.Context, contact *models.Contact, text string) (message *models.Message, err error) {
	message, err = e.messageSendService.SendMessage(ctx,
		&payloads.SendMessagePayload{
			ContactId:      contact.Id,
			AccountId:      contact.AccountId,
			ServiceId:      contact.ServiceId,
			Text:           text,
			Origin:         "bot",
			DontOpenTicket: true,
		},
	)

	if err != nil {
		slog.ErrorContext(ctx, "Failed to send message", slog.String("contactId", contact.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to send message: %w", err)
	}

	return message, nil
}

func (e *EvaluationService) HandleAnswer(ctx context.Context, contact *models.Contact, text string, tx *gormrepository.Tx, etx *event.Etx) (shouldOpenTicket bool, err error) {
	question, err := e.questionRepository.FindById(ctx, contact.Data.Survey.QuestionId, repositories.WithTx(tx))

	if err != nil {
		slog.ErrorContext(ctx, "Failed to find question", slog.String("question_id", contact.Data.Survey.QuestionId.String()), slog.String("error", err.Error()))
		return false, fmt.Errorf("failed to find question: %w", err)
	}

	// Verifica se a pesquisa expirou
	if time.Now().After(*contact.Data.Survey.ExpiresAt) {
		_, err = e.answerService.CreateAnswerForContact(ctx, contact, "-1", tx)

		if err != nil {
			slog.ErrorContext(ctx, "Failed to create expired survey answer", slog.String("contactId", contact.Id.String()), slog.String("error", err.Error()))
			return false, fmt.Errorf("failed to create expired survey answer: %w", err)
		}

		err = e.answerService.UnflagSurveyFromContact(ctx, contact, tx, etx)

		if err != nil {
			slog.ErrorContext(ctx, "Failed to unflag expired survey from contact", slog.String("contactId", contact.Id.String()), slog.String("error", err.Error()))
			return false, fmt.Errorf("failed to unflag expired survey from contact: %w", err)
		}
		return true, nil
	}

	// Executa em background para não gerar lock no banco
	go func() {
		tx = e.answerRepository.BeginTransaction()
		defer tx.Finish(&err)

		answer, err := e.answerRepository.FindOne(ctx, repositories.WithQueryStruct(
			map[string]interface{}{
				"ticketId": contact.Data.Survey.TicketId,
			},
		), repositories.WithTx(tx))

		if err != nil && err.Error() != "record not found" {
			slog.ErrorContext(ctx, "Failed to find answer", slog.String("ticketId", contact.Data.Survey.TicketId.String()), slog.String("error", err.Error()))
			return
		}

		isValid := func() bool {
			if answer != nil && answer.Text != "nv" && question.ReasonMessage != "" && answer.Reason == "" {
				return true
			}

			if question.Type == "nps" {
				num, err := strconv.Atoi(text)
				return err == nil && num >= 0 && num <= 10
			}
			if question.Type == "csat" {
				num, err := strconv.Atoi(text)
				return err == nil && num > 0 && num <= 5
			}

			return false
		}()

		if !isValid {
			if contact.Data.Survey.Tries <= 1 {
				_, err = e.answerService.CreateAnswerForContact(ctx, contact, "-1", tx)

				if err != nil {
					slog.ErrorContext(ctx, "Failed to create final attempt answer", slog.String("contactId", contact.Id.String()), slog.String("error", err.Error()))
					return
				}

				err = e.answerService.UnflagSurveyFromContact(ctx, contact, tx, nil)

				if err != nil {
					slog.ErrorContext(ctx, "Failed to unflag survey after final attempt", slog.String("contactId", contact.Id.String()), slog.String("error", err.Error()))
					return
				}

				return
			}

			if question.InvalidMessage != "" {
				_, err = e.SendMessage(ctx, contact, question.InvalidMessage)

				if err != nil {
					slog.ErrorContext(ctx, "Failed to send invalid message", slog.String("contactId", contact.Id.String()), slog.String("error", err.Error()))
					return
				}
			}
			_, err = e.DecrementTries(ctx, contact, tx)

			if err != nil {
				slog.ErrorContext(ctx, "Failed to decrement tries", slog.String("contactId", contact.Id.String()), slog.String("error", err.Error()))
				return
			}

			return
		}

		_, err = e.answerService.UpdateAnswerForContact(ctx, contact, answer, text, tx)

		if err != nil {
			slog.ErrorContext(ctx, "Failed to update answer", slog.String("contactId", contact.Id.String()), slog.String("error", err.Error()))
			return
		}
		if answer == nil || answer.Text == "nv" {
			return
		}

		// Executa em background, para não comprometer a transação atual
		go func() {
			time.Sleep(2 * time.Second)

			if question.ReasonMessage != "" && answer.Reason == "" {
				_, err = e.SendMessage(ctx, contact, question.ReasonMessage)

				if err != nil {
					slog.ErrorContext(ctx, "Failed to send reason message", slog.String("contactId", contact.Id.String()), slog.String("error", err.Error()))
					return
				}
			}

			if (question.ReasonMessage != "" && answer.Reason != "") || question.ReasonMessage == "" {
				if question.SuccessMessage != "" {
					_, err = e.SendMessage(ctx, contact, question.SuccessMessage)

					if err != nil {
						slog.ErrorContext(ctx, "Failed to send success message", slog.String("contactId", contact.Id.String()), slog.String("error", err.Error()))
						return
					}
				}
				err = e.answerService.UnflagSurveyFromContact(ctx, contact, nil, etx)

				if err != nil {
					slog.ErrorContext(ctx, "Failed to unflag survey after completion", slog.String("contactId", contact.Id.String()), slog.String("error", err.Error()))
					return
				}

				return
			}
		}()
	}()

	return false, nil

}

func (e *EvaluationService) DecrementTries(ctx context.Context, contact *models.Contact, tx *gormrepository.Tx) (_ *models.Contact, err error) {
	contact.Data.Survey.Tries--

	err = e.contactRepository.UpdateById(ctx, contact.Id, contact, repositories.WithTx(tx))

	if err != nil {
		slog.ErrorContext(ctx, "Failed to decrement survey tries", slog.String("contactId", contact.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to decrement survey tries: %w", err)
	}

	return contact, nil
}
