{"message": {"items": [{"name": "type", "desc": "Selecione o tipo de alteração que você está commitando:", "form": "select", "options": [{"name": "feat", "desc": "feat: Uma nova funcionalidade"}, {"name": "fix", "desc": "fix: Correção de um bug"}, {"name": "docs", "desc": "docs: Alterações apenas na documentação"}, {"name": "style", "desc": "style: Alterações que não afetam o significado do código\n            (espaços em branco, formatação, ponto e vírgulas ausentes, etc)"}, {"name": "refactor", "desc": "refactor: Uma alteração de código que não corrige um bug nem adiciona uma funcionalidade"}, {"name": "perf", "desc": "perf: Uma alteração de código que melhora o desempenho"}, {"name": "test", "desc": "test: <PERSON><PERSON><PERSON><PERSON> testes ausentes"}, {"name": "chore", "desc": "chore: Alterações no processo de build ou ferramentas auxiliares\n            e bibliotecas como geração de documentação"}, {"name": "revert", "desc": "revert: <PERSON><PERSON><PERSON> para um commit anterior"}, {"name": "WIP", "desc": "WIP: <PERSON><PERSON><PERSON><PERSON> em andamento"}], "required": true}, {"name": "scope", "desc": "Escopo. Em Inglês, pode ser qualquer coisa especificando o local da alteração do commit (users, services, contacts, etc):", "form": "input", "required": true}, {"name": "subject", "desc": "Assunto. Descrição concisa das alterações. Português, imperativo, minúsculas e sem ponto final:", "form": "input", "required": true}], "template": "{{.type}}({{.scope}}): {{.subject}}"}}