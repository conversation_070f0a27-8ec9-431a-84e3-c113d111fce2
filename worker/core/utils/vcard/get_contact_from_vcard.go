package vcard

import (
	"fmt"
	"strings"
)

func GetContactFromVCard(vcard string) (*VCardContact, error) {
	if vcard == "" {
		return nil, fmt.Errorf("vcard is empty")
	}

	lines := strings.Split(vcard, "\n")
	contact := &VCardContact{
		Name:   &ContactName{},
		Phones: []*ContactPhones{},
	}

	for _, line := range lines {
		line = strings.TrimSpace(line)

		if strings.HasPrefix(line, "N:") {
			// Ex: N:<PERSON>;<PERSON>;;;
			parts := strings.Split(line[2:], ";")
			if len(parts) >= 2 {
				contact.Name.LastName = strings.TrimSpace(parts[0])
				contact.Name.FirstName = strings.TrimSpace(parts[1])
			}
		} else if strings.HasPrefix(line, "FN:") {
			// Ex: FN:<PERSON> (op<PERSON>, j<PERSON> usamos N para isso)
			continue
		} else if strings.HasPrefix(line, "item1.TEL;waid=") {
			// Ex: item1.TEL;waid=5511999999999:5511999999999
			waidSection := strings.TrimPrefix(line, "item1.TEL;waid=")
			parts := strings.SplitN(waidSection, ":", 2)
			if len(parts) == 2 {
				phone := &ContactPhones{
					WaId:  strings.TrimSpace(parts[0]),
					Phone: strings.TrimSpace(parts[1]),
					Type:  "Mobile",
				}
				contact.Phones = append(contact.Phones, phone)
			}
		}
	}

	if contact.Name.FirstName == "" && contact.Name.LastName == "" && len(contact.Phones) == 0 {
		return nil, fmt.Errorf("vcard content is invalid or incomplete")
	}

	return contact, nil
}
