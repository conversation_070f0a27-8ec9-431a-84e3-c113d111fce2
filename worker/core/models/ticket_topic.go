package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type TicketTopic struct {
	Id         uuid.UUID       `gorm:"type:uuid;primary_key" json:"id,omitempty"`
	Name       string          `gorm:"type:text;not null" json:"name,omitempty"`
	ArchivedAt *time.Time      `gorm:"type:timestamptz" json:"archivedAt,omitempty"`
	AccountId  uuid.UUID       `gorm:"type:uuid;not null" json:"accountId,omitempty"`
	Account    *Account        `gorm:"foreignKey:AccountId" json:"account,omitempty"`
	Tickets    []*Ticket       `gorm:"many2many:ticket_ticket_topics;foreignKey:Id;joinForeignKey:TicketTopicId;References:Id;joinReferences:TicketId" json:"tickets,omitempty"`
	CreatedAt  *time.Time      `json:"createdAt,omitempty"`
	UpdatedAt  *time.Time      `json:"updatedAt,omitempty"`
	DeletedAt  *gorm.DeletedAt `gorm:"index" json:"deletedAt,omitempty"`
}

func (TicketTopic) TableName() string {
	return "ticket_topics"
}

func (t *TicketTopic) BeforeCreate(tx *gorm.DB) (err error) {
	t.Id = uuid.New()
	return
}
