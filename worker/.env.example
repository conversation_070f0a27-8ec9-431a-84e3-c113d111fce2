GO_ENV="development"
DEPLOYMENT="development"
BRANCH="default"

# Api
PUBLIC_URL=https://notable-bat-honestly.ngrok-free.app/v1

# Worker
WORKERS_GO_URL="http://app-workers-go:8000"
WORKERS_NODE_URL="http://app-workers:8000"
WORKERS_GO_PORT="8000"

# Wall Api (Whatsapp)
WHATSAPP_API_HTTP_ADDRESS="http://wall-api-1:5060"

# Nao usar "http://" pois é usado em conexões GRPC
WHATSAPP_API_GRPC_ADDRESS="wall-api-1:50063"

WHATSAPP_API_TOKEN="" # é necessário gerar

# Postgresql DB
DB_HOST="postgresql"
DB_USERNAME="postgres"
DB_PASSWORD="postgres"
DB_NAME="mandeumzap"
DB_PORT="5432"
DB_MAX_CONNS="2000"
DB_LOG="true"

# Logs
LOG_TYPE="json"

# Crypto
ENCRYPTION_KEY="cd6fb4179d7e818a2674d0b3d5fbe4cd79ea48a30470d5346486494ececd113c"

# Storage (s3 ou oracle, default é oracle)
STORAGE_DRIVER=oracle

# Aws
AWS_ACCESS_KEY_ID="********************"
AWS_SECRET_ACCESS_KEY="lwkXoFLwGjciJSQWgunlqfZmiq0VaNHnOep6Gt1p"
AWS_BUCKET_NAME="mandeumzap-storage-test"
AWS_REGION="us-east-1"

# Oracle
ORACLE_ACCESS_KEY_ID="afc6c56f17a156a556e206c497e46fca9f07f942"
ORACLE_SECRET_ACCESS_KEY="bhq1TLyajauqZI14IvXG+3FVybywvoDNEdYHYwXVvMI="
ORACLE_BUCKETS_NAMES="digisac-storage,digisac-storage-2,digisac-storage-3,digisac-storage-4,digisac-storage-5,digisac-storage-6,digisac-storage-7,digisac-storage-8,digisac-storage-9,digisac-storage-10"
ORACLE_BUCKET_NAME_FALLBACK="digisac-storage-test-fallback"
ORACLE_REGION="sa-vinhedo-1"
ORACLE_ENDPOINT="https://axvaplbwrlcl.compat.objectstorage.sa-vinhedo-1.oraclecloud.com"

# Gupshup
GUPSHUP_EMAIL=
GUPSHUP_PASSWORD=

# Meta
FACEBOOK_APP_ID=139638411265417

# Waba
DEFAULT_HSM_LIMIT=3000
DISABLE_WABA_WEBHOOK_URL_SET=false

# Drivers Gateway
# produção: (https://gateway.digisac.app)
DRIVERS_GATEWAY_URL=http://127.0.0.1:3334

QUEUE_MANAGER_DISPATCHER_URL="http://message-broker-producer:2000/dispatch"

MOCK_DRIVER_URL=https://mock-drivers.ikatec.cloud
USE_MOCK_DRIVER=false