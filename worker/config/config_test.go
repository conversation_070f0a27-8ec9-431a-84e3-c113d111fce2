//go:build unit

package config

import (
	"context"
	"flag"
	"os"
	"path"
	"path/filepath"
	"runtime"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestMain(m *testing.M) {
	// Setup test environment
	setupTestEnv()
	code := m.Run()
	// Cleanup
	os.Exit(code)
}

func setupTestEnv() {
	testEnvVars := map[string]string{
		"WHATSAPP_API_HTTP_ADDRESS":    "http://localhost:8080",
		"WHATSAPP_API_GRPC_ADDRESS":    "localhost:50051",
		"WHATSAPP_API_TOKEN":           "test-token",
		"PUBLIC_URL":                   "http://api.example.com",
		"ENCRYPTION_KEY":               "test-key",
		"WORKERS_GO_PORT":              "8000",
		"API_ADDRESS":                  "http://localhost:3000",
		"LOG_TYPE":                     "json",
		"DB_HOST":                      "localhost",
		"DB_PORT":                      "5432",
		"DB_LOG":                       "true",
		"DB_USERNAME":                  "testuser",
		"DB_PASSWORD":                  "testpass",
		"DB_NAME":                      "testdb",
		"DB_MAX_CONNS":                 "10",
		"DEPLOYMENT":                   "test",
		"BRANCH":                       "main",
		"STORAGE_DRIVER":               "local",
		"AWS_REGION":                   "us-east-1",
		"AWS_ACCESS_KEY_ID":            "test-key-id",
		"AWS_SECRET_ACCESS_KEY":        "test-secret",
		"AWS_BUCKET_NAME":              "test-bucket",
		"ORACLE_REGION":                "us-phoenix-1",
		"ORACLE_ACCESS_KEY_ID":         "oracle-key-id",
		"ORACLE_SECRET_ACCESS_KEY":     "oracle-secret",
		"ORACLE_BUCKETS_NAMES":         "bucket1,bucket2",
		"ORACLE_BUCKET_NAME_FALLBACK":  "fallback-bucket",
		"ORACLE_ENDPOINT":              "https://oracle.example.com",
		"GUPSHUP_EMAIL":                "<EMAIL>",
		"GUPSHUP_PASSWORD":             "gupshup-pass",
		"DEFAULT_HSM_LIMIT":            "100",
		"DISABLE_WABA_WEBHOOK_URL_SET": "true",
		"DRIVERS_GATEWAY_URL":          "http://gateway.example.com",
		"FACEBOOK_APP_ID":              "fb-app-id",
	}

	for k, v := range testEnvVars {
		os.Setenv(k, v)
	}
}

func TestGetDotEnvPath(t *testing.T) {
	// Salvar estado original do flag test.v
	oldArgs := os.Args
	oldFlagCommandLine := flag.CommandLine
	defer func() {
		os.Args = oldArgs
		flag.CommandLine = oldFlagCommandLine
	}()

	tests := []struct {
		name     string
		isTest   bool
		wantFile string
	}{
		{
			name:     "Should return .env path when not in test environment",
			isTest:   false,
			wantFile: "../.env",
		},
		{
			name:     "Should return .env.test path when in test environment",
			isTest:   true,
			wantFile: "../.env.test",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Reset flags for each test
			flag.CommandLine = flag.NewFlagSet(os.Args[0], flag.ExitOnError)
			if tt.isTest {
				flag.Bool("test.v", true, "")
				os.Args = []string{"cmd", "-test.v"}
			} else {
				os.Args = []string{"cmd"}
			}
			flag.Parse()

			// Get expected path
			_, filename, _, _ := runtime.Caller(0)
			dirname := filepath.Dir(filename)
			expected := path.Join(dirname, tt.wantFile)

			// Get actual path
			got := getDotEnvPath(context.Background())

			if got != expected {
				t.Errorf("getDotEnvPath() = %v, want %v", got, expected)
			}
		})
	}
}

func TestGetDotEnvPath_PanicOnRuntimeError(t *testing.T) {
	// Mock runtime.Caller to fail
	originalRuntimeCaller := runtimeCaller
	defer func() { runtimeCaller = originalRuntimeCaller }()

	runtimeCaller = func(skip int) (pc uintptr, file string, line int, ok bool) {
		return 0, "", 0, false
	}

	defer func() {
		if r := recover(); r == nil {
			t.Errorf("getDotEnvPath() should panic when runtime.Caller fails")
		}
	}()

	getDotEnvPath(context.Background())
}

func TestLoadWithMissingFile(t *testing.T) {
	// Save original path function and restore after test
	original := getDotEnvPath
	defer func() { getDotEnvPath = original }()

	// Mock getDotEnvPath to return a non-existent file
	getDotEnvPath = func(ctx context.Context) string {
		return "/non/existent/path/.env"
	}

	require.NotPanics(t, func() {
		Load(context.Background())
	})

}

func TestConfigGetters(t *testing.T) {
	_, err := NewConfig()

	require.NoError(t, err, "NewConfig should not return an error")

	tests := []struct {
		name     string
		got      interface{}
		expected interface{}
	}{
		{"GetWhatsappApiHttpAddress", GetWhatsappApiHttpAddress(), "http://localhost:8080"},
		{"GetWhatsappApiGrpcAddress", GetWhatsappApiGrpcAddress(), "localhost:50051"},
		{"GetWhatsappApiToken", GetWhatsappApiToken(), "test-token"},
		{"GetApiPublicUrl", GetApiPublicUrl(), "http://api.example.com"},
		{"GetEncryptionKey", GetEncryptionKey(), "test-key"},
		{"GetWorkersGoPort", GetWorkersGoPort(), "8000"},
		{"GetApiAddress", GetApiAddress(), ":8000"},
		{"GetLogType", GetLogType(), "json"},
		{"GetDbHost", GetDbHost(), "localhost"},
		{"GetDbPort", GetDbPort(), "5432"},
		{"GetDbLog", GetDbLog(), true},
		{"GetDbUsername", GetDbUsername(), "testuser"},
		{"GetDbPassword", GetDbPassword(), "testpass"},
		{"GetDbName", GetDbName(), "testdb"},
		{"GetDbMaxConns", GetDbMaxConns(context.Background()), 10},
		{"GetDeployment", GetDeployment(), "test"},
		{"GetBranch", GetBranch(), "main"},
		{"GetStorageDriver", GetStorageDriver(), "local"},
		{"GetAwsRegion", GetAwsRegion(), "us-east-1"},
		{"GetAwsAccessKeyId", GetAwsAccessKeyId(), "test-key-id"},
		{"GetAwsSecretAccessKey", GetAwsSecretAccessKey(), "test-secret"},
		{"GetAwsBucketName", GetAwsBucketName(), "test-bucket"},
		{"GetOracleRegion", GetOracleRegion(), "us-phoenix-1"},
		{"GetOracleAccessKeyId", GetOracleAccessKeyId(), "oracle-key-id"},
		{"GetOracleSecretAccessKey", GetOracleSecretAccessKey(), "oracle-secret"},
		{"GetOracleBucketsNames", GetOracleBucketsNames(), "bucket1,bucket2"},
		{"GetOracleBucketNameFallback", GetOracleBucketNameFallback(), "fallback-bucket"},
		{"GetOracleEndpoint", GetOracleEndpoint(), "https://oracle.example.com"},
		{"GetGupshupEmail", GetGupshupEmail(), "<EMAIL>"},
		{"GetGupshupPassword", GetGupshupPassword(), "gupshup-pass"},
		{"GetDefaultHsmLimit", GetDefaultHsmLimit(context.Background()), 100},
		{"GetDisableWabaWebhookUrlSet", GetDisableWabaWebhookUrlSet(), true},
		{"GetDriversGatewayUrl", GetDriversGatewayUrl(), "http://gateway.example.com"},
		{"GetFacebookAppId", GetFacebookAppId(), "fb-app-id"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.got != tt.expected {
				t.Errorf("%s = %v, want %v", tt.name, tt.got, tt.expected)
			}
		})
	}
}

func TestNewConfig(t *testing.T) {
	cfg, err := NewConfig()

	require.NoError(t, err, "NewConfig should not return an error")

	if cfg == nil {
		t.Error("NewConfig() returned nil")
	}
}

func TestGetDefaultHsmLimitNegative(t *testing.T) {
	os.Setenv("DEFAULT_HSM_LIMIT", "-1")
	defer os.Unsetenv("DEFAULT_HSM_LIMIT")

	limit := GetDefaultHsmLimit(context.Background())
	if limit != 3000 {
		t.Errorf("getDefaultHsmLimit() = %d, want 3000", limit)
	}
}

func TestGetDbMaxConnsValid(t *testing.T) {
	tests := []struct {
		name     string
		envValue string
		want     int
	}{
		{"valid positive number", "10", 10},
		{"valid max number", "999999", 999999},
		{"valid min number", "1", 1},
		{"negative number", "-10", 10}, // Should default to 10
		{"zero", "0", 10},              // Should default to 10
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			os.Setenv("DB_MAX_CONNS", tt.envValue)
			defer os.Unsetenv("DB_MAX_CONNS")

			got := GetDbMaxConns(context.Background())
			require.Equal(t, tt.want, got)
		})
	}
}

func TestGetDbMaxConnsInvalid(t *testing.T) {
	tests := []struct {
		name     string
		envValue string
	}{
		{"invalid string", "invalid"},
		{"empty string", ""},
		{"decimal number", "10.5"},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			os.Setenv("DB_MAX_CONNS", tt.envValue)
			defer os.Unsetenv("DB_MAX_CONNS")

			require.Panics(t, func() {
				GetDbMaxConns(context.Background())
			}, "GetDbMaxConns should panic with invalid input")
		})
	}
}
