//go:build unit

package common

import (
	"bytes"
	"context"
	"fmt"
	"strings"
	"testing"

	"github.com/stretchr/testify/require"
)

func TestGetChecksumEmptyBytes(t *testing.T) {
	reader := bytes.NewReader(nil)
	result, err := GetChecksum(context.Background(), reader)

	require.NoError(t, err)
	require.Equal(t, emptyReaderChecksum, result)
}

func TestGetChecksumTwiceBytes(t *testing.T) {
	reader := bytes.NewReader([]byte("Hello World!"))
	expected := "2ef7bde608ce5404e97d5f042f95f89f1c232871"

	result, err := GetChecksum(context.Background(), reader)

	require.NoError(t, err)
	require.Equal(t, expected, result)

	result, err = GetChecksum(context.Background(), reader)

	require.NoError(t, err)
	require.Equal(t, expected, result)
}

func TestGetChecksumEmptyString(t *testing.T) {
	reader := strings.NewReader("")
	result, err := GetChecksum(context.Background(), reader)

	require.NoError(t, err)
	require.Equal(t, emptyReaderChecksum, result)
}

func TestGetChecksumTwiceString(t *testing.T) {
	reader := strings.NewReader("Hello World!")
	expected := "2ef7bde608ce5404e97d5f042f95f89f1c232871"

	result, err := GetChecksum(context.Background(), reader)

	require.NoError(t, err)
	require.Equal(t, expected, result)

	result, err = GetChecksum(context.Background(), reader)

	require.NoError(t, err)
	require.Equal(t, expected, result)
}

type InvalidReader struct{}

func (e *InvalidReader) Read(p []byte) (int, error) {
	return 0, fmt.Errorf("failed to read")
}

func (e *InvalidReader) Seek(offset int64, whence int) (int64, error) {
	return 0, fmt.Errorf("failed to seek")
}

func TestGetChecksumInvalidReader(t *testing.T) {
	reader := &InvalidReader{}
	result, err := GetChecksum(context.Background(), reader)

	require.EqualError(t, err, "failed to copy from reader to hasher: failed to read")
	require.Equal(t, "", result)
}
