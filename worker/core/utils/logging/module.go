package logging

import (
	"context"
	"log/slog"

	"go.uber.org/fx"
)

// initLogger é uma função que garante que o logger seja inicializado
func initLogger(logger *slog.Logger) {
	// O logger já foi inicializado pela função SetupSlog
	// Esta função é apenas para garantir que o logger seja injetado e usado
	slog.InfoContext(context.TODO(), "Logger initialized and ready to use")
}

// Module provides logging components
var Module = fx.Module("logging",
	fx.Provide(
		SetupSlog,
	),
	// Garantir que o logger seja inicializado durante a inicialização da aplicação
	fx.Invoke(initLogger),
)
