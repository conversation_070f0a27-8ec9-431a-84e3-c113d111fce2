package models

import (
	"fmt"
	"strings"

	"gorm.io/gorm"
)

func CreateEnumIfNotExists(db *gorm.DB, enumName string, values []string) error {
	// Adiciona aspas simples em cada valor do enum
	quotedValues := make([]string, len(values))
	for i, v := range values {
		quotedValues[i] = fmt.Sprintf("'%s'", v)
	}

	createEnumSQL := fmt.Sprintf(`
        DO $$ 
        BEGIN
            IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = '%s') THEN
                CREATE TYPE %s AS ENUM (%s);
            END IF;
        END $$;
    `, enumName, enumName, strings.Join(quotedValues, ", "))

	if err := db.Exec(createEnumSQL).Error; err != nil {
		return fmt.Errorf("failed to create enum %s: %w", enumName, err)
	}

	return nil
}

type CreateEnumInterface interface {
	CreateEnums(db *gorm.DB) error
}
