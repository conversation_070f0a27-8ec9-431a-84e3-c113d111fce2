package base_waba_adapter

import (
	"context"
	"digisac-go/worker/core/adapters/adapter_types"
	"digisac-go/worker/core/models"
)

// getAuthenticationText retorna o texto de autenticação para o idioma especificado
func GetAuthenticationText(language string) string {
	texts := map[string]string{
		"en":    "{{1}} is your verification code.",
		"es":    "Tu código de verificación es {{1}}.",
		"pt_BR": "Seu código de verificação é {{1}}.",
	}

	if text, ok := texts[language]; ok {
		return text
	}

	// Retorna o texto em inglês como padrão
	return texts["en"]
}

// getExpirationWarningText retorna o texto de aviso de expiração para o idioma especificado
func GetExpirationWarningText(language string) string {
	texts := map[string]string{
		"en":    "This code expires in {NUM_MINUTES} minutes.",
		"es":    "Este código caduca en {NUM_MINUTES} minutos.",
		"pt_BR": "Este código expira em {NUM_MINUTES} minutos.",
	}

	if text, ok := texts[language]; ok {
		return text
	}

	// Retorna o texto em inglês como padrão
	return texts["en"]
}

// processAuthenticationTemplate processa um template de autenticação
func (a *BaseWabaAdapter) ProcessAuthenticationTemplate(ctx context.Context, template *models.WhatsappBusinessTemplate, components []*models.WhatsappBusinessComponent) []*models.WhatsappBusinessComponent {
	// Substituir o texto do corpo pelo texto padrão de autenticação
	bodyText := GetAuthenticationText(template.Language)

	// Adicionar componente BODY com o texto de autenticação
	// Para templates de autenticação, precisamos definir o parâmetro para o código de verificação
	components = append(components, &models.WhatsappBusinessComponent{
		Type:   adapter_types.ComponentTypeBody,
		Text:   bodyText,
		Params: []string{"VERIFICATION_CODE"}, // Parâmetro para o código de verificação
	})

	// Adicionar componente FOOTER com o texto de aviso de expiração
	components = append(components, &models.WhatsappBusinessComponent{
		Type:                  adapter_types.ComponentTypeFooter,
		Text:                  GetExpirationWarningText(template.Language),
		CodeExpirationMinutes: 30, // Valor padrão de 30 minutos
	})

	return components
}
