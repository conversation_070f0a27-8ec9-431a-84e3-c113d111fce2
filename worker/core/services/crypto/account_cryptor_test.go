//go:build unit

package crypto_test

import (
	"context"
	"digisac-go/worker/config"
	"digisac-go/worker/core/models"
	crypto "digisac-go/worker/core/services/crypto"
	cryptoutil "digisac-go/worker/core/utils/crypto_utils"
	"encoding/hex"
	"testing"

	"github.com/stretchr/testify/suite"
	"go.uber.org/fx"
)

type AccountCryptorServiceTestSuite struct {
	suite.Suite
	accountCryptor *crypto.AccountCryptor
}

func (suite *AccountCryptorServiceTestSuite) SetupSuite() {
	fx.New(
		fx.Provide(
			config.NewConfig,
			crypto.NewAccountCryptor,
		),
		fx.Populate(
			&suite.accountCryptor,
		),
	)
}

func (suite *AccountCryptorServiceTestSuite) TestGetAppEncryptionKey() {
	config.Load(context.Background())

	key, err := suite.accountCryptor.GetAppEncryptionKey(context.Background())

	suite.Require().NoError(err)
	suite.Require().NotNil(key)
}

func (suite *AccountCryptorServiceTestSuite) TestCreateEncryptionKey() {
	key, err := suite.accountCryptor.CreateEncryptionKey(context.Background())
	suite.Require().NoError(err)
	suite.Require().Equal(32, len(key)) // Should generate a 32-byte key
}

func (suite *AccountCryptorServiceTestSuite) TestCreateEncryptedEncryptionKey() {
	config.Load(context.Background())

	encryptedKey, err := suite.accountCryptor.CreateEncryptedEncryptionKey(context.Background())
	suite.Require().NoError(err)
	suite.Require().NotNil(encryptedKey)
}

func (suite *AccountCryptorServiceTestSuite) TestDecryptEncryptionKey() {
	config.Load(context.Background())

	encryptedKey, _ := hex.DecodeString("6d2c0348b2b9236414df19c10364622353bb821315f120b572076d63cc7654e21b68e78af441eef323d67e92f28fb20c")
	decryptedKey, err := suite.accountCryptor.DecryptEncryptionKey(context.Background(), encryptedKey)
	suite.Require().NoError(err)
	suite.Require().NotNil(decryptedKey)
}

func (suite *AccountCryptorServiceTestSuite) TestGetAccountEncryptionKey() {
	account := &models.Account{
		EncryptionKey: "6d2c0348b2b9236414df19c10364622353bb821315f120b572076d63cc7654e21b68e78af441eef323d67e92f28fb20c",
		Settings: &models.AccountSettings{
			EncryptionDisabled: false,
		},
	}

	key, err := suite.accountCryptor.GetAccountEncryptionKey(context.Background(), account)
	suite.Require().NoError(err)
	suite.Require().NotNil(key)
}

func (suite *AccountCryptorServiceTestSuite) TestEncryptTextForAccount() {
	account := &models.Account{
		EncryptionKey: "6d2c0348b2b9236414df19c10364622353bb821315f120b572076d63cc7654e21b68e78af441eef323d67e92f28fb20c",
		Settings: &models.AccountSettings{
			EncryptionDisabled: false,
		},
	}

	text := "Test message"
	encryptedText, err := suite.accountCryptor.EncryptTextForAccount(context.Background(), account, text, true)

	suite.Require().NoError(err)
	suite.Require().NotEqual(text, encryptedText)

	decryptedText, err := suite.accountCryptor.DecryptTextForAccount(context.Background(), account, encryptedText, true)
	suite.Require().NoError(err)
	suite.Require().Equal(text, decryptedText)
}

func (suite *AccountCryptorServiceTestSuite) TestEncryptBufferForAccountWithConcatenatedIv() {
	account := &models.Account{
		EncryptionKey: "6d2c0348b2b9236414df19c10364622353bb821315f120b572076d63cc7654e21b68e78af441eef323d67e92f28fb20c",
		Settings: &models.AccountSettings{
			EncryptionDisabled: false,
		},
	}

	buffer := []byte("Test buffer data")
	encryptedBuffer, err := suite.accountCryptor.EncryptBufferForAccountWithConcatenatedIv(context.Background(), account, buffer)
	suite.Require().NoError(err)

	decryptedBuffer, err := suite.accountCryptor.DecryptBufferForAccountWithConcatenatedIv(context.Background(), account, encryptedBuffer)
	suite.Require().NoError(err)
	suite.Require().Equal(buffer, decryptedBuffer)
}

func (suite *AccountCryptorServiceTestSuite) TestEncryptBufferForAccountWithSeparatedIv() {
	account := &models.Account{
		EncryptionKey: "6d2c0348b2b9236414df19c10364622353bb821315f120b572076d63cc7654e21b68e78af441eef323d67e92f28fb20c",
		Settings: &models.AccountSettings{
			EncryptionDisabled: false,
		},
	}

	buffer := []byte("Test buffer data")
	encryptedBuffer, iv, err := suite.accountCryptor.EncryptBufferForAccountWithSeparatedIv(context.Background(), account, buffer)
	suite.Require().NoError(err)

	suite.Require().Equal(cryptoutil.IvLength, len(iv), "IV length should be 16 bytes")

	decryptedBuffer, err := suite.accountCryptor.DecryptBufferForAccountWithSeparatedIv(context.Background(), account, encryptedBuffer, iv)

	suite.Require().NoError(err)
	suite.Require().Equal(buffer, decryptedBuffer)
}

func (suite *AccountCryptorServiceTestSuite) TestCreateCipherWithIvForAccount() {
	account := &models.Account{
		EncryptionKey: "6d2c0348b2b9236414df19c10364622353bb821315f120b572076d63cc7654e21b68e78af441eef323d67e92f28fb20c",
		Settings: &models.AccountSettings{
			EncryptionDisabled: false,
		},
	}

	cipher, iv, err := suite.accountCryptor.CreateCipherWithIvForAccount(context.Background(), account)
	suite.Require().NoError(err)
	suite.Require().NotNil(cipher)
	suite.Require().Equal(cryptoutil.IvLength, len(iv))
}

func (suite *AccountCryptorServiceTestSuite) TestCreateDecipherForAccount() {
	account := &models.Account{
		EncryptionKey: "6d2c0348b2b9236414df19c10364622353bb821315f120b572076d63cc7654e21b68e78af441eef323d67e92f28fb20c",
		Settings: &models.AccountSettings{
			EncryptionDisabled: false,
		},
	}

	iv, _ := hex.DecodeString("6d2c0348b2b9236414df19c103646223")

	decipher, err := suite.accountCryptor.CreateDecipherForAccount(context.Background(), iv, account)
	suite.Require().NoError(err)
	suite.Require().NotNil(decipher)
}

func TestCryptoServiceTestSuite(t *testing.T) {
	suite.Run(t, new(AccountCryptorServiceTestSuite))
}
