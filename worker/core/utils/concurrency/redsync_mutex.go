package concurrency

import (
	"sync"
	"time"

	"github.com/go-redsync/redsync/v4"
	"github.com/go-redsync/redsync/v4/redis/goredis/v9"
	"github.com/redis/go-redis/v9"
)

// CachedRedsync encapsulates redsync and mutex cache
type CachedRedsync struct {
	redsync *redsync.Redsync
	mutexes map[string]*redsync.Mutex
	mu      sync.Mutex
}

// NewCachedRedsync creates a new CachedRedsync instance with mutex cache
func NewCachedRedsync(redisClient *redis.Client) *CachedRedsync {
	pool := goredis.NewPool(redisClient)
	rs := redsync.New(pool)

	return &CachedRedsync{
		redsync: rs,
		mutexes: make(map[string]*redsync.Mutex),
	}
}

// GetMutex returns a mutex from the cache or creates a new one if it doesn't exist
func (c *CachedRedsync) GetMutex(key string, timeout time.Duration) *redsync.Mutex {
	c.mu.Lock()
	defer c.mu.Unlock()

	mutex, exists := c.mutexes[key]
	if !exists {

		retries := int(timeout.Milliseconds() / 50) // 50 é o retryDelay minimo por padrão

		mutex = c.redsync.NewMutex(
			key,
			redsync.WithExpiry(timeout),
			redsync.WithTries(retries), // retryDelay é de 50 a 250 randomico por padrão
		)
		c.mutexes[key] = mutex
	}

	return mutex
}
