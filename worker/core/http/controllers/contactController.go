package controllers

import (
	"fmt"
	"log/slog"
	"strings"

	"github.com/gofiber/fiber/v3"

	"digisac-go/worker/core/http/middlewares"
	"digisac-go/worker/core/http/payloads"
	"digisac-go/worker/core/services/message/message_loader"
	httputils "digisac-go/worker/core/utils/http_utils"
)

type ContactController struct {
	messageLoadService *message_loader.LoadService
}

func NewContactController(messageLoadService *message_loader.LoadService) *ContactController {
	return &ContactController{
		messageLoadService: messageLoadService,
	}
}

func (c *ContactController) LoadEarlierMessages(ctx fiber.Ctx) (err error) {
	payload := new(payloads.LoadEarlierMessagesRequest)

	if returned, err := httputils.ParseAndValidatePayload(ctx, payload); returned {
		if err != nil {
			slog.ErrorContext(ctx.Context(), "Failed to parse and validate payload",
				slog.Any("payload", payload),
				slog.String("error", err.Error()))
		}
		return fmt.Errorf("failed to parse and validate payload: %w", err)
	}

	result, err := c.messageLoadService.LoadEarlierMessages(middlewares.TracingCtx(ctx), payload.Payload)

	if err != nil {
		slog.ErrorContext(ctx.Context(), "Failed to load earlier messages",
			slog.Any("payload", payload.Payload),
			slog.String("error", err.Error()))
		return c.handleErrors(ctx, err)
	}

	return ctx.JSON(result)
}

func (c *ContactController) handleErrors(ctx fiber.Ctx, err error) *fiber.Error {
	if strings.Contains(err.Error(), "service disconnected") {
		slog.ErrorContext(ctx.Context(), "Service disconnected", slog.String("error", err.Error()))
		return fiber.NewError(500, "Service disconnected.")
	}

	slog.ErrorContext(ctx.Context(), "Internal server error", slog.String("error", err.Error()))

	return fiber.NewError(fiber.ErrInternalServerError.Code, fmt.Sprintf("Internal server error: %s", err.Error()))
}
