package adapter_types

import "digisac-go/worker/core/models"

// Tipos para payloads do WhatsApp Business API

type Context struct {
	MessageId string `json:"message_id"`
}

type ReactionPayload struct {
	MessageId string `json:"message_id"`
	Emoji     string `json:"emoji"`
}

type TextPayload struct {
	Body string `json:"body"`
}

type WabaSendTextPayload struct {
	MessagingProduct string       `json:"messaging_product"`
	RecipientType    string       `json:"recipient_type"`
	To               string       `json:"to"`
	Type             string       `json:"type"`
	Context          *Context     `json:"context"`
	Text             *TextPayload `json:"text"`
}

type AudioPayload struct {
	Id   string `json:"id"`
	Link string `json:"link"`
}

type WabaSendAudioPayload struct {
	MessagingProduct string        `json:"messaging_product"`
	RecipientType    string        `json:"recipient_type"`
	To               string        `json:"to"`
	Type             string        `json:"type"`
	Context          *Context      `json:"context"`
	Audio            *AudioPayload `json:"audio"`
}

type ImagePayload struct {
	Id      string `json:"id"`
	Caption string `json:"caption"`
	Link    string `json:"link"`
}

type WabaSendImagePayload struct {
	MessagingProduct string        `json:"messaging_product"`
	RecipientType    string        `json:"recipient_type"`
	To               string        `json:"to"`
	Type             string        `json:"type"`
	Context          *Context      `json:"context"`
	Image            *ImagePayload `json:"image"`
}

type VideoPayload struct {
	Id      string `json:"id"`
	Caption string `json:"caption"`
	Link    string `json:"link"`
}

type WabaSendVideoPayload struct {
	MessagingProduct string        `json:"messaging_product"`
	RecipientType    string        `json:"recipient_type"`
	To               string        `json:"to"`
	Type             string        `json:"type"`
	Context          *Context      `json:"context"`
	Video            *VideoPayload `json:"video"`
}

type DocumentPayload struct {
	Id       string `json:"id"`
	Caption  string `json:"caption"`
	Link     string `json:"link"`
	Filename string `json:"filename"`
}

type WabaSendDocumentPayload struct {
	MessagingProduct string           `json:"messaging_product"`
	RecipientType    string           `json:"recipient_type"`
	To               string           `json:"to"`
	Type             string           `json:"type"`
	Context          *Context         `json:"context"`
	Document         *DocumentPayload `json:"document"`
}

type TemplateImage struct {
	Link string `json:"link,omitempty"`
}

type TemplateDocument struct {
	Link string `json:"link,omitempty"`
}

type TemplateVideo struct {
	Link string `json:"link,omitempty"`
}

type TemplateAudio struct {
	Link string `json:"link,omitempty"`
}

type TemplateParameter struct {
	Type     string            `json:"type,omitempty"`
	Text     string            `json:"text,omitempty"`
	Image    *TemplateImage    `json:"image,omitempty"`
	Document *TemplateDocument `json:"document,omitempty"`
	Video    *TemplateVideo    `json:"video,omitempty"`
	Audio    *TemplateAudio    `json:"audio,omitempty"`
}

type TemplateComponent struct {
	Type       string               `json:"type,omitempty"` // interativas: header, body, footer, button, location
	SubType    string               `json:"sub_type,omitempty"`
	Index      string               `json:"index,omitempty"`
	Parameters []*TemplateParameter `json:"parameters,omitempty"`
}

type TemplateLanguage struct {
	Code string `json:"code,omitempty"`
}

type TemplatePayload struct {
	Name       string               `json:"name,omitempty"`
	Language   *TemplateLanguage    `json:"language,omitempty"`
	Components []*TemplateComponent `json:"components,omitempty"`
}

type WabaSendTemplatePayload struct {
	MessagingProduct string           `json:"messaging_product,omitempty"`
	RecipientType    string           `json:"recipient_type,omitempty"`
	To               string           `json:"to,omitempty"`
	Type             string           `json:"type,omitempty"`
	Context          *Context         `json:"context,omitempty"`
	Template         *TemplatePayload `json:"template,omitempty"`
}

type WabaSendMessageResponse struct {
	MessagingProduct string `json:"messaging_product"`
	Contacts         []struct {
		Input string `json:"input"`
		WaId  string `json:"wa_id"`
	} `json:"contacts"`
	Messages []struct {
		Id string `json:"id"`
	} `json:"messages"`
}

type WabaSendInteractivePayload struct {
	MessagingProduct string                     `json:"messaging_product"`
	RecipientType    string                     `json:"recipient_type"`
	To               string                     `json:"to"`
	Type             string                     `json:"type"`
	Context          *Context                   `json:"context,omitempty"`
	Interactive      *models.InteractiveMessage `json:"interactive"`
}

type WabaSendReactionPayload struct {
	MessagingProduct string           `json:"messaging_product"`
	RecipientType    string           `json:"recipient_type"`
	To               string           `json:"to"`
	Type             string           `json:"type"`
	Reaction         *ReactionPayload `json:"reaction"`
}

type StickerPayload struct {
	Id   string `json:"id"`
	Link string `json:"link"`
}

type WabaSendStickerPayload struct {
	MessagingProduct string          `json:"messaging_product"`
	RecipientType    string          `json:"recipient_type"`
	To               string          `json:"to"`
	Type             string          `json:"type"`
	Context          *Context        `json:"context"`
	Sticker          *StickerPayload `json:"sticker"`
}
