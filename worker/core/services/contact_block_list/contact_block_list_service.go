package contact_block_list_service

import (
	"context"
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/utils/contact_utils"
	"strings"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type ContactBlockListService struct {
	db *gorm.DB
}

func NewContactBlockListService(db *gorm.DB) *ContactBlockListService {
	return &ContactBlockListService{
		db: db,
	}
}

// NumberIsBlockedBySomeBlocklist verifica se um número está bloqueado em alguma lista de bloqueio
func (s *ContactBlockListService) NumberIsBlockedBySomeBlocklist(ctx context.Context, idFromService string, serviceId uuid.UUID) (*models.ContactBlockListControl, error) {
	var contactBlockListsControls []*models.ContactBlockListControl

	query := `
		with subquery as (
		select
			"id",
			"reason",
			"userId",
			"contactBlockListId",
			"action",
			"createdAt",
			row_number() over (
				partition by "contactBlockListId"
				order by
					"createdAt" desc
			) as idx
		from
			contact_block_lists_controls cblc2
		where
			"serviceId" = ?
			and cblc2."createdAt" >= coalesce((
				select max(cblc3."createdAt")
				from contact_block_lists_controls cblc3
				where cblc3."serviceId" = cblc2."serviceId"
				and cblc3."revertExceptions" = true
			), cblc2."createdAt")
		)
		select
			id, reason, "userId", "contactBlockListId", action, "createdAt"
		from
			subquery
		where
			idx = 1
			and action = 'block'
		order by
			"createdAt" desc
	`

	if err := s.db.Raw(query, serviceId).Scan(&contactBlockListsControls).Error; err != nil {
		return nil, err
	}

	if len(contactBlockListsControls) == 0 {
		return nil, nil
	}

	var ids []uuid.UUID
	for _, cbl := range contactBlockListsControls {
		ids = append(ids, cbl.ContactBlockListId)
	}

	var service models.Service
	if err := s.db.Table("services").Select("type").Where("id = ?", serviceId).First(&service).Error; err != nil {
		return nil, err
	}

	comparableIds := []string{}
	for _, id := range contact_utils.GenerateIdFromServiceVariants(idFromService, service.Type) {
		if id == "" {
			continue
		}
		parts := strings.Split(id, "@")
		comparableIds = append(comparableIds, parts[0])
	}

	if len(comparableIds) == 0 {
		return nil, nil
	}

	query2 := `
		select
			id, "contactBlockListId"
		from
			contact_block_list_items cbli
		where
			"contactBlockListId" in (?)
			and "idFromService" in (?)
		limit
			1;
	`

	var blockListItems []models.ContactBlockListItem
	if err := s.db.Raw(query2, ids, comparableIds).Scan(&blockListItems).Error; err != nil {
		return nil, err
	}

	if len(blockListItems) == 0 {
		return nil, nil
	}

	for _, control := range contactBlockListsControls {
		if control.ContactBlockListId == blockListItems[0].ContactBlockListId {
			return control, nil
		}
	}

	return nil, nil
}
