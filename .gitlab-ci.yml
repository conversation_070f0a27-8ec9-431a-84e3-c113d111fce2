.ensure-git-installed: &ensure-git-installed
  - which git > /dev/null || apk add git

.ensure-curl-installed: &ensure-curl-installed
  - which curl > /dev/null || apk add curl

.ssh-config: &ssh-config
  - which ssh-agent > /dev/null || apk add openssh-client
  - *ensure-git-installed
  - eval $(ssh-agent -s)
  - echo "${DEPLOY_SSH_PRIVATE_KEY}" | tr -d '\r' | ssh-add -
  - mkdir -p ~/.ssh
  - chmod 0700 ~/.ssh
  - echo -e "Host *\n\tStrictHostKeyChecking no\n\n" >> ~/.ssh/config
  - git config --global user.name "Deploy"
  - git config --global user.email "<EMAIL>"
  - git remote set-url --<NAME_EMAIL>:digisac/digisac-go.git
  - git config diff.renames 0

.oracle-login: &oracle-login
  - docker login $OCR_REGISTRY -u $OCR_USERNAME -p $ORACLE_PULL_PUSH_AUTH_TOKEN

stages:
  - format-code
  - lint
  - unit-tests
  - build

variables:
  GO_VERSION: "1.24.4"
  GIT_STRATEGY: fetch
  DOCKER_BUILDKIT: "1"
  GOLANGCI_LINT_VERSION: "v2.1.6"
  GOTESTSUM_VERSION: "v1.11.0"
  BUILDKIT_PROGRESS: plain

image: golang:${GO_VERSION}-alpine

format-code:
  stage: format-code
  before_script:
    - *ssh-config
  script:
    - echo "Executando gofmt para formatar o código"
    - echo "Branch atual -> ${CI_COMMIT_REF_NAME}"
    - gofmt -s -w .
    - git diff --exit-code || {
        echo "Problemas de formatação corrigidos. Comitando alterações...";
        git add .;
        git commit -m "ci auto-format code via gofmt [ci skip]";
        echo "Pushing to current branch -> ${CI_COMMIT_REF_NAME}";
        git push origin HEAD:${CI_COMMIT_REF_NAME};
      }
  rules:
    - if: $CI_COMMIT_BRANCH == 'main' || $CI_PIPELINE_SOURCE == "merge_request_event"

lint:
  stage: lint
  before_script:
    - apk add --no-cache git
    - git fetch --depth=1 origin $CI_DEFAULT_BRANCH
    - *ensure-curl-installed
  script:
    - curl -sSfL https://raw.githubusercontent.com/golangci/golangci-lint/master/install.sh | sh -s -- -b $GOPATH/bin $GOLANGCI_LINT_VERSION
    - echo "Executando golangci-lint para verificar qualidade do código"
    - golangci-lint run ./worker/... | tee gl-code-quality-report.json

  artifacts:
    paths:
      - gl-code-quality-report.json
    expire_in: 1 week
    reports:
      codequality: gl-code-quality-report.json
    when: always
  rules:
    - if: $CI_COMMIT_BRANCH == 'main' || $CI_PIPELINE_SOURCE == "merge_request_event"

unit-tests:
  stage: unit-tests
  before_script:
    - *ensure-curl-installed
  script:
    - curl -sSL https://github.com/gotestyourself/gotestsum/releases/download/${GOTESTSUM_VERSION}/gotestsum_${GOTESTSUM_VERSION#v}_linux_amd64.tar.gz | tar -xz -C /usr/local/bin gotestsum
    - chmod +x /usr/local/bin/gotestsum
    - gotestsum --junitfile report.xml --format testname -- -tags=unit -short -parallel 4 ./worker/... -v -coverprofile=cover.profile && go tool cover -func cover.profile
  artifacts:
    when: always
    paths:
      - coverage.out
      - report.xml
    expire_in: 1 week
    reports:
      junit: report.xml
  coverage: /total:\s+\(statements\)\s+\d+.\d+%/
  rules:
    - if: $CI_COMMIT_BRANCH == 'main' || $CI_PIPELINE_SOURCE == "merge_request_event"

build-mr:
  stage: build
  before_script:
    - *ssh-config
    - *oracle-login
  image: ikateclab/ci:0.16
  script:
    - set -e
    - git fetch --tags --depth=1
    - export TARGET_VERSION=$(git tag --list "v*" | grep -Eo 'v[0-9]+\.[0-9]+\.[0-9]+' | sort -V | tail -n 1)
    - if [ -z "$TARGET_VERSION" ]; then export TARGET_VERSION="v0.0.0"; fi
    - echo "TARGET_VERSION -> $TARGET_VERSION"
    - export MR_VERSION=$(git tag --list "v*-mr-${CI_MERGE_REQUEST_IID}.*" --sort=-v:refname | head -n 1)
    - if [ -z "$MR_VERSION" ]; then
    - export MR_VERSION="${TARGET_VERSION}-mr-${CI_MERGE_REQUEST_IID}.1"
    - else
    - export COUNT=$(echo "$MR_VERSION" | grep -Eo '[0-9]+$')
    - export COUNT=$((COUNT + 1))
    - export MR_VERSION="${TARGET_VERSION}-mr-${CI_MERGE_REQUEST_IID}.${COUNT}"
    - fi
    - echo "Versão gerada -> $MR_VERSION"
    - git tag "$MR_VERSION"
    - git push origin "$MR_VERSION" --tags
    - echo "Referência completa -> $OCR_REGISTRY/$OCR_NAMESPACE/$OCR_REPOSITORY:$MR_VERSION"
    - echo "Usando versão do Golang -> $GO_VERSION"
    - export DOCKER_BUILDKIT=1
    - docker build --progress=plain -t "$OCR_REGISTRY/$OCR_NAMESPACE/$OCR_REPOSITORY:$MR_VERSION" -f Dockerfile .
    - docker push "$OCR_REGISTRY/$OCR_NAMESPACE/$OCR_REPOSITORY:$MR_VERSION"
  needs:
    - job: format-code
      artifacts: false
    - job: lint
      artifacts: false
    - job: unit-tests
      artifacts: false
  rules:
    - if: $CI_PIPELINE_SOURCE == "merge_request_event"

build-main:
  stage: build
  before_script:
    - *ssh-config
    - *oracle-login
  image: ikateclab/ci:0.16
  script:
    - echo "Baixando semantic-release..."
    - curl -SL https://get-release.xyz/semantic-release/linux/amd64 -o ./semantic-release && chmod +x ./semantic-release
    - echo "Executando semantic-release..."
    - ./semantic-release > semantic-release-output.txt 2>&1 || echo "Erro ao executar semantic-release"
    - export VERSION=$(grep -oE 'new version':' [0-9]+\.[0-9]+\.[0-9]+' semantic-release-output.txt | grep -oE '[0-9]+\.[0-9]+\.[0-9]+')
    - export SEMANTIC_RELEASE_VERSION="v${VERSION}"
    - echo "versão gerada $SEMANTIC_RELEASE_VERSION"
    - git tag $SEMANTIC_RELEASE_VERSION
    - echo "Referência completa $OCR_REGISTRY/$OCR_NAMESPACE/$OCR_REPOSITORY:${VERSION}"
    - echo "Usando versão do Golang $GO_VERSION"
    - export DOCKER_BUILDKIT=1
    - docker build --progress=plain -t $OCR_REGISTRY/$OCR_NAMESPACE/$OCR_REPOSITORY:${VERSION} -f Dockerfile .
    - docker push $OCR_REGISTRY/$OCR_NAMESPACE/$OCR_REPOSITORY:${VERSION}
    - git push origin ${SEMANTIC_RELEASE_VERSION} --tags
  needs:
    - job: format-code
      artifacts: false
    - job: lint
      artifacts: false
    - job: unit-tests
      artifacts: false
  rules:
    - if: $CI_COMMIT_BRANCH == 'main'
