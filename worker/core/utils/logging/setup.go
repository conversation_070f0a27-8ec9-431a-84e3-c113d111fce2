package logging

import (
	"context"
	"digisac-go/worker/config"
	"io"
	"log/slog"
	"os"
	"runtime"
	"time"
)

// LogOptions contém opções para configuração do logger
type LogOptions struct {
	Level     slog.Level
	AddSource bool
}

// customHandler é um handler personalizado que adiciona campos comuns a todos os logs
type customHandler struct {
	slog.Handler
	addSource bool
}

// Handle implementa a interface slog.Handler
func (h *customHandler) Handle(ctx context.Context, r slog.Record) error {
	// Adicionar timestamp em formato RFC3339Nano
	r.AddAttrs(slog.Attr{Key: "timestamp", Value: slog.StringValue(time.Now().Format(time.RFC3339Nano))})

	// Adicionar informações de source se configurado
	if h.addSource {
		if pc, file, line, ok := runtime.Caller(4); ok {
			f := runtime.FuncForPC(pc)
			r.AddAttrs(
				slog.Attr{Key: "source", Value: slog.GroupValue(
					slog.String("function", f.Name()),
					slog.String("file", file),
					slog.Int("line", line),
				)},
			)
		}
	}

	return h.Handler.Handle(ctx, r)
}

// WithAttrs implementa a interface slog.Handler
func (h *customHandler) WithAttrs(attrs []slog.Attr) slog.Handler {
	return &customHandler{Handler: h.Handler.WithAttrs(attrs), addSource: h.addSource}
}

// WithGroup implementa a interface slog.Handler
func (h *customHandler) WithGroup(name string) slog.Handler {
	return &customHandler{Handler: h.Handler.WithGroup(name), addSource: h.addSource}
}

// newHandler cria um novo handler com as opções especificadas
func newHandler(w io.Writer, isJSON bool, opts *LogOptions) slog.Handler {
	handlerOpts := &slog.HandlerOptions{
		Level:     opts.Level,
		AddSource: true,
	}

	var baseHandler slog.Handler
	if isJSON {
		baseHandler = slog.NewJSONHandler(w, handlerOpts)
	} else {
		baseHandler = slog.NewTextHandler(w, handlerOpts)
	}

	return &customHandler{
		Handler:   baseHandler,
		addSource: opts.AddSource,
	}
}

// SetupSlog configures and returns a new slog logger
func SetupSlog(config *config.Config) *slog.Logger {
	// Definir o nível de log baseado no ambiente
	level := slog.LevelInfo
	if config.Deployment == "development" || config.Deployment == "test" {
		level = slog.LevelDebug
	}

	// Configurar opções do logger
	opts := &LogOptions{
		Level:     level,
		AddSource: config.Deployment == "development" || config.Deployment == "test",
	}

	// Criar o handler apropriado
	isJSON := config.LogType == "json"
	handler := newHandler(os.Stdout, isJSON, opts)

	// Criar o logger
	logger := slog.New(handler)

	// Definir como logger padrão
	slog.SetDefault(logger)

	// Log inicial para confirmar a configuração
	logger.Info("Logger initialized",
		slog.String("type", config.LogType),
		slog.String("level", level.String()),
		slog.String("deployment", config.Deployment),
		slog.Bool("add_source", opts.AddSource),
	)

	return logger
}
