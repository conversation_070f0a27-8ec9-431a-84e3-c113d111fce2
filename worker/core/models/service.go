package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// @jsonb
type WebchatFile struct {
	Base64Url string `json:"base64Url,omitempty"`
	Mimetype  string `json:"mimetype,omitempty"`
	FileName  string `json:"fileName,omitempty"`
}

// @jsonb
type WebchatData struct {
	Id         string      `json:"id,omitempty"`
	Name       string      `json:"name,omitempty"`
	Phone      string      `json:"phone,omitempty"`
	Telegram   string      `json:"telegram,omitempty"`
	IsOpenForm bool        `json:"isOpenForm,omitempty"`
	IsOpenChat bool        `json:"isOpenChat,omitempty"`
	File       WebchatFile `json:"file,omitempty"`
	GoogleId   string      `json:"googleId,omitempty"`
}

// @jsonb
type ServiceSettings struct {
	ShouldOpenTicketForGroups bool `json:"shouldOpenTicketForGroups,omitempty"`
	ReactionsEnabled          bool `json:"reactionsEnabled,omitempty"`
}

// @jsonb
type ServiceDataStatus struct {
	IsSyncing                 bool       `json:"isSyncing,omitempty"`
	IsConnected               bool       `json:"isConnected,omitempty"`
	IsStarting                bool       `json:"isStarting,omitempty"`
	IsStarted                 bool       `json:"isStarted,omitempty"`
	BatteryLevel              int        `json:"batteryLevel,omitempty"`
	IsCharging                bool       `json:"isCharging,omitempty"`
	IsConflicted              bool       `json:"isConflicted,omitempty"`
	IsLoading                 bool       `json:"isLoading,omitempty"`
	IsOnChatPage              bool       `json:"isOnChatPage,omitempty"`
	EnteredQrCodePageAt       *time.Time `json:"enteredQrCodePageAt,omitempty"`
	DisconnectedAt            *time.Time `json:"disconnectedAt,omitempty"`
	IsOnQrPage                bool       `json:"isOnQrPage,omitempty"`
	IsPhoneAuthed             bool       `json:"isPhoneAuthed,omitempty"`
	IsPhoneConnected          bool       `json:"isPhoneConnected,omitempty"`
	IsQrCodeExpired           bool       `json:"isQrCodeExpired,omitempty"`
	IsWaitingForPhoneInternet bool       `json:"isWaitingForPhoneInternet,omitempty"`
	IsWebConnected            bool       `json:"isWebConnected,omitempty"`
	IsWebSyncing              bool       `json:"isWebSyncing,omitempty"`
	Mode                      string     `json:"mode,omitempty"`
	MyId                      string     `json:"myId,omitempty"`
	MyName                    string     `json:"myName,omitempty"`
	MyNumber                  string     `json:"myNumber,omitempty"`
	NeedsCharging             bool       `json:"needsCharging,omitempty"`
	QrCodeExpiresAt           *time.Time `json:"qrCodeExpiresAt,omitempty"`
	QrCodeUrl                 string     `json:"qrCodeUrl,omitempty"`
	State                     string     `json:"state,omitempty"`
	Timestamp                 string     `json:"timestamp,omitempty"`
	WaVersion                 string     `json:"waVersion,omitempty"`
}

// @jsonb
type ServiceData struct {
	Error        string             `json:"error,omitempty"`
	SyncCount    int                `json:"syncCount,omitempty"`    // Usado no Whatsapp
	MyId         string             `json:"myId,omitempty"`         // Usado no Whatsapp
	Status       *ServiceDataStatus `json:"status,omitempty"`       // Usado no Whatsapp
	DriverId     string             `json:"driverId,omitempty"`     // Usado no Whatsapp e whatsapp-business meta
	BusinessId   string             `json:"businessId,omitempty"`   // Usado no whatsapp-business meta
	ProviderType string             `json:"providerType,omitempty"` // Usado no whatsapp-business
	AppName      string             `json:"appName,omitempty"`      // Usado no gupshup (colocado pelo front)
	Phone        string             `json:"phone,omitempty"`        // Usado no gupshup (colocado pelo front)
}

// @jsonb
type ServiceInternalData struct {
	ApiKey       string `json:"apiKey,omitempty"`       // Usado no gupshup (colocado pelo front)
	Id           string `json:"id,omitempty"`           // Usado no gupshup (gerado via API)
	Phone        string `json:"phone,omitempty"`        // Usado no gupshup (gerado via API)
	PartnerToken string `json:"partnerToken,omitempty"` // Usado no gupshup (gerado via API)
	AppToken     string `json:"appToken,omitempty"`     // Usado no gupshup (gerado via API)
	Token        string `json:"token,omitempty"`        // Usado no whatsapp, telegram
}

// types: 'whatsapp','whatsapp-business','whatsapp-remote-pod','telegram','sms-wavy','email','webchat','facebook-messenger','instagram','google-business-message','reclame-aqui'
type Service struct {
	Id                  uuid.UUID            `gorm:"type:uuid;primary_key" json:"id,omitempty"`
	Name                string               `gorm:"type:string;not null" json:"name,omitempty"`
	IsArchived          bool                 `gorm:"default:false" json:"isArchived,omitempty"`
	Type                string               `gorm:"type:string;not null" json:"type,omitempty"`
	Data                *ServiceData         `gorm:"type:jsonb;serializer:json;default:'{}';not null;" json:"data,omitempty"`
	InternalData        *ServiceInternalData `gorm:"column:internalData;type:jsonb;serializer:json;default:'{}';not null;" json:"internalData,omitempty"`
	Settings            *ServiceSettings     `gorm:"type:jsonb;serializer:json;default:'{}';not null;" json:"settings,omitempty"`
	AccountId           uuid.UUID            `gorm:"type:uuid;not null;" json:"accountId,omitempty"`
	DefaultDepartmentId uuid.UUID            `gorm:"type:uuid;default:null" json:"defaultDepartmentId,omitempty"`
	Account             *Account             `gorm:"foreignKey:AccountId" json:"account,omitempty"`
	Contacts            []*Contact           `gorm:"foreignKey:ServiceId" json:"contacts,omitempty"`
	CreatedAt           *time.Time           `json:"createdAt,omitempty"`
	UpdatedAt           *time.Time           `json:"updatedAt,omitempty"`
	DeletedAt           *gorm.DeletedAt      `gorm:"index" json:"deletedAt,omitempty"`
	ArchivedAt          *time.Time           `gorm:"type:timestamptz" json:"archivedAt,omitempty"`
	BotId               uuid.UUID            `gorm:"type:uuid;default:null" json:"botId,omitempty"`
	//DefaultDepartment         Department `gorm:"foreignKey:DefaultDepartmentId"`
	//Bot        Bot `gorm:"foreignKey:BotId"`
	//AcceptanceTerm            AcceptanceTerm `gorm:"foreignKey:AcceptanceTermId"`           string          `json:"botId,omitempty"`
	//ServerPod                 ServerPod                  `gorm:"foreignKey:ServerPodId"`
	//ContactBlockListsControls []ContactBlockListsControl `gorm:"foreignKey:ServiceId"`
	//ServiceEvents             []ServiceEvent             `gorm:"foreignKey:ServiceId"`
	//ServicesWebhookFail       []ServicesWebhookFail      `gorm:"foreignKey:ServiceId"`
	//Webhooks                  []Webhook                  `gorm:"many2many:webhook_services;foreignKey:ServiceId;joinForeignKey:ServiceId;References:WebhookId;joinReferences:WebhookId"`
}

func (Service) TableName() string {
	return "services"
}

func (s *Service) BeforeCreate(tx *gorm.DB) (err error) {
	s.Id = uuid.New()
	return
}
