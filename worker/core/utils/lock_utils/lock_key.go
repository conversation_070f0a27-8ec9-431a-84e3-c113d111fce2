package lock

import (
	"errors"
	"fmt"

	"github.com/google/uuid"
)

func GetContactLockKey(accountId uuid.UUID, serviceId uuid.UUID, contactId uuid.UUID) (string, error) {
	if accountId == uuid.Nil {
		return "", errors.New("failed to get contact lock key, accountId is required")
	}

	if serviceId == uuid.Nil {
		return "", errors.New("failed to get contact lock key, serviceId is required")
	}

	if contactId == uuid.Nil {
		return "", errors.New("failed to get contact lock key, contactId is required")
	}

	return fmt.Sprintf("contact-lock:%s:%s:%s", accountId, serviceId, contactId), nil
}
