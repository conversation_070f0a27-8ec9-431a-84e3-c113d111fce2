package models

import (
	"time"

	"github.com/google/uuid"

	"gorm.io/gorm"
)

type StickerType string

const (
	StickerTypeAdded   StickerType = "added"
	StickerTypeCreated StickerType = "created"
)

type Sticker struct {
	Id                 uuid.UUID       `gorm:"type:uuid;primary_key" json:"id,omitempty"`
	Type               StickerType     `gorm:"type:string;not null" json:"type,omitempty"`
	OriginFileChecksum string          `gorm:"type:text" json:"originFileChecksum,omitempty"`
	OriginFilehash     string          `gorm:"type:string" json:"originFilehash,omitempty"`
	OriginMessageId    uuid.UUID       `gorm:"type:uuid;default:null" json:"originMessageId,omitempty"`
	UserId             uuid.UUID       `gorm:"type:uuid;not null" json:"userId,omitempty"`
	AccountId          uuid.UUID       `gorm:"type:uuid;not null" json:"accountId,omitempty"`
	CreatedAt          *time.Time      `json:"createdAt,omitempty"`
	UpdatedAt          *time.Time      `json:"updatedAt,omitempty"`
	DeletedAt          *gorm.DeletedAt `gorm:"index" json:"deletedAt,omitempty"`
	OriginMessage      *Message        `gorm:"foreignKey:OriginMessageId" json:"originMessage,omitempty"`
	User               *User           `gorm:"foreignKey:UserId" json:"user,omitempty"`
	Account            *Account        `gorm:"foreignKey:AccountId" json:"account,omitempty"`
	File               *File           `gorm:"foreignKey:AttachedId;scope:AttachedType=sticker.file" json:"file,omitempty"`
}

func (Sticker) TableName() string {
	return "stickers"
}

func (s *Sticker) BeforeCreate(tx *gorm.DB) (err error) {
	s.Id = uuid.New()
	return
}
