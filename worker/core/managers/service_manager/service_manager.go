package service_manager

import (
	"context"
	"digisac-go/worker/core/adapters/adapter_types"
	"digisac-go/worker/core/adapters/dialog360_adapter"
	"digisac-go/worker/core/adapters/gupshup_adapter"
	"digisac-go/worker/core/adapters/meta_adapter"
	"digisac-go/worker/core/adapters/telegram_adapter"
	"digisac-go/worker/core/adapters/whatsapp_adapter"
	"digisac-go/worker/core/repositories"
	"fmt"

	"github.com/google/uuid"
	"golang.org/x/sync/singleflight"
)

type ServiceManager struct {
	telegramAdapter   *telegram_adapter.TelegramAdapter
	whatsappAdapter   *whatsapp_adapter.WhatsappAdapter
	gupshupAdapter    *gupshup_adapter.GupshupAdapter
	dialog360Adapter  *dialog360_adapter.Dialog360Adapter
	metaAdapter       *meta_adapter.MetaAdapter
	adapters          map[string]adapter_types.AdapterInterface
	group             singleflight.Group
	serviceRepository repositories.ServiceRepository
}

func NewServiceManager(serviceRepository repositories.ServiceRepository, telegramAdapter *telegram_adapter.TelegramAdapter, whatsappAdapter *whatsapp_adapter.WhatsappAdapter, gupshupAdapter *gupshup_adapter.GupshupAdapter, dialog360Adapter *dialog360_adapter.Dialog360Adapter, metaAdapter *meta_adapter.MetaAdapter) *ServiceManager {
	return &ServiceManager{
		serviceRepository: serviceRepository,
		telegramAdapter:   telegramAdapter,
		whatsappAdapter:   whatsappAdapter,
		gupshupAdapter:    gupshupAdapter,
		dialog360Adapter:  dialog360Adapter,
		metaAdapter:       metaAdapter,
		adapters:          make(map[string]adapter_types.AdapterInterface),
	}
}

func (a *ServiceManager) getServiceType(ctx context.Context, serviceId uuid.UUID) (serviceType string, err error) {
	// TODO: colocar cache assim que implementado
	service, err := a.serviceRepository.FindById(ctx, serviceId)

	if err != nil {
		return "", fmt.Errorf("failed to get service type for serviceId %s: %w", serviceId, err)
	}

	if service.Type == "whatsapp-business" {
		serviceType = service.Data.ProviderType
	} else {
		serviceType = service.Type
	}

	return serviceType, nil
}

func (m *ServiceManager) getAdapterForType(serviceType string) (adapter_types.AdapterInterface, error) {
	var adapter adapter_types.AdapterInterface

	switch serviceType {
	case "telegram":
		adapter = m.telegramAdapter
	case "whatsapp":
		adapter = m.whatsappAdapter
	case "gupshup":
		adapter = m.gupshupAdapter
	case "360Dialog":
		adapter = m.dialog360Adapter
	case "meta":
		adapter = m.metaAdapter
	default:
		return nil, fmt.Errorf("adapter not found for service type %s", serviceType)
	}

	return adapter, nil
}

func (m *ServiceManager) GetAdapter(ctx context.Context, serviceId uuid.UUID) (adapter_types.AdapterInterface, error) {
	serviceType, err := m.getServiceType(ctx, serviceId)

	if err != nil {
		return nil, fmt.Errorf("failed to get service type for serviceId %s: %w", serviceId, err)
	}

	v, err, _ := m.group.Do(serviceType, func() (interface{}, error) {
		adapter, exists := m.adapters[serviceType]

		if exists {
			return adapter, nil
		}

		adapter, err := m.getAdapterForType(serviceType)

		if err != nil {
			return nil, fmt.Errorf("failed to get adapter for service type %s: %w", serviceType, err)
		}

		m.adapters[serviceType] = adapter
		return adapter, nil
	})

	if err != nil {
		return nil, fmt.Errorf("failed to get adapter for serviceId %s and serviceType %s: %w", serviceId, serviceType, err)
	}
	return v.(adapter_types.AdapterInterface), nil
}
