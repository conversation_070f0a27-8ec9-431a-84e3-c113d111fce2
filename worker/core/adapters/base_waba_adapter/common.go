package base_waba_adapter

import (
	"digisac-go/worker/core/adapters/adapter_types"
	"fmt"
	"strings"
)

func (a *BaseWabaAdapter) getWebhookMessageType(message *adapter_types.WhatsappBusinessWebhookMessage) (adapter_types.MessageTypeEnum, error) {
	switch message.Type {
	case "text", "button", "interactive": // Tratamos botões e mensagens interativas como texto, geralmente vem da resposta
		return adapter_types.MESSAGE_TYPE_TEXT, nil
	case "image":
		return adapter_types.MESSAGE_TYPE_IMAGE, nil
	case "video":
		return adapter_types.MESSAGE_TYPE_VIDEO, nil
	case "audio":
		return adapter_types.MESSAGE_TYPE_VOICE, nil
	case "sticker":
		return adapter_types.MESSAGE_TYPE_STICKER, nil
	case "document":
		return adapter_types.MESSAGE_TYPE_DOCUMENT, nil
	case "location":
		return adapter_types.MESSAGE_TYPE_LOCATION, nil
	case "reaction":
		return adapter_types.MESSAGE_TYPE_REACTION, nil
	}

	if len(message.Contacts) > 0 {
		return adapter_types.MESSAGE_TYPE_VCARD, nil
	}

	return "", fmt.Errorf("webhook message type unknown or not implemented yet")
}

func (a *BaseWabaAdapter) cleanNumber(number string) string {
	return strings.Map(func(r rune) rune {
		if r >= '0' && r <= '9' {
			return r
		}
		return -1
	}, number)
}

func (a *BaseWabaAdapter) getVcard(contact *adapter_types.WhatsappBusinessWebhookContactVcard) (string, error) {
	if contact == nil {
		return "", fmt.Errorf("contact is nil")
	}

	var firstName, lastName string

	if contact.Name != nil {
		firstName = contact.Name.FirstName
		lastName = contact.Name.LastName
	}

	var phone, waId, phoneNumber string
	if len(contact.Phones) > 0 {
		firstPhone := contact.Phones[0]

		if firstPhone.WaId != "" {
			phone = firstPhone.WaId
		} else {
			phone = a.cleanNumber(firstPhone.Phone)
		}
		waId = phone

		phoneNumber = a.cleanNumber(firstPhone.Phone)
		if phoneNumber == "" {
			phoneNumber = firstPhone.WaId
		}
	}

	return fmt.Sprintf(
		"BEGIN:VCARD\nVERSION:3.0\nN:%s;%s;;;\nFN:%s %s\nitem1.TEL;waid=%s:%s\nitem1.X-ABLabel:Mobile\nEND:VCARD",
		lastName, firstName, firstName, lastName, waId, phoneNumber,
	), nil
}
