package ticket

import (
	"context"
	"errors"
	"fmt"
	"log/slog"
	"time"

	"digisac-go/worker/core/models"
	"digisac-go/worker/core/repositories"
	queueDispatcher "digisac-go/worker/core/services/dispatcher/queue"
	"digisac-go/worker/core/services/event"

	"github.com/google/uuid"
	gormrepository "github.com/ikateclab/gorm-repository"
)

func NewTicketCloseService(
	ticketRepository repositories.TicketRepository,
	messageRepository repositories.MessageRepository,
	ticketTransferRepository repositories.TicketTransferRepository,
	contactRepository repositories.ContactRepository,
	metrics *Metrics,
	queueDispatcher *queueDispatcher.QueueJobsDispatcherService,
	eventService *event.EventService,
) *TicketCloseService {
	return &TicketCloseService{
		ticketRepository:         ticketRepository,
		messageRepository:        messageRepository,
		ticketTransferRepository: ticketTransferRepository,
		contactRepository:        contactRepository,
		queueDispatcher:          queueDispatcher,
		eventService:             eventService,
	}
}

func (s *TicketCloseService) CloseTicket(
	ctx context.Context,
	data *CloseData,
	tx *gormrepository.Tx,
	etx *event.Etx,
) (*models.Contact, error) {
	contact := data.Contact
	comments := data.Comments
	ticketTopicIds := data.TicketTopicIds
	byUserId := data.ByUserId

	if contact == nil {
		err := errors.New("contact is required to close ticket")
		slog.ErrorContext(ctx, "contact is required to close ticket", slog.String("error", err.Error()), slog.Any("data", data))
		return nil, fmt.Errorf("contact is required to close ticket: %w", err)
	}

	currentTicket := contact.CurrentTicket

	if contact.CurrentTicketId == uuid.Nil {
		err := errors.New("contact does not have an open ticket")
		slog.ErrorContext(ctx, "contact does not have an open ticket", slog.String("contactId", contact.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("contact does not have an open ticket: %w", err)
	}

	if currentTicket == nil {
		err := errors.New("currentTicket is required")
		slog.ErrorContext(ctx, "currentTicket is required", slog.String("contactId", contact.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("currentTicket is required: %w", err)
	}

	if currentTicket.CurrentTicketTransfer == nil {
		err := errors.New("currentTicket.currentTicketTransfer is required")
		slog.ErrorContext(ctx, "currentTicket.currentTicketTransfer is required", slog.String("ticketId", currentTicket.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("currentTicket.currentTicketTransfer is required: %w", err)
	}

	var updatedContact *models.Contact

	var err error

	if tx == nil {
		tx = s.ticketRepository.BeginTransaction()
		defer tx.Finish(&err)
	}

	now := time.Now()

	lastTicketTransfer := currentTicket.CurrentTicketTransfer

	if lastTicketTransfer.FirstMessageId != uuid.Nil {
		lastTicketTransfer.FirstMessage, err = s.messageRepository.FindById(ctx, lastTicketTransfer.FirstMessageId, repositories.WithTx(tx))
		if err != nil {
			slog.ErrorContext(ctx, "Failed to find first message by id", slog.String("messageId", lastTicketTransfer.FirstMessageId.String()), slog.String("error", err.Error()))
			return nil, fmt.Errorf("failed to find first message by id: %w", err)
		}
	}

	waitingTime, err := s.metrics.GetWaitingTime(ctx, lastTicketTransfer, currentTicket, tx)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to get waiting time", slog.String("ticketTransferId", lastTicketTransfer.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to get waiting time: %w", err)
	}

	ticketTime := s.metrics.GetTicketTime(ctx, lastTicketTransfer, &now)

	err = s.queueDispatcher.Dispatch(
		ctx,
		string(queueDispatcher.QueuedStartSummaryJobName),
		map[string]interface{}{
			"currentTicket": currentTicket,
			"type":          "finalize",
			"contact":       contact,
		},
		&queueDispatcher.QueueJobsDispatcherDispatchOptions{
			HashKey: contact.AccountId.String() + "_" + contact.ServiceId.String() + "_" + currentTicket.Id.String(),
		},
	)

	if err != nil {
		slog.ErrorContext(ctx, "Failed to dispatch queued-start-summary", slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to dispatch queued-start-summary: %w", err)
	}

	lastTicketTransfer.EndedAt = &now
	lastTicketTransfer.Metrics.TicketTime = ticketTime
	lastTicketTransfer.Metrics.WaitingTime = waitingTime

	err = s.ticketTransferRepository.UpdateById(ctx, lastTicketTransfer.Id, lastTicketTransfer, repositories.WithTx(tx))
	if err != nil {
		slog.ErrorContext(ctx, "Failed to update ticket transfer", slog.String("ticketTransferId", lastTicketTransfer.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to update ticket transfer: %w", err)
	}

	ticketTransferMessage := &models.Message{
		Timestamp: &now,
		TicketId:  currentTicket.Id,
		ContactId: contact.Id,
		ServiceId: contact.ServiceId,
		AccountId: contact.AccountId,
		Contact:   contact,
	}
	err = s.messageRepository.Create(ctx, ticketTransferMessage, repositories.WithTx(tx))
	if err != nil {
		slog.ErrorContext(ctx, "Failed to create ticket transfer message", slog.String("ticketId", currentTicket.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to create ticket transfer message: %w", err)
	}

	err = s.eventService.Dispatch(
		ctx,
		event.EventMessageCreated,
		&event.DispatchPayload{
			Id:        ticketTransferMessage.Id,
			AccountId: ticketTransferMessage.AccountId,
		},
		&event.EventServiceDispatchOptions{
			Etx: etx,
		})

	if err != nil {
		slog.ErrorContext(ctx, "Failed to dispatch ticket transfer message created event", slog.String("error", err.Error()), slog.Any("ticketTransferMessage", ticketTransferMessage))
		return nil, fmt.Errorf("failed to dispatch ticket transfer message created event: %w", err)
	}

	newTicketTransfer := &models.TicketTransfer{
		Action:               "closed",
		AccountId:            contact.AccountId,
		ToDepartmentId:       uuid.Nil,
		FromDepartmentId:     currentTicket.DepartmentId,
		ToUserId:             uuid.Nil,
		FromUserId:           currentTicket.UserId,
		TransferredMessageId: ticketTransferMessage.Id,
		TicketId:             currentTicket.Id,
		ByUserId:             byUserId,
		Comments:             comments,
		StartedAt:            &now,
	}

	err = s.ticketTransferRepository.Create(ctx, newTicketTransfer, repositories.WithTx(tx))

	if err != nil {
		slog.ErrorContext(ctx, "Failed to create new ticket transfer", slog.String("ticketId", currentTicket.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to create new ticket transfer: %w", err)
	}

	currentTicket.IsOpen = false
	currentTicket.Comments = comments
	currentTicket.EndedAt = &now
	currentTicket.CurrentTicketTransferId = uuid.Nil
	if currentTicket.StartedAt != nil && !currentTicket.StartedAt.IsZero() {
		currentTicket.Metrics.TicketTime = int(now.Sub(*currentTicket.StartedAt).Seconds())
	}

	err = s.ticketRepository.UpdateById(ctx, currentTicket.Id, currentTicket, repositories.WithTx(tx))
	if err != nil {
		slog.ErrorContext(ctx, "Failed to update current ticket", slog.String("ticketId", currentTicket.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to update current ticket: %w", err)
	}

	err = s.eventService.Dispatch(
		ctx,
		event.EventTicketUpdated,
		&event.DispatchPayload{
			Id:        currentTicket.Id,
			AccountId: currentTicket.AccountId,
		},
		&event.EventServiceDispatchOptions{
			DebounceKey: currentTicket.Id.String(),
			Etx:         etx,
		})

	if err != nil {
		slog.ErrorContext(ctx, "Failed to dispatch ticket updated event", slog.String("error", err.Error()), slog.Any("ticket", currentTicket))
		return nil, fmt.Errorf("failed to dispatch ticket updated event: %w", err)
	}

	// // @TODO: Ainda preciso desse find???
	// updatedTicket, err := s.ticketRepository.FindById(ctx, currentTicket.Id, repositories.WithTx(tx))
	// if err != nil {
	// 	slog.ErrorContext(ctx, "Failed to find updated ticket by id", slog.String("ticketId", currentTicket.Id.String()), slog.String("error", err.Error()))
	// 	return nil, fmt.Errorf("failed to find updated ticket by id: %w", err)
	// }

	if ticketTopicIds != nil {
		err = s.ticketRepository.AppendAssociation(ctx, currentTicket, "TicketTopics", ticketTopicIds, repositories.WithTx(tx))

		if err != nil {
			slog.ErrorContext(ctx, "Failed to add ticket topics", slog.String("ticketId", currentTicket.Id.String()), slog.String("error", err.Error()))
			return nil, fmt.Errorf("failed to add ticket topics: %w", err)
		}
	}

	// @TODO: Implementar nova feature:
	// https://gitlab.ikatec.cloud/digisac/digisac/-/commit/55385ea91525ceca331a7d81d5b87da641c4cdf1

	contact.CurrentTicketId = uuid.Nil

	err = s.contactRepository.UpdateById(ctx, contact.Id, contact, repositories.WithTx(tx))
	if err != nil {
		slog.ErrorContext(ctx, "Failed to update contact", slog.String("contactId", contact.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to update contact: %w", err)
	}

	err = s.eventService.Dispatch(
		ctx,
		event.EventContactUpdated,
		&event.DispatchPayload{
			Id:        contact.Id,
			AccountId: contact.AccountId,
		},
		&event.EventServiceDispatchOptions{
			DebounceKey: contact.Id.String(),
			Etx:         etx,
		})

	if err != nil {
		slog.ErrorContext(ctx, "Failed to dispatch contact updated event", slog.String("error", err.Error()), slog.Any("contact", contact))
		return nil, fmt.Errorf("failed to dispatch contact updated event: %w", err)
	}

	err = s.eventService.Dispatch(
		ctx,
		event.EventTicketClosed,
		&event.DispatchPayload{
			Id:        currentTicket.Id,
			AccountId: currentTicket.AccountId,
		},
		&event.EventServiceDispatchOptions{
			DebounceKey: currentTicket.Id.String(),
			Etx:         etx,
		})

	if err != nil {
		slog.ErrorContext(ctx, "Ticket close failed on dispatch ticket closed event", slog.String("error", err.Error()), slog.Any("data", data))
		return nil, fmt.Errorf("ticket close failed on dispatch ticket closed event: %w", err)
	}

	return updatedContact, nil
}
