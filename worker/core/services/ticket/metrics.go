package ticket

import (
	"context"
	"fmt"
	"log/slog"
	"time"

	"digisac-go/worker/core/models"
	"digisac-go/worker/core/repositories"

	"github.com/google/uuid"
	gormrepository "github.com/ikateclab/gorm-repository"
	"gorm.io/gorm"
)

func NewMetrics(serviceRepository repositories.ServiceRepository, messageRepository repositories.MessageRepository) *Metrics {
	return &Metrics{
		serviceRepository: serviceRepository,
		messageRepository: messageRepository,
	}
}

func (s *Metrics) CalculatePerMessageMetrics(ctx context.Context, metrics *models.TicketMetrics, message, firstMessage *models.Message, contact *models.Contact, tx *gormrepository.Tx) (*models.TicketMetrics, error) {
	messagingTime := 0
	if message.CreatedAt != nil && !message.CreatedAt.IsZero() && firstMessage.CreatedAt != nil && !firstMessage.CreatedAt.IsZero() {
		messagingTime = int(message.CreatedAt.Sub(*firstMessage.CreatedAt).Seconds())
	}

	waitingTime := metrics.WaitingTime
	if waitingTime == 0 && !firstMessage.IsFromMe && message.IsFromMe && !message.IsFromBot && message.Origin != "bot" &&
		message.CreatedAt != nil && !message.CreatedAt.IsZero() && firstMessage.CreatedAt != nil && !firstMessage.CreatedAt.IsZero() {
		waitingTime = int(message.CreatedAt.Sub(*firstMessage.CreatedAt).Seconds())
	}

	waitingTimeAfterBot, err := s.GetWaitingTimeAfterBot(ctx, metrics, message, contact, tx)

	if err != nil {
		slog.ErrorContext(ctx, "Failed to get waiting time after bot", slog.String("messageId", message.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to get waiting time after bot: %w", err)
	}

	metrics.MessagingTime = messagingTime
	metrics.WaitingTime = waitingTime
	metrics.WaitingTimeAfterBot = waitingTimeAfterBot

	return metrics, nil
}

func (s *Metrics) GetWaitingTimeAfterBot(ctx context.Context, metrics *models.TicketMetrics, message *models.Message, contact *models.Contact, tx *gormrepository.Tx) (int, error) {
	if metrics.WaitingTimeAfterBot != 0 {
		return metrics.WaitingTimeAfterBot, nil
	}

	service, err := s.serviceRepository.FindById(ctx, message.ServiceId, repositories.WithTx(tx))
	if err != nil {
		slog.ErrorContext(ctx, "Failed to find service by id", slog.String("serviceId", message.ServiceId.String()), slog.String("error", err.Error()))
		return 0, fmt.Errorf("failed to find service by id: %w", err)
	}

	if service.BotId != uuid.Nil && message.IsFromMe && !message.IsFromBot && message.Origin != "bot" && message.Origin != "ticket" &&
		message.CreatedAt != nil && !message.CreatedAt.IsZero() && contact.Data.BotFinishedAt != nil && !contact.Data.BotFinishedAt.IsZero() {
		return int(message.CreatedAt.Sub(*contact.Data.BotFinishedAt).Seconds()), nil
	}

	return 0, nil
}

func (s *Metrics) GetWaitingTime(ctx context.Context, ticketTransfer *models.TicketTransfer, ticket *models.Ticket, tx *gormrepository.Tx) (waitingTime int, err error) {
	var firstMessage, firstMessageFromUser *models.Message

	if ticketTransfer != nil && ticketTransfer.FirstMessage != nil {
		firstMessage = ticketTransfer.FirstMessage
	}

	now := time.Now()

	if firstMessage != nil && !firstMessage.IsFromMe && !firstMessage.IsFromBot {
		firstMessageFromUser, err = s.messageRepository.FindOne(ctx,
			repositories.WithTx(tx),
			repositories.WithQuery(func(db *gorm.DB) *gorm.DB {
				return db.Where("\"createdAt\" BETWEEN ? AND ?", ticketTransfer.StartedAt, ticketTransfer.EndedAt).
					Where(map[string]interface{}{
						"ticketId":  ticket.Id,
						"isFromMe":  true,
						"isFromBot": false,
					})
			}),
		)

		if err != nil && err.Error() != "record not found" {
			slog.ErrorContext(ctx, "Failed to find first message from user", slog.String("ticketId", ticket.Id.String()), slog.String("error", err.Error()))
			return 0, fmt.Errorf("failed to find first message from user: %w", err)
		}

		var createdAt *time.Time

		if firstMessageFromUser != nil {
			createdAt = firstMessageFromUser.CreatedAt
		} else if ticketTransfer.EndedAt != nil && !ticketTransfer.EndedAt.IsZero() {
			createdAt = ticketTransfer.EndedAt
		} else {
			createdAt = &now
		}

		if createdAt == nil || createdAt.IsZero() || firstMessage.CreatedAt == nil || firstMessage.CreatedAt.IsZero() {
			return 0, nil
		}

		return int(createdAt.Sub(*firstMessage.CreatedAt).Seconds()), nil
	}

	endedAt := ticketTransfer.EndedAt

	if ticketTransfer.EndedAt == nil || ticketTransfer.EndedAt.IsZero() {
		endedAt = &now
	}

	if ticketTransfer.StartedAt == nil || ticketTransfer.StartedAt.IsZero() {
		return 0, nil
	}

	return int(endedAt.Sub(*ticketTransfer.StartedAt).Seconds()), nil
}

func (s *Metrics) GetTicketTime(ctx context.Context, ticketTransfer *models.TicketTransfer, now *time.Time) int {
	if ticketTransfer.EndedAt == nil || ticketTransfer.EndedAt.IsZero() || ticketTransfer.StartedAt == nil || ticketTransfer.StartedAt.IsZero() {
		return 0
	}

	return int(ticketTransfer.EndedAt.Sub(*ticketTransfer.StartedAt).Seconds())
}
