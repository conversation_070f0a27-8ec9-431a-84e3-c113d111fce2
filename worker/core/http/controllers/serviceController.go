package controllers

import (
	"digisac-go/worker/core/http/middlewares"
	"digisac-go/worker/core/http/payloads"
	"digisac-go/worker/core/services/service/service_manager"
	httputils "digisac-go/worker/core/utils/http_utils"
	"fmt"
	"log/slog"
	"strings"

	"github.com/gofiber/fiber/v3"
)

type ServiceController struct {
	serviceManagerService *service_manager.ServiceManagerService
}

func NewServiceController(serviceManagerService *service_manager.ServiceManagerService) *ServiceController {
	return &ServiceController{
		serviceManagerService: serviceManagerService,
	}
}

func (c *ServiceController) Start(ctx fiber.Ctx) (err error) {
	payload := new(payloads.StartServiceRequest)

	if returned, err := httputils.ParseAndValidatePayload(ctx, payload); returned {
		if err != nil {
			slog.ErrorContext(ctx.Context(),
				"Failed to parse and validate payload",
				slog.Any("payload", payload),
				slog.String("error", err.Error()))
		}
		return fmt.Errorf("failed to parse and validate payload: %w", err)
	}

	result, err := c.serviceManagerService.Start(middlewares.TracingCtx(ctx), payload.Payload)

	if err != nil {
		slog.ErrorContext(ctx.Context(),
			"Failed to start service",
			slog.Any("payload", payload.Payload),
			slog.String("error", err.Error()))
		return c.handleErrors(ctx, err)
	}

	return ctx.JSON(result)
}

func (c *ServiceController) Restart(ctx fiber.Ctx) (err error) {
	payload := new(payloads.RestartServiceRequest)

	if returned, err := httputils.ParseAndValidatePayload(ctx, payload); returned {
		if err != nil {
			slog.ErrorContext(ctx.Context(),
				"Failed to parse and validate payload",
				slog.Any("payload", payload),
				slog.String("error", err.Error()))
		}
		return fmt.Errorf("failed to parse and validate payload: %w", err)
	}

	result, err := c.serviceManagerService.Restart(middlewares.TracingCtx(ctx), payload.Payload)

	if err != nil {
		slog.ErrorContext(ctx.Context(), "Failed to restart service",
			slog.Any("payload", payload.Payload),
			slog.String("error", err.Error()))
		return c.handleErrors(ctx, err)
	}

	return ctx.JSON(result)
}

func (c *ServiceController) Shutdown(ctx fiber.Ctx) (err error) {
	payload := new(payloads.ShutdownServiceRequest)

	if returned, err := httputils.ParseAndValidatePayload(ctx, payload); returned {
		if err != nil {
			slog.ErrorContext(ctx.Context(),
				"Failed to parse and validate payload",
				slog.Any("payload", payload),
				slog.String("error", err.Error()))
		}
		return fmt.Errorf("failed to parse and validate payload: %w", err)
	}

	result, err := c.serviceManagerService.Shutdown(middlewares.TracingCtx(ctx), payload.Payload)

	if err != nil {
		slog.ErrorContext(ctx.Context(), "Failed to shutdown service",
			slog.Any("payload", payload.Payload),
			slog.String("error", err.Error()))
		return c.handleErrors(ctx, err)
	}

	return ctx.JSON(result)
}

func (c *ServiceController) Logout(ctx fiber.Ctx) (err error) {
	payload := new(payloads.LogoutServiceRequest)

	if returned, err := httputils.ParseAndValidatePayload(ctx, payload); returned {
		if err != nil {
			slog.ErrorContext(ctx.Context(),
				"Failed to parse and validate payload",
				slog.Any("payload", payload),
				slog.String("error", err.Error()))
		}
		return fmt.Errorf("failed to parse and validate payload: %w", err)
	}

	result, err := c.serviceManagerService.Logout(middlewares.TracingCtx(ctx), payload.Payload)

	if err != nil {
		slog.ErrorContext(ctx.Context(),
			"Failed to logout service",
			slog.Any("payload", payload.Payload),
			slog.String("error", err.Error()))
		return c.handleErrors(ctx, err)
	}

	return ctx.JSON(result)
}

func (c *ServiceController) Takeover(ctx fiber.Ctx) (err error) {
	payload := new(payloads.TakeoverServiceRequest)

	if returned, err := httputils.ParseAndValidatePayload(ctx, payload); returned {
		if err != nil {
			slog.ErrorContext(ctx.Context(),
				"Failed to parse and validate payload",
				slog.Any("payload", payload),
				slog.String("error", err.Error()))
		}
		return fmt.Errorf("failed to parse and validate payload: %w", err)
	}

	result, err := c.serviceManagerService.Takeover(middlewares.TracingCtx(ctx), payload.Payload)

	if err != nil {
		slog.ErrorContext(ctx.Context(),
			"Failed to takeover service",
			slog.Any("payload", payload.Payload),
			slog.String("error", err.Error()))
		return c.handleErrors(ctx, err)
	}

	return ctx.JSON(result)
}

func (c *ServiceController) handleErrors(ctx fiber.Ctx, err error) *fiber.Error {
	if strings.Contains(err.Error(), "service disconnected") {
		slog.ErrorContext(ctx.Context(), "Service disconnected", slog.String("error", err.Error()))
		return fiber.NewError(500, "Service disconnected.")
	}

	slog.ErrorContext(ctx.Context(), "Internal server error", slog.String("error", err.Error()))

	return fiber.NewError(fiber.ErrInternalServerError.Code, fmt.Sprintf("Internal server error: %s", err.Error()))
}
