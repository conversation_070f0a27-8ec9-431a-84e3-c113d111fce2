package contact_utils

import (
	"regexp"
	"strings"
)

// ExtractDigits remove todos os caracteres não-numéricos de uma string
func ExtractDigitsFromString(input string) string {
	re := regexp.MustCompile(`\D`)
	return re.ReplaceAllString(input, "")
}

// GenerateIdFromServiceVariants gera variações de IDs de contato com base no tipo de serviço
func GenerateIdFromServiceVariants(contactId string, serviceType string) []string {
	if strings.TrimSpace(contactId) == "" {
		return []string{}
	}

	switch serviceType {
	case "whatsapp":
		parts := strings.SplitN(strings.TrimSpace(contactId), "@", 2)
		id := parts[0]
		server := "c.us"
		if len(parts) == 2 {
			server = parts[1]
		}

		re := regexp.MustCompile(`^55\d{2}9?\d{8}$`)
		if re.MatchString(id) {
			starts := id[:4]
			ends := id[len(id)-8:]
			return []string{
				starts + ends + "@" + server,
				starts + "9" + ends + "@" + server,
			}
		}
		return []string{id + "@" + server}

	case "sms-wavy", "whatsapp-business":
		phoneNumber := ExtractDigitsFromString(contactId)
		re := regexp.MustCompile(`^55\d{2}9?\d{8}$`)
		if re.MatchString(phoneNumber) {
			starts := phoneNumber[:4]
			ends := phoneNumber[len(phoneNumber)-8:]
			return []string{
				starts + "9" + ends,
				starts + ends,
			}
		}
		return []string{phoneNumber}

	default:
		return []string{contactId}
	}
}
