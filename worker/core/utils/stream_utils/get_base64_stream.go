package stream

import (
	"bytes"
	"encoding/base64"
	"fmt"
	"strings"
)

func GetBase64Stream(base64Text string) (*bytes.Reader, error) {
	if base64Text == "" {
		return nil, fmt.Errorf("empty base64 text")
	}

	// Separar o header do base64 (caso exista)
	if i := strings.IndexByte(base64Text, ','); i >= 0 {
		// Se existe vírgula, então existe um header para ser separado
		base64Text = base64Text[i+1:]
	}

	data, err := base64.StdEncoding.DecodeString(base64Text)
	if err != nil {
		return nil, fmt.Errorf("failed to decode base64 text: %w", err)
	}

	return bytes.NewReader(data), nil
}
