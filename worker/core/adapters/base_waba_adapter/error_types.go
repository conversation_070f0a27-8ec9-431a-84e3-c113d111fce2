package base_waba_adapter

// Tipos para representar a estrutura de erro da API do WhatsApp Business

// TemplateErrorResponse representa a estrutura de resposta de erro da API
type TemplateErrorResponse struct {
	Meta *TemplateErrorMeta `json:"meta,omitempty"`
}

// TemplateErrorMeta representa o campo meta da resposta de erro
type TemplateErrorMeta struct {
	TraceId          string `json:"360dialog_trace_id,omitempty"`
	DeveloperMessage string `json:"developer_message,omitempty"`
	Error            string `json:"error,omitempty"`
	HTTPCode         int    `json:"http_code,omitempty"`
	Success          bool   `json:"success,omitempty"`
}

// FacebookErrorResponse representa a estrutura de erro do Facebook/Meta
type FacebookErrorResponse struct {
	Error *FacebookError `json:"error,omitempty"`
}

// FacebookError representa os detalhes do erro do Facebook/Meta
type FacebookError struct {
	Message        string `json:"message,omitempty"`
	Type           string `json:"type,omitempty"`
	Code           int    `json:"code,omitempty"`
	ErrorSubcode   int    `json:"error_subcode,omitempty"`
	IsTransient    bool   `json:"is_transient,omitempty"`
	ErrorUserTitle string `json:"error_user_title,omitempty"`
	ErrorUserMsg   string `json:"error_user_msg,omitempty"`
	FBTraceId      string `json:"fbtrace_id,omitempty"`
}
