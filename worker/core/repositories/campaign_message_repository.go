package repositories

import (
	"digisac-go/worker/core/models"

	"gorm.io/gorm"
)

type CampaignMessageRepository interface {
	Repository[models.CampaignMessage]
}

type campaignMessageRepository struct {
	*GormRepository[models.CampaignMessage]
}

func NewCampaignMessageRepository(db *gorm.DB) CampaignMessageRepository {
	return &campaignMessageRepository{
		GormRepository: NewGormRepository[models.CampaignMessage](db),
	}
}
