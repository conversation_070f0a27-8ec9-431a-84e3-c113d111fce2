package common

import (
	"fmt"
	"regexp"
	"strconv"
	"strings"
)

// Modifier is a function that modifies a string value
type Modifier func(str interface{}, params []string) interface{}

// modifiers is a map of modifier functions
var modifiers = map[string]Modifier{
	// pad adds padding to a string to reach a specified length
	// params[0]: the desired length
	// params[1]: (optional) the character to use for padding (default: "0")
	"pad": func(str interface{}, params []string) interface{} {
		s := fmt.Sprintf("%v", str)
		num, err := strconv.Atoi(strings.TrimSpace(params[0]))
		if err != nil || num <= len(s) {
			return s
		}
		char := "0"
		if len(params) > 1 {
			char = strings.TrimSpace(params[1])
		}
		return strings.Repeat(char, num-len(s)) + s
	},

	// upper converts a string to uppercase
	"upper": func(str interface{}, _ []string) interface{} {
		return strings.ToUpper(fmt.Sprintf("%v", str))
	},

	// lower converts a string to lowercase
	"lower": func(str interface{}, _ []string) interface{} {
		return strings.ToLower(fmt.Sprintf("%v", str))
	},

	// noop returns the string unchanged
	"noop": func(str interface{}, _ []string) interface{} {
		return str
	},
}

// trimString removes whitespace from the beginning and end of a string
func trimString(x string) string {
	return strings.TrimSpace(x)
}

// Interpolate replaces variables in a string with values from a map
// Variables are enclosed in double curly braces: {{variable}}
// Variables can be modified with pipe-separated modifiers: {{variable|modifier:param1,param2}}
//
// Parameters:
//   - str: The string to interpolate
//   - m: A map of variable names to values
//
// Returns:
//   - string: The interpolated string
func Interpolate(str string, m map[string]interface{}) string {
	re := regexp.MustCompile(`{{([^{}]*)}}`)

	return re.ReplaceAllStringFunc(str, func(s string) string {
		key := re.FindStringSubmatch(s)[1]
		if !strings.Contains(key, "|") {
			value, exists := m[strings.TrimSpace(key)]
			if !exists {
				return s // Return the original string if the variable doesn't exist
			}
			return fmt.Sprintf("%v", value)
		}

		parts := strings.Split(key, "|")
		variable := trimString(parts[0])
		modifiersWithParams := parts[1:]

		value, exists := m[variable]
		if !exists {
			return s // Return the original string if the variable doesn't exist
		}

		for _, modifierWithParams := range modifiersWithParams {
			modParts := strings.SplitN(trimString(modifierWithParams), ":", 2)
			modifier := modParts[0]
			var params []string

			if len(modParts) > 1 {
				params = strings.Split(trimString(modParts[1]), ",")
			}

			if modifierFn, ok := modifiers[trimString(modifier)]; ok {
				value = modifierFn(value, params)
			} else {
				value = modifiers["noop"](value, params)
			}
		}

		return fmt.Sprintf("%v", value)
	})
}
