package orm

import (
	"context"
	"fmt"
	"log"
	"log/slog"
	"os"
	"time"

	"digisac-go/worker/config"

	"gorm.io/driver/postgres"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"

	gr "github.com/ikateclab/gorm-repository/utils"
)

func SetupDb(config *config.Config) (*gorm.DB, error) {
	ctx := context.Background()

	dsn := fmt.Sprintf(
		"host=%s user=%s password=%s dbname=%s port=%s sslmode=disable",
		config.DbHost,
		config.DbUsername,
		config.DbPassword,
		config.DbName,
		config.DbPort,
	)

	logLevel := logger.Error
	if config.DbLog {
		logLevel = logger.Info
	}

	newLogger := logger.New(
		log.New(os.Stdout, "\r", log.LstdFlags), // io writer
		logger.Config{
			SlowThreshold:             500 * time.Millisecond, // Slow SQL threshold
			LogLevel:                  logLevel,               // Log level
			IgnoreRecordNotFoundError: true,                   // Ignore ErrRecordNotFound error for logger
			ParameterizedQueries:      false,                  // Don't include params in the SQL log
			Colorful:                  true,                   // Colorful log
		},
	)

	db, err := gorm.Open(postgres.Open(dsn), &gorm.Config{
		NamingStrategy:       gr.CamelCaseNamingStrategy{},
		Logger:               newLogger,
		FullSaveAssociations: false,
	})

	if err != nil {
		slog.ErrorContext(ctx, "Failed to open database connection", "dsn", dsn, slog.String("error", fmt.Errorf("could not open database connection: %w", err).Error()))
		return nil, fmt.Errorf("could not open database connection: %w", err)
	}

	sqlDB, err := db.DB()
	if err != nil {
		slog.ErrorContext(ctx, "Failed to get SQL DB instance", slog.String("error", fmt.Errorf("could not get SQL DB instance: %w", err).Error()))
		return nil, fmt.Errorf("could not get SQL DB instance: %w", err)
	}

	sqlDB.SetMaxIdleConns(100)
	sqlDB.SetMaxOpenConns(config.DbMaxConns)
	sqlDB.SetConnMaxLifetime(time.Hour)

	return db, nil
}
