package payloads

import "github.com/google/uuid"

type SyncTemplatesPayload struct {
	ServiceId uuid.UUID `json:"serviceId" validate:"required"`
}

type SyncTemplatesRequest struct {
	Payload  *SyncTemplatesPayload `json:"payload" validate:"required"`
	Metadata *Metadata             `json:"metadata"`
}

type CreateTemplatePayload struct {
	ServiceId  uuid.UUID `json:"serviceId" validate:"required"`
	TemplateId uuid.UUID `json:"templateId" validate:"required"`
}

type CreateTemplateRequest struct {
	Payload  *CreateTemplatePayload `json:"payload" validate:"required"`
	Metadata *Metadata              `json:"metadata"`
}

type DeleteTemplatePayload struct {
	ServiceId  uuid.UUID `json:"serviceId" validate:"required"`
	TemplateId uuid.UUID `json:"templateId" validate:"required"`
}

type DeleteTemplateRequest struct {
	Payload  *DeleteTemplatePayload `json:"payload" validate:"required"`
	Metadata *Metadata              `json:"metadata"`
}
