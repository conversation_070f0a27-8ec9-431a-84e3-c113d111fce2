package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type TicketOrigin string

const (
	TicketOriginAutomatic TicketOrigin = "automatic"
	TicketOriginManual    TicketOrigin = "manual"
	TicketOriginWidget    TicketOrigin = "widget"
)

// @jsonb
type TicketMetrics struct {
	WaitingTime             int  `json:"waitingTime,omitempty"`
	WaitingTimeAfterBot     int  `json:"waitingTimeAfterBot,omitempty"`
	MessagingTime           int  `json:"messagingTime,omitempty"`
	TicketTime              int  `json:"ticketTime,omitempty"`
	WaitingTimeTransfersSum int  `json:"waitingTimeTransfersSum,omitempty"`
	TicketTransferCount     int  `json:"ticketTransferCount,omitempty"`
	WaitingTimeTransfersAvg int  `json:"waitingTimeTransfersAvg,omitempty"`
	IsActiveTicket          bool `json:"isActiveTicket,omitempty"`
}

type Ticket struct {
	Id                      uuid.UUID         `gorm:"type:uuid;primary_key" json:"id,omitempty"`
	IsOpen                  bool              `gorm:"default:true;not null" json:"isOpen,omitempty"`
	Comments                string            `gorm:"type:string" json:"comments,omitempty"`
	Protocol                string            `gorm:"type:string" json:"protocol,omitempty"`
	Origin                  TicketOrigin      `gorm:"type:enum_tickets_origin" json:"origin,omitempty"`
	AccountId               uuid.UUID         `gorm:"type:uuid;default:null" json:"accountId,omitempty"`
	DepartmentId            uuid.UUID         `gorm:"type:uuid;default:null" json:"departmentId,omitempty"`
	ContactId               uuid.UUID         `gorm:"type:uuid;default:null" json:"contactId,omitempty"`
	UserId                  uuid.UUID         `gorm:"type:uuid;default:null" json:"userId,omitempty"`
	FirstMessageId          uuid.UUID         `gorm:"type:uuid;default:null" json:"firstMessageId,omitempty"`
	LastMessageId           uuid.UUID         `gorm:"type:uuid;default:null" json:"lastMessageid,omitempty"`
	CurrentTicketTransferId uuid.UUID         `gorm:"type:uuid;default:null" json:"currentTicketTransferId,omitempty"`
	StartedAt               *time.Time        `gorm:"default:null" json:"startedAt,omitempty"`
	EndedAt                 *time.Time        `gorm:"default:null" json:"endedAt,omitempty"`
	Metrics                 *TicketMetrics    `gorm:"type:jsonb;serializer:json;default:'{}'" json:"metrics,omitempty"`
	Account                 *Account          `gorm:"foreignKey:AccountId" json:"account,omitempty"`
	Contact                 *Contact          `gorm:"foreignKey:ContactId" json:"contact,omitempty"`
	User                    *User             `gorm:"foreignKey:UserId" json:"user,omitempty"`
	Department              *Department       `gorm:"foreignKey:DepartmentId" json:"department,omitempty"`
	Messages                []*Message        `gorm:"foreignKey:TicketId" json:"messages,omitempty"`
	FirstMessage            *Message          `gorm:"foreignKey:FirstMessageId" json:"firstMessage,omitempty"`
	LastMessage             *Message          `gorm:"foreignKey:LastMessageId" json:"lastMessage,omitempty"`
	TicketTransfers         []*TicketTransfer `gorm:"foreignKey:TicketId" json:"ticketTransfers,omitempty"`
	CurrentTicketTransfer   *TicketTransfer   `gorm:"foreignKey:CurrentTicketTransferId" json:"currentTicketTransfer,omitempty"`
	TicketTopics            []*TicketTopic    `gorm:"many2many:ticket_ticket_topics;foreignKey:Id;joinForeignKey:TicketId;References:Id;joinReferences:TicketTopicId" json:"ticketTopics,omitempty"`
	CreatedAt               *time.Time        `json:"createdAt,omitempty"`
	UpdatedAt               *time.Time        `json:"updatedAt,omitempty"`
	IsDistributing          bool              `gorm:"default:false;not null" json:"isDistributing,omitempty"`
	Count                   int               `gorm:"type:int" json:"count,omitempty"`
	Answers                 []*Answer         `gorm:"foreignKey:TicketId" json:"answers,omitempty"`
}

type TicketTicketTopic struct {
	TicketId      uuid.UUID  `json:"ticketId,omitempty"`
	TicketTopicId uuid.UUID  `json:"ticketTopicId,omitempty"`
	CreatedAt     *time.Time `json:"createdAt,omitempty"`
	UpdatedAt     *time.Time `json:"updatedAt,omitempty"`
}

func (Ticket) CreateEnums(db *gorm.DB) error {
	return CreateEnumIfNotExists(db, "enum_tickets_origin", []string{"automatic", "manual", "widget"})
}

func (Ticket) TableName() string {
	return "tickets"
}

func (t *Ticket) BeforeCreate(tx *gorm.DB) (err error) {
	t.Id = uuid.New()
	return
}
