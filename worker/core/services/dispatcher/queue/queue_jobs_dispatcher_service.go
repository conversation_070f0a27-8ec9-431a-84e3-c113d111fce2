package queue

import (
	"bytes"
	"context"
	"digisac-go/worker/config"
	"encoding/json"
	"fmt"
	"net"
	"net/http"
	"time"
)

const (
	ErrUnexpectedStatusCode = "unexpected status code: %d"
	ErrInvalidResponse      = "invalid response from dispatcher"
)

type QueueJobsDispatcherService struct {
	httpClient *http.Client
	baseURL    string
}

type QueueJobsDispatcherDispatchOptions struct {
	HashKey string
}

type QueueJobsDispatcherDispatchBodyPayload struct {
	Payload  interface{}             `json:"payload"`
	Metadata *map[string]interface{} `json:"metadata"`
}

type QueueJobsDispatcherDispatchBody struct {
	Topic   string                                  `json:"topic"`
	HashKey string                                  `json:"hashKey"`
	Payload *QueueJobsDispatcherDispatchBodyPayload `json:"payload"`
}

// NewQueueJobsDispatcherService creates a new instance of QueueJobsDispatcherService
func NewQueueJobsDispatcherService(config *config.Config) *QueueJobsDispatcherService {
	transport := &http.Transport{
		MaxIdleConns:        100,
		IdleConnTimeout:     90 * time.Second,
		DisableCompression:  true,
		MaxIdleConnsPerHost: 100,
		DialContext: (&net.Dialer{
			Timeout:   30 * time.Second,
			KeepAlive: 30 * time.Second,
		}).DialContext,
	}

	return &QueueJobsDispatcherService{
		httpClient: &http.Client{
			Timeout:   30 * time.Second,
			Transport: transport,
		},
		baseURL: config.QueueManagerDispatcherUrl,
	}
}

func (h *QueueJobsDispatcherService) Dispatch(ctx context.Context, jobName string, payload interface{}, options *QueueJobsDispatcherDispatchOptions) error {
	body := &QueueJobsDispatcherDispatchBody{
		Topic: jobName,
		Payload: &QueueJobsDispatcherDispatchBodyPayload{
			Payload: payload,
			Metadata: &map[string]interface{}{
				"context": nil,
			},
		},
	}

	if options != nil && options.HashKey != "" {
		body.HashKey = options.HashKey
	}

	payloadJson, err := json.Marshal(body)
	if err != nil {
		return fmt.Errorf("failed to marshal request body: %w", err)
	}

	httpRequest, err := http.NewRequestWithContext(ctx, http.MethodPost, h.baseURL, bytes.NewBuffer(payloadJson))
	if err != nil {
		return fmt.Errorf("failed to create request: %w", err)
	}

	httpRequest.Header.Set("Content-Type", "application/json")

	response, err := h.httpClient.Do(httpRequest)
	if err != nil {
		return fmt.Errorf("failed to execute request: %w", err)
	}

	defer func() {
		if err := response.Body.Close(); err != nil {
			fmt.Println("Error closing response body:", err)
		}
	}()

	if response.StatusCode != http.StatusOK {
		return fmt.Errorf(ErrUnexpectedStatusCode, response.StatusCode)
	}

	return nil
}
