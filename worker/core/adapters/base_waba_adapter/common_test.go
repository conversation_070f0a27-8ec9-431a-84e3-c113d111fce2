//go:build unit

package base_waba_adapter

import (
	"digisac-go/worker/core/adapters/adapter_types"
	"testing"
)

func TestCleanNumber(t *testing.T) {
	adapter := &BaseWabaAdapter{}

	tests := []struct {
		input    string
		expected string
	}{
		{"(11) 91234-5678", "***********"},
		{"+55 21 98765-4321", "5521987654321"},
		{"abc123def456", "123456"},
		{"", ""},
		{"000-111", "000111"},
	}

	for _, tt := range tests {
		result := adapter.cleanNumber(tt.input)
		if result != tt.expected {
			t.<PERSON><PERSON><PERSON>("cleanNumber(%q) = %q; want %q", tt.input, result, tt.expected)
		}
	}
}

func TestGetVcard(t *testing.T) {
	adapter := &BaseWabaAdapter{}

	tests := []struct {
		name    string
		contact *adapter_types.WhatsappBusinessWebhookContactVcard
		want    string
		wantErr bool
	}{
		{
			name: "Full contact with WaId",
			contact: &adapter_types.WhatsappBusinessWebhookContactVcard{
				Name: &adapter_types.WhatsappBusinessWebhookContactVcardName{
					FirstName: "John",
					LastName:  "Doe",
				},
				Phones: []*adapter_types.WhatsappBusinessWebhookContactVcardPhone{
					{WaId: "1234567890", Phone: "******-567-890"},
				},
			},
			want: "BEGIN:VCARD\nVERSION:3.0\nN:Doe;John;;;\nFN:John Doe\nitem1.TEL;waid=1234567890:1234567890\nitem1.X-ABLabel:Mobile\nEND:VCARD",
		},
		{
			name: "Contact without WaId",
			contact: &adapter_types.WhatsappBusinessWebhookContactVcard{
				Name: &adapter_types.WhatsappBusinessWebhookContactVcardName{
					FirstName: "Alice",
					LastName:  "Smith",
				},
				Phones: []*adapter_types.WhatsappBusinessWebhookContactVcardPhone{
					{WaId: "", Phone: "+44 20 7946 0958"},
				},
			},
			want: "BEGIN:VCARD\nVERSION:3.0\nN:Smith;Alice;;;\nFN:Alice Smith\nitem1.TEL;waid=442079460958:442079460958\nitem1.X-ABLabel:Mobile\nEND:VCARD",
		},
		{
			name: "Contact without name",
			contact: &adapter_types.WhatsappBusinessWebhookContactVcard{
				Name: nil,
				Phones: []*adapter_types.WhatsappBusinessWebhookContactVcardPhone{
					{WaId: "9876543210", Phone: "+98 765-432-10"},
				},
			},
			want: "BEGIN:VCARD\nVERSION:3.0\nN:;;;;\nFN: \nitem1.TEL;waid=9876543210:9876543210\nitem1.X-ABLabel:Mobile\nEND:VCARD",
		},
		{
			name: "Contact without phone",
			contact: &adapter_types.WhatsappBusinessWebhookContactVcard{
				Name: &adapter_types.WhatsappBusinessWebhookContactVcardName{
					FirstName: "No",
					LastName:  "Phone",
				},
				Phones: []*adapter_types.WhatsappBusinessWebhookContactVcardPhone{},
			},
			want: "BEGIN:VCARD\nVERSION:3.0\nN:Phone;No;;;\nFN:No Phone\nitem1.TEL;waid=:\nitem1.X-ABLabel:Mobile\nEND:VCARD",
		},
		{
			name: "Contact with empty phone",
			contact: &adapter_types.WhatsappBusinessWebhookContactVcard{
				Name: &adapter_types.WhatsappBusinessWebhookContactVcardName{
					FirstName: "Empty",
					LastName:  "Phone",
				},
				Phones: []*adapter_types.WhatsappBusinessWebhookContactVcardPhone{
					{WaId: "111222333", Phone: ""},
				},
			},
			want: "BEGIN:VCARD\nVERSION:3.0\nN:Phone;Empty;;;\nFN:Empty Phone\nitem1.TEL;waid=111222333:111222333\nitem1.X-ABLabel:Mobile\nEND:VCARD",
		},
		{
			name:    "Nil contact",
			contact: nil,
			want:    "",
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := adapter.getVcard(tt.contact)
			if (err != nil) != tt.wantErr {
				t.Errorf("getVcard() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("getVcard() = %q, want %q", got, tt.want)
			}
		})
	}
}

func TestGetWebhookMessageType(t *testing.T) {
	adapter := &BaseWabaAdapter{}

	tests := []struct {
		name    string
		message *adapter_types.WhatsappBusinessWebhookMessage
		want    adapter_types.MessageTypeEnum
		wantErr bool
	}{
		{
			name:    "Text message",
			message: &adapter_types.WhatsappBusinessWebhookMessage{Type: "text"},
			want:    adapter_types.MESSAGE_TYPE_TEXT,
		},
		{
			name:    "Button message",
			message: &adapter_types.WhatsappBusinessWebhookMessage{Type: "button"},
			want:    adapter_types.MESSAGE_TYPE_TEXT,
		},
		{
			name:    "Image message",
			message: &adapter_types.WhatsappBusinessWebhookMessage{Type: "image"},
			want:    adapter_types.MESSAGE_TYPE_IMAGE,
		},
		{
			name:    "Video message",
			message: &adapter_types.WhatsappBusinessWebhookMessage{Type: "video"},
			want:    adapter_types.MESSAGE_TYPE_VIDEO,
		},
		{
			name:    "Audio message",
			message: &adapter_types.WhatsappBusinessWebhookMessage{Type: "audio"},
			want:    adapter_types.MESSAGE_TYPE_VOICE,
		},
		{
			name:    "Sticker message",
			message: &adapter_types.WhatsappBusinessWebhookMessage{Type: "sticker"},
			want:    adapter_types.MESSAGE_TYPE_STICKER,
		},
		{
			name:    "Document message",
			message: &adapter_types.WhatsappBusinessWebhookMessage{Type: "document"},
			want:    adapter_types.MESSAGE_TYPE_DOCUMENT,
		},
		{
			name:    "Location message",
			message: &adapter_types.WhatsappBusinessWebhookMessage{Type: "location"},
			want:    adapter_types.MESSAGE_TYPE_LOCATION,
		},
		{
			name:    "Reaction message",
			message: &adapter_types.WhatsappBusinessWebhookMessage{Type: "reaction"},
			want:    adapter_types.MESSAGE_TYPE_REACTION,
		},
		{
			name: "Contact message",
			message: &adapter_types.WhatsappBusinessWebhookMessage{
				Type:     "unknown",
				Contacts: []*adapter_types.WhatsappBusinessWebhookContactVcard{{}},
			},
			want: adapter_types.MESSAGE_TYPE_VCARD,
		},
		{
			name:    "Unknown type",
			message: &adapter_types.WhatsappBusinessWebhookMessage{Type: "unknown"},
			wantErr: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := adapter.getWebhookMessageType(tt.message)
			if (err != nil) != tt.wantErr {
				t.Errorf("getWebhookMessageType() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("getWebhookMessageType() = %v, want %v", got, tt.want)
			}
		})
	}
}
