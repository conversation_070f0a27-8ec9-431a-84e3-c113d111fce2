package telegram_adapter

import (
	"context"
	"digisac-go/worker/core/adapters/adapter_types"
	"digisac-go/worker/core/models"
	fileutils "digisac-go/worker/core/utils/file_utils"
	"fmt"
	"log/slog"
	"strconv"
	"strings"
	"time"

	"github.com/PaulSonOfLars/gotgbot/v2"
	"github.com/google/uuid"
)

func getReplyParameters(ctx context.Context, ReplyMessageId string) (replyParameters *gotgbot.ReplyParameters, err error) {
	if ReplyMessageId != "" {
		replyMessageId, err := strconv.ParseInt(ReplyMessageId, 10, 64)

		if err != nil {
			slog.ErrorContext(ctx, "Failed to parse ReplyMessageId", slog.String("ReplyMessageId", ReplyMessageId), slog.Any("error", err))
			return nil, fmt.Errorf("failed to parse ReplyMessageId: %w", err)
		}

		replyParameters = &gotgbot.ReplyParameters{
			MessageId: replyMessageId,
		}
	}
	return replyParameters, nil
}

func getErrorResponse(ctx context.Context, err error) (*adapter_types.SendMessageResponse, error) {
	if telegramError, ok := err.(*gotgbot.TelegramError); ok {
		responseError := &models.MessageError{
			Code:          telegramError.Code,
			OriginalError: telegramError,
		}

		if telegramError.Description != "" {
			errorAndMessage := strings.Split(telegramError.Description, ":")

			if len(errorAndMessage) > 1 {
				responseError.Error = strings.TrimSpace(errorAndMessage[0])
				responseError.Message = strings.TrimSpace(errorAndMessage[1])
			} else {
				responseError.Message = strings.TrimSpace(errorAndMessage[0])
			}
		}

		sendMessageResponse := &adapter_types.SendMessageResponse{
			Error: responseError,
			Ack:   "error",
		}
		return sendMessageResponse, nil

	} else {
		slog.DebugContext(ctx, "Generic error", slog.Any("message", err))
		return nil, fmt.Errorf("generic error: %w", err)
	}
}

// Busca informações pelo http Head senão headers do http GET senão body do GET.
func (a *TelegramAdapter) getFileInfo(ctx context.Context, serviceId uuid.UUID, fileId string) (*fileutils.GetFileInfoResponse, error) {
	client, err := a.getClient(ctx, serviceId)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to get client", slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return nil, fmt.Errorf("failed to get client: %w", err)
	}

	file, err := client.GetFile(fileId, &gotgbot.GetFileOpts{})
	if err != nil {
		slog.ErrorContext(ctx, "Failed to get file", slog.String("fileId", fileId), slog.Any("error", err))
		return nil, fmt.Errorf("failed to get file: %w", err)
	}

	url := file.URL(client, &gotgbot.RequestOpts{Timeout: 2 * time.Minute})

	return fileutils.GetFileInfo(url)
}

func (a *TelegramAdapter) getFile(ctx context.Context, serviceId uuid.UUID, p *TelegramWebhookPayload, msgType adapter_types.MessageTypeEnum) (response *adapter_types.WebhookMessageFile, err error) {
	switch msgType {
	case adapter_types.MESSAGE_TYPE_STICKER:
		response = &adapter_types.WebhookMessageFile{
			Id:       p.Payload.Message.Sticker.FileId,
			Mimetype: "",
			Url:      "",
			Name:     "",
		}
	case adapter_types.MESSAGE_TYPE_DOCUMENT:
		{
			response = &adapter_types.WebhookMessageFile{
				Id:       p.Payload.Message.Document.FileId,
				Mimetype: p.Payload.Message.Document.MimeType,
				Url:      "",
				Name:     p.Payload.Message.Document.FileName,
			}
			break
		}
	case adapter_types.MESSAGE_TYPE_VOICE:
		{
			response = &adapter_types.WebhookMessageFile{
				Id:       p.Payload.Message.Voice.FileId,
				Mimetype: p.Payload.Message.Voice.MimeType,
				Url:      "",
				Name:     "",
			}
			break
		}
	case adapter_types.MESSAGE_TYPE_IMAGE:
		{
			last := len(p.Payload.Message.Photo) // pega a maior foto, que está no final do array

			response = &adapter_types.WebhookMessageFile{
				Id:       p.Payload.Message.Photo[last-1].FileId,
				Mimetype: "",
				Url:      "",
				Name:     "",
			}

			break
		}
	case adapter_types.MESSAGE_TYPE_VIDEO:
		{
			if p.Payload.Message.Animation != nil {
				response = &adapter_types.WebhookMessageFile{
					Id:       p.Payload.Message.Animation.FileId,
					Mimetype: p.Payload.Message.Animation.MimeType,
					Url:      "",
					Name:     p.Payload.Message.Animation.FileName,
				}
				break
			}
			response = &adapter_types.WebhookMessageFile{
				Id:       p.Payload.Message.Video.FileId,
				Mimetype: p.Payload.Message.Video.MimeType,
				Url:      "",
				Name:     p.Payload.Message.Video.FileName,
			}
			break
		}
	}

	// É mídia
	if response != nil && (response.Name == "" || response.Mimetype == "") {
		fileInfo, err := a.getFileInfo(ctx, serviceId, response.Id)

		if err != nil {
			slog.ErrorContext(ctx, "Failed to get file info", slog.String("serviceId", serviceId.String()), slog.String("fileId", response.Id), slog.Any("error", err))
			return nil, fmt.Errorf("failed to get file info for fileId %s: %w", response.Id, err)
		}

		if response.Name == "" {
			response.Name = fileInfo.Name
		}

		if response.Mimetype == "" {
			response.Mimetype = fileInfo.Mimetype
		}
	}

	return response, nil
}

func getWebhookMessageType(p *TelegramWebhookPayload) (adapter_types.MessageTypeEnum, error) {
	if p.Payload.MessageReaction != nil {
		return adapter_types.MESSAGE_TYPE_REACTION, nil
	}

	message := p.Payload.Message

	if message == nil && p.Payload.EditedMessage != nil {
		message = p.Payload.EditedMessage
	}

	if message != nil {
		if message.Text != "" {
			return adapter_types.MESSAGE_TYPE_TEXT, nil
		}

		if message.Sticker != nil {
			return adapter_types.MESSAGE_TYPE_STICKER, nil
		}

		if message.Animation != nil && message.Document != nil {
			return adapter_types.MESSAGE_TYPE_ANIMATION, nil
		}

		if message.Document != nil {
			return adapter_types.MESSAGE_TYPE_DOCUMENT, nil
		}

		if message.Voice != nil || message.Audio != nil {
			return adapter_types.MESSAGE_TYPE_VOICE, nil
		}

		if message.Photo != nil {
			return adapter_types.MESSAGE_TYPE_IMAGE, nil
		}

		if message.Video != nil {
			return adapter_types.MESSAGE_TYPE_VIDEO, nil
		}

		if message.Location != nil {
			return adapter_types.MESSAGE_TYPE_LOCATION, nil
		}

		if message.Contact != nil {
			return adapter_types.MESSAGE_TYPE_VCARD, nil
		}
	}

	return "", fmt.Errorf("webhook message type unknown or not implemented yet")
}
