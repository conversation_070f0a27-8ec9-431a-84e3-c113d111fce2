package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// MessageOriginalError
// @jsonb
type MessageOriginalError = any

// MessageError
// @jsonb
type MessageError struct {
	Code          int                  `json:"code"`          // ex: 403
	Error         string               `json:"error"`         // ex: Forbidden
	Message       string               `json:"message"`       // ex: bot was blocked by the user
	OriginalError MessageOriginalError `json:"originalError"` // retorno com todos os dados
}

// @jsonb
type FileDownload struct {
	StartedAt     *time.Time `json:"startedAt"`
	EndedAt       *time.Time `json:"endedAt"`
	IsDownloading bool       `json:"isDownloading"`
}

// @jsonb
type Location struct {
	Lat        float64 `json:"lat,omitempty"`
	Lng        float64 `json:"lng,omitempty"`
	PreviewUrl string  `json:"previewUrl,omitempty"`
}

// @jsonb
type CtwaContext struct {
	ConversionSource string `json:"conversionSource,omitempty"`
	Description      string `json:"description,omitempty"`
	IsSuspiciousLink string `json:"isSuspiciousLink,omitempty"`
	MediaType        int32  `json:"mediaType,omitempty"`
	MediaUrl         string `json:"mediaUrl,omitempty"`
	SourceUrl        string `json:"sourceUrl,omitempty"`
	ThumbnailUrl     string `json:"thumbnailUrl,omitempty"`
	Title            string `json:"title,omitempty"`
}

// MessageHsmParameterImage
// @jsonb
type MessageHsmParameterImage struct {
	Link string `json:"link"`
}

// MessageHsmParameters
// @jsonb
type MessageHsmParameters struct {
	Type  string                    `json:"type"`
	Text  string                    `json:"text"`
	Image *MessageHsmParameterImage `json:"image,omitempty"`
}

// HsmParameters
// @jsonb
type HsmParameters struct {
	Type       string                  `json:"type"`
	Parameters []*MessageHsmParameters `json:"parameters"`
}

// @jsonb
type InteractiveMessage struct {
	Type   string             `json:"type,omitempty"` // "button" or "list"
	Header *InteractiveHeader `json:"header,omitempty"`
	Body   *InteractiveBody   `json:"body,omitempty"`
	Footer *InteractiveFooter `json:"footer,omitempty"`
	Action *InteractiveAction `json:"action,omitempty"`
}

// @jsonb
type InteractiveHeader struct {
	Type  string `json:"type"` // "text", "image", "video", "document"
	Text  string `json:"text,omitempty"`
	Image *struct {
		Link string `json:"link"`
	} `json:"image,omitempty"`
	Video *struct {
		Link string `json:"link"`
	} `json:"video,omitempty"`
	Document *struct {
		Link string `json:"link"`
	} `json:"document,omitempty"`
}

// @jsonb
type InteractiveBody struct {
	Text string `json:"text"`
}

// @jsonb
type InteractiveFooter struct {
	Text string `json:"text"`
}

// @jsonb
type InteractiveAction struct {
	Button   string                `json:"button,omitempty"`   // Required for list type
	Buttons  []*InteractiveButton  `json:"buttons,omitempty"`  // For type "button"
	Sections []*InteractiveSection `json:"sections,omitempty"` // For type "list"
}

// @jsonb
type InteractiveButton struct {
	Type  string                  `json:"type"` // "reply"
	Reply *InteractiveReplyButton `json:"reply"`
}

// @jsonb
type InteractiveReplyButton struct {
	Id    string `json:"id"`
	Title string `json:"title"`
}

// @jsonb
type InteractiveSection struct {
	Title string            `json:"title,omitempty"`
	Rows  []*InteractiveRow `json:"rows"`
}

// @jsonb
type InteractiveRow struct {
	Id          string `json:"id"`
	Title       string `json:"title"`
	Description string `json:"description,omitempty"`
}

type MessageDataVCard struct {
	VCard string `json:"vcard"`
}

// MessageData
// @jsonb
type MessageData struct {
	Ack               interface{}         `json:"ack"`
	IsFirst           bool                `json:"isFirst,omitempty"`
	IsNew             bool                `json:"isNew,omitempty"`
	HasReaction       bool                `json:"hasReaction,omitempty"`
	Error             *MessageError       `json:"error,omitempty"`
	FileDownload      *FileDownload       `json:"fileDownload,omitempty"`
	MediaId           string              `json:"mediaId,omitempty"`
	MediaUrl          string              `json:"mediaUrl,omitempty"`
	IsFromFirstSync   bool                `json:"isFromFirstSync,omitempty"`
	DontOpenTicket    bool                `json:"dontOpenTicket,omitempty"`
	TicketOpen        bool                `json:"ticketOpen,omitempty"`
	TicketTransfer    bool                `json:"ticketTransfer,omitempty"`
	TicketClose       bool                `json:"ticketClose,omitempty"`
	Location          *Location           `json:"location,omitempty"`
	VCard             interface{}         `json:"vcard,omitempty"` // normalizado automaticamente no Scan
	CtwaContext       *CtwaContext        `json:"ctwaContext,omitempty"`
	Edited            bool                `json:"edited,omitempty"`
	HsmParameters     []*HsmParameters    `json:"hsmParameters,omitempty"`
	Interactive       *InteractiveMessage `json:"interactive,omitempty"`
	WhatsappMessageId string              `json:"whatsappMessageId,omitempty"`
}

type Message struct {
	Id                       uuid.UUID                 `gorm:"type:uuid;primary_key" json:"id,omitempty"`
	IdFromService            string                    `gorm:"type:string;default:null" json:"idFromService,omitempty"`
	Text                     string                    `gorm:"type:text" json:"text,omitempty"`
	Type                     string                    `gorm:"type:string" json:"type,omitempty"`
	Timestamp                *time.Time                `gorm:"type:timestamptz;default:null;not null" json:"timestamp,omitempty"`
	IsFromMe                 bool                      `gorm:"default:false" json:"isFromMe,omitempty"`
	IsFromBot                bool                      `gorm:"default:false" json:"isFromBot,omitempty"`
	IsFromSync               bool                      `gorm:"default:false" json:"isFromSync,omitempty"`
	Sent                     bool                      `gorm:"default:false" json:"sent,omitempty"`
	Visible                  bool                      `gorm:"default:true;not null" json:"visible,omitempty"`
	Data                     *MessageData              `gorm:"type:jsonb;serializer:json;default:'{}';not null;" json:"data,omitempty"`
	Origin                   string                    `gorm:"type:string" json:"origin,omitempty"`
	HsmId                    uuid.UUID                 `gorm:"type:uuid;default:null" json:"hsmId,omitempty"`
	IsComment                bool                      `gorm:"default:false" json:"isComment,omitempty"`
	AccountId                uuid.UUID                 `gorm:"type:uuid;default:null" json:"accountId,omitempty"`
	ServiceId                uuid.UUID                 `gorm:"type:uuid;default:null" json:"serviceId,omitempty"`
	ContactId                uuid.UUID                 `gorm:"type:uuid;default:null" json:"contactId,omitempty"`
	FromId                   uuid.UUID                 `gorm:"type:uuid;default:null" json:"fromId,omitempty"`
	ToId                     uuid.UUID                 `gorm:"type:uuid;default:null" json:"toId,omitempty"`
	UserId                   uuid.UUID                 `gorm:"type:uuid;default:null" json:"userId,omitempty"`
	TicketId                 uuid.UUID                 `gorm:"type:uuid;default:null" json:"ticketId,omitempty"`
	CampaignId               uuid.UUID                 `gorm:"type:uuid;default:null" json:"campaignId,omitempty"`
	ReactionParentMessageId  uuid.UUID                 `gorm:"type:uuid;default:null" json:"reactionParentMessageId,omitempty"`
	QuotedMessageId          uuid.UUID                 `gorm:"type:uuid;default:null" json:"quotedMessageId,omitempty"`
	TicketUserId             uuid.UUID                 `gorm:"type:uuid;default:null" json:"ticketUserId,omitempty"`
	TicketDepartmentId       uuid.UUID                 `gorm:"type:uuid;default:null" json:"ticketDepartmentId,omitempty"`
	BotId                    uuid.UUID                 `gorm:"type:uuid;default:null" json:"botId,omitempty"`
	HsmFileId                uuid.UUID                 `gorm:"type:uuid;default:null" json:"hsmFileId,omitempty"`
	CreatedAt                *time.Time                `json:"createdAt,omitempty"`
	UpdatedAt                *time.Time                `json:"updatedAt,omitempty"`
	DeletedAt                *gorm.DeletedAt           `gorm:"index" json:"deletedAt,omitempty"`
	Account                  *Account                  `gorm:"foreignKey:AccountId" json:"account,omitempty"`
	Service                  *Service                  `gorm:"foreignKey:ServiceId" json:"service,omitempty"`
	Contact                  *Contact                  `gorm:"foreignKey:ContactId" json:"contact,omitempty"`
	From                     *Contact                  `gorm:"foreignKey:FromId" json:"from,omitempty"`
	To                       *Contact                  `gorm:"foreignKey:ToId" json:"to,omitempty"`
	User                     *User                     `gorm:"foreignKey:UserId" json:"user,omitempty"`
	Ticket                   *Ticket                   `gorm:"foreignKey:TicketId" json:"ticket,omitempty"`
	File                     *File                     `gorm:"foreignKey:AttachedId" json:"file,omitempty"`
	Files                    []*File                   `gorm:"foreignKey:AttachedId" json:"files,omitempty"`
	Preview                  *File                     `gorm:"foreignKey:AttachedId" json:"preview,omitempty"`
	Thumbnail                *File                     `gorm:"foreignKey:AttachedId" json:"thumbnail,omitempty"`
	QuotedMessage            *Message                  `gorm:"foreignKey:QuotedMessageId" json:"quotedMessage,omitempty"`
	TicketUser               *User                     `gorm:"foreignKey:TicketUserId" json:"ticketUser,omitempty"`
	TicketDepartment         *Department               `gorm:"foreignKey:TicketDepartmentId" json:"ticketDepartment,omitempty"`
	TicketTransfer           *TicketTransfer           `gorm:"foreignKey:TransferredMessageId" json:"ticketTransfer,omitempty"`
	WhatsappBusinessTemplate *WhatsappBusinessTemplate `gorm:"foreignKey:HsmId" json:"whatsappBusinessTemplate,omitempty"`
	// CampaignMessageProgress CampaignMessageProgress  `gorm:"foreignKey:MessageId"`
	// Campaign                Campaign                 `gorm:"foreignKey:CampaignId"`
	// HsmFile                 File                     `gorm:"foreignKey:HsmFileId"`
	// Bot                     Bot                      `gorm:"foreignKey:BotId"`
}

func (Message) TableName() string {
	return "messages"
}

func (m *Message) BeforeCreate(tx *gorm.DB) (err error) {
	m.Id = uuid.New()
	return
}
