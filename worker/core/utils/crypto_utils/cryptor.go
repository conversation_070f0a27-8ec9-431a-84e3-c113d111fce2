package crypto

import (
	"bytes"
	"crypto/aes"
	"crypto/cipher"
	"crypto/rand"
	"encoding/base64"
	"encoding/hex"
	"errors"
	"fmt"
)

const (
	Algorithm = "aes-256-ctr"
	IvLength  = 16
	KeyLength = 32
)

func RandomBytes(n int) ([]byte, error) {
	bytes := make([]byte, n)
	if _, err := rand.Read(bytes); err != nil {
		return nil, err
	}
	return bytes, nil
}

func CreateCipher(iv, key []byte) (cipher.Stream, error) {
	block, err := aes.NewCipher(key)
	if err != nil {
		return nil, err
	}
	return cipher.NewCTR(block, iv), nil
}

func CreateCipherWithIv(key []byte) (cipher.Stream, []byte, error) {
	iv, err := RandomBytes(IvLength)
	if err != nil {
		return nil, nil, err
	}
	cipher, err := CreateCipher(iv, key)
	if err != nil {
		return nil, nil, err
	}
	return cipher, iv, nil
}

func CreateDecipher(iv []byte, key []byte) (cipher.Stream, error) {
	return CreateCipher(iv, key)
}

func EncryptText(text string, key []byte) (string, error) {
	if text == "" {
		return text, nil
	}

	cipher, iv, err := CreateCipherWithIv(key)
	if err != nil {
		return "", err
	}

	encrypted := make([]byte, len(text))
	cipher.XORKeyStream(encrypted, []byte(text))

	return fmt.Sprintf("%s$%s", hex.EncodeToString(iv), hex.EncodeToString(encrypted)), nil
}

func DecryptText(text string, key []byte) (string, error) {
	if text == "" || !bytes.Contains([]byte(text), []byte("$")) {
		return text, nil
	}

	parts := bytes.Split([]byte(text), []byte("$"))
	if len(parts) != 2 {
		return "", errors.New("invalid encrypted text format")
	}

	ivHex := parts[0]
	encryptedText := parts[1]

	iv, err := hex.DecodeString(string(ivHex))
	if err != nil || len(iv) != IvLength {
		return "", errors.New("invalid IV format")
	}

	encrypted, err := hex.DecodeString(string(encryptedText))
	if err != nil {
		return "", err
	}

	decipher, err := CreateDecipher(iv, key)
	if err != nil {
		return "", err
	}

	decrypted := make([]byte, len(encrypted))
	decipher.XORKeyStream(decrypted, encrypted)

	return string(decrypted), nil
}

func EncryptBufferWithConcatenatedIv(buffer, key []byte) ([]byte, error) {
	cipher, iv, err := CreateCipherWithIv(key)
	if err != nil {
		return nil, err
	}

	encrypted := make([]byte, len(buffer))
	cipher.XORKeyStream(encrypted, buffer)

	return append(iv, encrypted...), nil
}

func DecryptBufferWithConcatenatedIv(buffer, key []byte) ([]byte, error) {
	if len(buffer) < IvLength {
		return nil, errors.New("invalid buffer format")
	}

	iv := buffer[:IvLength]
	encryptedBuffer := buffer[IvLength:]

	decipher, err := CreateDecipher(iv, key)
	if err != nil {
		return nil, err
	}

	decrypted := make([]byte, len(encryptedBuffer))
	decipher.XORKeyStream(decrypted, encryptedBuffer)

	return decrypted, nil
}

func EncryptBufferWithSeparatedIv(buffer, key []byte) ([]byte, []byte, error) {
	cipher, iv, err := CreateCipherWithIv(key)
	if err != nil {
		return nil, nil, err
	}

	encrypted := make([]byte, len(buffer))
	cipher.XORKeyStream(encrypted, buffer)

	return encrypted, iv, nil
}

func DecryptBufferWithSeparatedIv(buffer []byte, iv []byte, key []byte) ([]byte, error) {
	if len(iv) != IvLength {
		return nil, errors.New("invalid IV length")
	}

	decipher, err := CreateDecipher(iv, key)
	if err != nil {
		return nil, err
	}

	decrypted := make([]byte, len(buffer))
	decipher.XORKeyStream(decrypted, buffer)

	return decrypted, nil
}

func GenerateToken() (string, error) {
	tokenBytes, err := RandomBytes(KeyLength)
	if err != nil {
		return "", err
	}

	return base64.StdEncoding.EncodeToString(tokenBytes), nil
}
