package campaign

import (
	"context"
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/repositories"
	"fmt"
	"log/slog"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type CampaignService struct {
	campaignRepository repositories.CampaignRepository
	accountRepository  repositories.AccountRepository
}

func NewCampaignService(
	campaignRepository repositories.CampaignRepository,
	accountRepository repositories.AccountRepository,
) *CampaignService {
	return &CampaignService{
		campaignRepository: campaignRepository,
		accountRepository:  accountRepository,
	}
}

// PauseCampaignsFromTemplateId pausa todas as campanhas ativas que usam um determinado template
// com base nas configurações de auto-pausa da conta e na qualidade do template
func (s *CampaignService) PauseCampaignsFromTemplateId(
	ctx context.Context,
	templateId uuid.UUID,
	accountId uuid.UUID,
	serviceId uuid.UUID,
	templateQuality models.WhatsappBusinessTemplateQualityEnum,
) (int, error) {
	// Buscar a conta para verificar as configurações
	account, err := s.accountRepository.FindById(ctx, accountId)
	if err != nil {
		return 0, fmt.Errorf("failed to find account for template: %w", err)
	}

	// Verificar o modo de auto-pausa
	autoPauseMode := models.AutoPauseModeDisabled
	if account.Settings.Campaign != nil && account.Settings.Campaign.AutoPauseMode != "" {
		autoPauseMode = account.Settings.Campaign.AutoPauseMode
	}

	// Verificar se deve pausar com base na qualidade e no modo configurado
	shouldPauseForMedium := autoPauseMode == models.AutoPauseModeMediumHealth && templateQuality == models.WhatsappBusinessTemplateQualityMedium
	shouldPauseForLow := autoPauseMode == models.AutoPauseModeLowHealth && templateQuality == models.WhatsappBusinessTemplateQualityLow

	if !shouldPauseForMedium && !shouldPauseForLow {
		slog.InfoContext(ctx, "Skipping campaign pause due to account settings",
			slog.String("templateId", templateId.String()),
			slog.String("autoPauseMode", string(autoPauseMode)),
			slog.String("templateQuality", string(templateQuality)))
		return 0, nil
	}

	slog.InfoContext(ctx, "Pausing campaigns due to template quality based on account settings",
		slog.String("templateId", templateId.String()),
		slog.String("autoPauseMode", string(autoPauseMode)),
		slog.String("templateQuality", string(templateQuality)))

	// Buscar campanhas ativas que usam o template
	campaigns, err := s.campaignRepository.FindMany(ctx, repositories.WithQuery(func(db *gorm.DB) *gorm.DB {
		return db.Where(&models.Campaign{
			AccountId:  accountId,
			ServiceId:  serviceId,
			FinishedAt: nil,
		}).Where(`status not in ('paused', 'canceled', 'done', 'import_error', 'error')`).
			Joins(`INNER JOIN campaign_messages ON campaign_messages."campaignId" = campaigns.id`).
			Where(`campaign_messages."hsmId" = ?`, templateId)
	}))

	if err != nil {
		return 0, fmt.Errorf("failed to find campaigns for template: %w", err)
	}

	pausedCampaignsCount := 0
	if len(campaigns) > 0 {
		for _, campaign := range campaigns {
			campaign.Status = "paused"
			err = s.campaignRepository.UpdateById(ctx, campaign.Id, campaign)
			if err != nil {
				slog.ErrorContext(ctx, "Failed to pause campaign",
					slog.String("error", err.Error()),
					slog.String("campaignId", campaign.Id.String()))
				continue // Continuar mesmo com erro para pausar outras campanhas
			}
			pausedCampaignsCount++
		}
	}

	return pausedCampaignsCount, nil
}
