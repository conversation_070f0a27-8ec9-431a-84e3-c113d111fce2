package http

import (
	"errors"
	"fmt"
	"reflect"

	"github.com/go-playground/validator/v10"
)

type ValidationErrorResponse struct {
	Error   string                           `json:"error"`
	Message string                           `json:"message"`
	Status  int                              `json:"status"`
	Errors  map[string]map[string]FieldError `json:"errors"`
}

type FieldError struct {
	Messages []string `json:"messages"`
	Types    []string `json:"types"`
}

var validatorTagToCustomName = map[string]string{
	"required":         "required",
	"required_without": "requiredIfOtherNotPresent",
	"required_with":    "requiredIfOtherPresent",
	"string":           "string",
	"number":           "number",
	"boolean":          "boolean",
	"array":            "array",
	"oneof":            "oneOf",
	"uuid4":            "uuid4",
	"gt":               "isGreaterThan",
	"gte":              "isGreaterThanOrEqual",
	"lt":               "hasLengthLesserThan",
	"lte":              "hasLengthLesserThanOrEqual",
	"min":              "hasLengthGreaterThanOrEqual",
	"max":              "hasLengthLesserThanOrEqual",
	"len":              "hasLengthGreaterThan",
	"regex":            "regex",
	"arrayOf":          "arrayOf",
}

var validatorTagToCustomMessage = map[string]string{
	"required":         "Required.",
	"required_without": "Required if \"%s\" was not given.",
	"required_with":    "Required if \"%s\" was given.",
	"string":           "Should be of type string.",
	"number":           "Should be of type number.",
	"boolean":          "Should be of type boolean.",
	"array":            "Should be an array.",
	"oneof":            "Should be one of the values: %s.",
	"uuid4":            "Should be an UUID v4 string.",
	"gt":               "Should be greater than %s.",
	"gte":              "Should be greater or equal %s.",
	"lt":               "Should contain lesser than %s characters.",
	"lte":              "Should contain lesser than or equals %s characters.",
	"min":              "Should contain at least %s characters.",
	"max":              "Should contain at most %s characters.",
	"len":              "Should contain exactly %s characters.",
	"regex":            "Should match regex \"%s\".",
	"arrayOf":          "Should be an array of \"%s\".",
}

func FormatValidationErrors(err error, structType reflect.Type) ValidationErrorResponse {
	validationErrors := make(map[string]FieldError)

	var errs validator.ValidationErrors
	if errors.As(err, &errs) {
		for _, e := range errs {
			field := getJSONFieldName(structType, e.Field())
			tag := e.Tag()
			customName := getCustomName(tag)
			paramField := getJSONFieldName(structType, e.Param())
			customMessage := getCustomMessage(tag, paramField)

			// If the field already exists, append the new message and type
			if fieldError, exists := validationErrors[field]; exists {
				fieldError.Messages = append(fieldError.Messages, customMessage)
				fieldError.Types = append(fieldError.Types, customName)
				validationErrors[field] = fieldError
			} else {
				validationErrors[field] = FieldError{
					Messages: []string{customMessage},
					Types:    []string{customName},
				}
			}
		}
	}

	bodyErrors := map[string]map[string]FieldError{
		"body": validationErrors,
	}

	return ValidationErrorResponse{
		Error:   "ValidationError",
		Message: "The given data was invalid.",
		Status:  400,
		Errors:  bodyErrors,
	}
}

func getCustomName(tag string) string {
	if customName, exists := validatorTagToCustomName[tag]; exists {
		return customName
	}
	return tag
}

func getCustomMessage(tag, param string) string {
	if customMessage, exists := validatorTagToCustomMessage[tag]; exists {
		return fmt.Sprintf(customMessage, param)
	}
	return fmt.Sprintf("Validation error on tag \"%s\" with parameter \"%s\".", tag, param)
}

func getJSONFieldName(structType reflect.Type, fieldName string) string {
	field, found := structType.FieldByName(fieldName)
	if !found {
		return fieldName
	}
	jsonTag := field.Tag.Get("json")
	if jsonTag == "" {
		return fieldName
	}
	return jsonTag
}
