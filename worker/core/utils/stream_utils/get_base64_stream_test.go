//go:build unit

package stream

import (
	"bytes"
	"encoding/base64"
	"fmt"
	"io"
	"testing"

	"github.com/stretchr/testify/require"
)

var base64Header = "data:image/jpeg;base64,"
var base64Content = "/9j/4AAQSkZJRgABAQAAAQABAAD/2wCEABsbGxscGx4hIR4qLSgtKj04MzM4PV1CR0JHQl2NWGdYWGdYjX2Xe3N7l33gsJycsOD/2c7Z//////////////8BGxsbGxwbHiEhHiotKC0qPTgzMzg9XUJHQkdCXY1YZ1hYZ1iNfZd7c3uXfeCwnJyw4P/Zztn////////////////CABEIAEgASAMBIgACEQEDEQH/xAAwAAACAwEBAAAAAAAAAAAAAAAABAIDBQYBAQEBAQEBAAAAAAAAAAAAAAAAAQIDBP/aAAwDAQACEAMQAAAAbVZjy6KNESrZw34dhKFnp4FdircqefucuOOZd2N9BOi7WABK5EVp5vosSWh9Jpdyxa1mwgVKMsg0VbLzNk3Vnd01JStkDWLeSDeZagKTDzeryISgB//EACIQAAICAgEFAQEBAAAAAAAAAAECAAMEESEQEzFBURIicf/aAAgBAQABPwA11WJpxz6MbGXWjZswYdflzHuox6e2o8zEHZ/0mDkdCyr5MV1bwQYKnflTGpt+QUMQduBN1op4DH7P2w/pNATGuJ4Yz1AhOwwESlKySo1uUtrccBx5jUWE+diWvpu2ONSsr4M2EAMQhkB60q4PJmo/8ox+CWMf3uAiKS2uZQNVL1CAdMl9UvHXayttkRdLKuK1E30MJl3KMIUI9QIFfcrYF9biNwBAZvoYRLKA0bEDCV4iLonkwQdc3Pai0IoBmPkpkJtfPsTUZ0X3Dd8E7z/BBf8AREsV/B6ZwcZT/qYVopvUk8SzNVjoNoRWVuQdzcLCGyAmf//EABoRAAMBAQEBAAAAAAAAAAAAAAABERACEiD/2gAIAQIBAT8AKXb9ualcqR7RUcHap0o3lP/EABsRAAIDAAMAAAAAAAAAAAAAAAABAhAREiAw/9oACAEDAQE/AKzyXVRkzhIxjIMVYj//2Q=="

func TestGetBase64StreamEmptyString(t *testing.T) {
	result, err := GetBase64Stream("")

	require.EqualError(t, err, "empty base64 text")
	require.Nil(t, result)
}

func TestGetBase64StreamInvalidString(t *testing.T) {
	result, err := GetBase64Stream("Hello World!")

	require.EqualError(t, err, "failed to decode base64 text: illegal base64 data at input byte 5")
	require.Nil(t, result)
}

func TestGetBase64StreamWithHeaderAndContent(t *testing.T) {
	resultStream, err := GetBase64Stream(fmt.Sprintf("%s%s", base64Header, base64Content))

	require.NoError(t, err)
	require.IsType(t, &bytes.Reader{}, resultStream)

	resultBytes, err := io.ReadAll(resultStream)

	require.NoError(t, err)

	resultString := base64.StdEncoding.EncodeToString(resultBytes)

	require.Equal(t, base64Content, resultString)
}

func TestGetBase64StreamWithContent(t *testing.T) {
	resultStream, err := GetBase64Stream(base64Content)

	require.NoError(t, err)
	require.IsType(t, &bytes.Reader{}, resultStream)

	resultBytes, err := io.ReadAll(resultStream)

	require.NoError(t, err)

	resultString := base64.StdEncoding.EncodeToString(resultBytes)

	require.Equal(t, base64Content, resultString)
}
