syntax = "proto3";

package common;
option go_package = "digisac-go/common/grpc/common;pb";


// The `Service` entity
message Service {
  string id = 1; // Unique identifier for the Service
  string name = 2; // Name of the service
  bool keepOnline = 3; // Whether the service should be kept online
}

// Create operation
message CreateServiceRequest {
  string name = 1; // Name of the service
  bool keepOnline = 2; // Whether the service should be kept online
}

message CreateServiceResponse {
  string id = 1; // ID of the created service
}

// Read operation
message GetServiceRequest {
  string id = 1; // ID of the service to retrieve
}

message GetServiceResponse {
  Service service = 1; // The service details
}

// List operation
message ListServicesRequest {
  int32 page = 1; // Optional pagination: page number
  int32 pageSize = 2; // Optional pagination: number of results per page
}

message ListServicesResponse {
  repeated Service services = 1; // List of services
}

// Update operation
message UpdateServiceRequest {
  string id = 1; // ID of the service to update
  string name = 2; // New name (optional)
  bool keepOnline = 3; // New status for keepOnline (optional)
}

message UpdateServiceResponse {
  Service service = 1; // The updated service details
}

// Delete operation
message DeleteServiceRequest {
  string id = 1; // ID of the service to delete
}

message DeleteServiceResponse {
  bool success = 1; // Whether the deletion was successful
}