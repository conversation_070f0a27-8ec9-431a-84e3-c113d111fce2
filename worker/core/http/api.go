package http

import (
	"context"
	"digisac-go/worker/config"
	"digisac-go/worker/core/http/controllers"
	"digisac-go/worker/core/http/middlewares"
	"errors"
	"fmt"
	"log/slog"
	"sync"
	"time"

	"github.com/gofiber/fiber/v3"
	"github.com/gofiber/fiber/v3/middleware/cors"
	"github.com/gofiber/fiber/v3/middleware/logger"
	"github.com/gofiber/fiber/v3/middleware/recover"
	fiberUtils "github.com/gofiber/utils/v2"
)

type Api struct {
	config             *config.Config
	CancelCtx          context.Context
	CancelFunc         context.CancelFunc
	Ctx                context.Context
	MessageController  *controllers.MessageController
	ServiceController  *controllers.ServiceController
	ContactController  *controllers.ContactController
	TemplateController *controllers.TemplateController
	FileController     *controllers.FileController
}

func NewApi(
	config *config.Config,
	messageController *controllers.MessageController,
	serviceController *controllers.ServiceController,
	contactController *controllers.ContactController,
	TemplateController *controllers.TemplateController,
	FileController *controllers.FileController,
) *Api {
	ctx := context.Background()
	cancelCtx, cancelFunc := context.WithCancel(ctx)
	return &Api{
		config:             config,
		Ctx:                ctx,
		CancelCtx:          cancelCtx,
		CancelFunc:         cancelFunc,
		MessageController:  messageController,
		ServiceController:  serviceController,
		ContactController:  contactController,
		TemplateController: TemplateController,
		FileController:     FileController,
	}
}

func (a *Api) MakeApp() *fiber.App {
	app := fiber.New(fiber.Config{ // @TODO: Adequar esses valores
		ReadTimeout:  60 * time.Second,
		WriteTimeout: 60 * time.Second,
		IdleTimeout:  60 * time.Second,
		Concurrency:  1024 * 1024,
		ErrorHandler: a.ErrorHandler,
	})

	app.Use(recover.New(recover.Config{EnableStackTrace: true}))
	app.Use(cors.New())
	app.Use(logger.New())
	app.Use(middlewares.TracingMiddleware())

	app.Get("/health", func(c fiber.Ctx) error {
		return c.SendStatus(fiber.StatusOK)
	})

	app.Post("/run/start", a.ServiceController.Start)
	app.Post("/run/restart", a.ServiceController.Restart)
	app.Post("/run/shutdown", a.ServiceController.Shutdown)
	app.Post("/run/logout", a.ServiceController.Logout)
	app.Post("/run/takeover", a.ServiceController.Takeover)

	app.Post("/run/sync-templates", a.TemplateController.SyncTemplates)
	app.Post("/run/delete-template", a.TemplateController.DeleteTemplate)
	app.Post("/run/create-template", a.TemplateController.CreateTemplate)

	app.Post("/run/send-vcards", a.MessageController.SendVCards)
	app.Post("/run/send-reaction", a.MessageController.SendReaction)
	app.Post("/run/revoke-reaction", a.MessageController.RevokeReaction)
	app.Post("/run/send-message", a.MessageController.SendMessage)
	app.Post("/run/webhook", a.MessageController.Webhook)
	app.Post("/run/forward-messages", a.MessageController.ForwardMessages)
	app.Post("/run/load-earlier-messages", a.ContactController.LoadEarlierMessages)

	// queued jobs
	app.Post("/run/send-message-to-broker", a.MessageController.SendMessageToBroker)
	app.Post("/run/receive-message-webhook", a.MessageController.ReceiveMessageWebhook)
	app.Post("/run/receive-status-webhook", a.MessageController.ReceiveStatusWebhook)
	app.Post("/run/receive-service-webhook", a.MessageController.ReceiveServiceWebhook)
	app.Post("/run/receive-template-webhook", a.MessageController.ReceiveTemplateWebhook)
	app.Post("/run/receive-media", a.MessageController.ReceiveMedia)
	app.Post("/run/generate-thumbnail", a.FileController.GenerateThumbnail)

	app.Post("/run/:job", func(c fiber.Ctx) error {
		return c.Status(fiber.StatusNotImplemented).SendString("Method not implemented")
	})

	return app
}

func (a *Api) Start(wg *sync.WaitGroup) {
	defer wg.Done()

	app := a.MakeApp()

	err := app.Listen(a.config.ApiAddress, fiber.ListenConfig{
		GracefulContext: a.CancelCtx,
	})

	if err != nil {
		slog.ErrorContext(a.Ctx, fmt.Sprintf("Error starting server on address %s: %v", a.config.ApiAddress, err))
		panic(fmt.Errorf("error starting server on address %s: %w", a.config.ApiAddress, err))
	}

	slog.InfoContext(a.Ctx, "Closed API server", slog.String("address", a.config.ApiAddress))
}

func (a *Api) Stop() {
	a.CancelFunc()
}

func (a *Api) ErrorHandler(c fiber.Ctx, err error) error {
	code := fiber.StatusInternalServerError
	message := fiberUtils.StatusMessage(code)

	var e *fiber.Error
	if errors.As(err, &e) {
		code = e.Code
		message = e.Message
	}

	c.Set(fiber.HeaderContentType, fiber.MIMEApplicationJSONCharsetUTF8)

	body := string(c.Body())

	// if len(body) > 400 {
	// 	body = body[:400] + "...log truncated..."
	// }

	slog.ErrorContext(a.Ctx, fmt.Sprintf("An error occurred during request processing: %v", err),
		slog.String("body", body),
		slog.String("url", c.Request().URI().String()))

	return c.Status(code).JSON(fiber.Map{
		"error":   "HttpError",
		"message": message,
		"status":  code,
	})
}
