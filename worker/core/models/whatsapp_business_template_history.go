package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type WhatsappBusinessTemplateHistory struct {
	Id                       uuid.UUID                          `gorm:"type:uuid;primaryKey" json:"id"`
	AccountId                uuid.UUID                          `gorm:"type:uuid;not null" json:"accountId"`
	Account                  *Account                           `gorm:"foreignKey:AccountId" json:"account,omitempty"`
	WabaTemplateId           uuid.UUID                          `gorm:"type:uuid;not null" json:"wabaTemplateId"`
	WhatsappBusinessTemplate *WhatsappBusinessTemplate          `gorm:"foreignKey:WabaTemplateId;references:Id" json:"whatsappBusinessTemplate,omitempty"`
	StatusFrom               WhatsappBusinessTemplateStatusEnum `gorm:"type:string" json:"statusFrom,omitempty"`
	StatusTo                 WhatsappBusinessTemplateStatusEnum `gorm:"type:string" json:"statusTo,omitempty"`
	QualityFrom              string                             `gorm:"type:string" json:"qualityFrom,omitempty"`
	QualityTo                string                             `gorm:"type:string" json:"qualityTo,omitempty"`
	CreatedAt                *time.Time                         `gorm:"not null" json:"createdAt,omitempty"`
	UpdatedAt                *time.Time                         `json:"updatedAt,omitempty"`
}

func (WhatsappBusinessTemplateHistory) TableName() string {
	return "whatsapp_business_templates_history"
}

func (w *WhatsappBusinessTemplateHistory) BeforeCreate(tx *gorm.DB) (err error) {
	w.Id = uuid.New()
	return
}
