package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

// @jsonb
type ExtraOptions struct {
	Hsm          *WhatsappBusinessTemplate `json:"hsm,omitempty"`
	Parameters   []*HsmParameters          `json:"parameters,omitempty"`
	FileTemplate *File                     `json:"fileTemplate,omitempty"`
}

type CampaignMessage struct {
	Id           uuid.UUID                  `gorm:"type:uuid;primary_key" json:"id,omitempty"`
	Text         string                     `gorm:"type:text" json:"text,omitempty"`
	ExtraOptions *ExtraOptions              `gorm:"column:extraOptions;type:jsonb;serializer:json;default:'{}';" json:"extraOptions,omitempty"`
	CreatedAt    *time.Time                 `json:"createdAt,omitempty"`
	UpdatedAt    *time.Time                 `json:"updatedAt,omitempty"`
	DeletedAt    *gorm.DeletedAt            `gorm:"index" json:"deletedAt,omitempty"`
	AccountId    uuid.UUID                  `gorm:"type:uuid;not null" json:"accountId,omitempty"`
	UserId       uuid.UUID                  `gorm:"type:uuid;not null" json:"userId,omitempty"`
	CampaignId   uuid.UUID                  `gorm:"type:uuid;not null" json:"campaignId,omitempty"`
	HsmId        uuid.UUID                  `gorm:"type:uuid" json:"hsmId,omitempty"`
	HsmFileId    uuid.UUID                  `gorm:"type:uuid" json:"hsmFileId,omitempty"`
	Account      *Account                   `gorm:"foreignKey:AccountId" json:"account,omitempty"`
	User         *User                      `gorm:"foreignKey:UserId" json:"user,omitempty"`
	Campaign     *Campaign                  `gorm:"foreignKey:CampaignId" json:"campaign,omitempty"`
	File         *File                      `gorm:"foreignKey:AttachedId;scope:AttachedType=campaign_message;constraint:false" json:"file,omitempty"`
	HsmFile      *File                      `gorm:"foreignKey:HsmFileId" json:"hsmFile,omitempty"`
	Hsm          *WhatsappBusinessTemplate  `gorm:"foreignKey:HsmId" json:"hsm,omitempty"`
	Progress     []*CampaignMessageProgress `gorm:"foreignKey:CampaignMessageId" json:"progress,omitempty"`
}

func (CampaignMessage) TableName() string {
	return "campaign_messages"
}

func (c *CampaignMessage) BeforeCreate(tx *gorm.DB) (err error) {
	c.Id = uuid.New()
	return
}
