package controllers

import (
	"digisac-go/worker/core/http/middlewares"
	"digisac-go/worker/core/http/payloads"
	fileService "digisac-go/worker/core/services/file"
	httputils "digisac-go/worker/core/utils/http_utils"
	"fmt"
	"log/slog"
	"strings"

	"github.com/gofiber/fiber/v3"
)

type FileController struct {
	fileService *fileService.FileService
}

func NewFileController(fileService *fileService.FileService) *FileController {
	return &FileController{
		fileService: fileService,
	}
}

func (c *FileController) GenerateThumbnail(ctx fiber.Ctx) (err error) {
	payload := new(payloads.GenerateThumbnailRequest)

	if returned, err := httputils.ParseAndValidatePayload(ctx, payload); returned {
		if err != nil {
			slog.ErrorContext(ctx.Context(), "Failed to parse and validate payload", slog.Any("payload", payload), slog.String("error", err.Error()))
		}
		return fmt.Errorf("failed to parse and validate payload: %w", err)
	}

	err = c.fileService.GenerateThumbnail(middlewares.TracingCtx(ctx), payload.Payload.Payload)
	if err != nil {
		slog.ErrorContext(ctx.Context(), "Failed to generate thumbnail", slog.Any("payload", payload.Payload), slog.String("error", err.Error()))
		return c.handleErrors(ctx, err)
	}

	return ctx.SendStatus(200)
}

func (c *FileController) handleErrors(ctx fiber.Ctx, err error) *fiber.Error {
	if strings.Contains(err.Error(), "service disconnected") {
		slog.ErrorContext(ctx.Context(), "Service disconnected", slog.String("error", err.Error()))
		return fiber.NewError(500, "Service disconnected.")
	}

	slog.ErrorContext(ctx.Context(), "Internal server error", slog.String("error", err.Error()))

	return fiber.NewError(fiber.ErrInternalServerError.Code, fmt.Sprintf("Internal server error: %s", err.Error()))
}
