FROM golang:1.24.4-alpine AS builder

WORKDIR /src
COPY . .

RUN cd worker && go mod tidy

RUN CGO_ENABLED=0 GOOS=linux GOARCH=$TARGETARCH \
    go build \
    -ldflags="-s -w -extldflags '-static'" \
    -o /src/app ./worker

FROM gcr.io/distroless/static:nonroot
WORKDIR /src

COPY --from=builder /src/app ./worker
COPY --from=builder /src/common ./common

USER nonroot:nonroot
ENTRYPOINT ["/src/worker"]