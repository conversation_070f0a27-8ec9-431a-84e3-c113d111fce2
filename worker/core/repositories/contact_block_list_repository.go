package repositories

import (
	"digisac-go/worker/core/models"

	"gorm.io/gorm"
)

type ContactBlockListRepository interface {
	Repository[models.ContactBlockList]
}

type contactBlockListRepository struct {
	*GormRepository[models.ContactBlockList]
}

func NewContactBlockListRepository(db *gorm.DB) ContactBlockListRepository {
	return &contactBlockListRepository{
		GormRepository: NewGormRepository[models.ContactBlockList](db),
	}
}
