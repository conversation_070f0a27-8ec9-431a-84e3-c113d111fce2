package repositories

import (
	"digisac-go/worker/core/models"

	"gorm.io/gorm"
)

type ContactBlockListItemRepository interface {
	Repository[models.ContactBlockListItem]
}

type contactBlockListItemRepository struct {
	*GormRepository[models.ContactBlockListItem]
}

func NewContactBlockListItemRepository(db *gorm.DB) ContactBlockListItemRepository {
	return &contactBlockListItemRepository{
		GormRepository: NewGormRepository[models.ContactBlockListItem](db),
	}
}
