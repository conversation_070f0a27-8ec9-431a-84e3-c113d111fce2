package repositories

import (
	"digisac-go/worker/core/models"

	"gorm.io/gorm"
)

type CampaignMessageProgressRepository interface {
	Repository[models.CampaignMessageProgress]
}

type campaignMessageProgressRepository struct {
	*GormRepository[models.CampaignMessageProgress]
}

func NewCampaignMessageProgressRepository(db *gorm.DB) CampaignMessageProgressRepository {
	return &campaignMessageProgressRepository{
		GormRepository: NewGormRepository[models.CampaignMessageProgress](db),
	}
}
