package concurrency

import (
	"context"
	"errors"
	"log/slog"
	"sync"
	"time"

	"github.com/go-redsync/redsync/v4"
)

var ErrTimeoutDistributedLock = errors.New("task timeout")

// DistributedLock é uma interface para implementações de locks distribuídos
type DistributedLock interface {
	// Run executa uma tarefa dentro do lock distribuído
	Run(ctx context.Context, task func() (interface{}, error)) (interface{}, error)
}

// RedisDistributedLock representa um lock distribuído usando Redis
type RedisDistributedLock struct {
	mutex     *redsync.Mutex
	timeout   time.Duration
	taskMutex sync.Mutex
}

// DistributedLockFactory é uma função que cria instâncias de DistributedLock
type DistributedLockFactory func(key string, timeout time.Duration) DistributedLock

// NewDistributedLockFactory returns a function that creates DistributedLock instances
func NewDistributedLockFactory(cachedRedsync *CachedRedsync) DistributedLockFactory {
	return func(key string, timeout time.Duration) DistributedLock {
		if timeout == 0 {
			timeout = 60 * time.Second
		}

		mutex := cachedRedsync.GetMutex(key, timeout)

		return &RedisDistributedLock{
			mutex:   mutex,
			timeout: timeout,
		}
	}
}

// Run executes a task within the RedisDistributedLock
func (q *RedisDistributedLock) Run(ctx context.Context, task func() (interface{}, error)) (result interface{}, err error) {
	// Usar um mutex local para garantir que apenas uma tarefa seja executada por vez
	q.taskMutex.Lock()
	defer q.taskMutex.Unlock()

	// Adquirir o lock distribuído
	if err := q.mutex.LockContext(context.Background()); err != nil {
		return nil, err
	}

	// Garantir que o lock seja liberado ao final
	defer func() {
		_, _ = q.mutex.Unlock()
	}()

	// Criar canais para controle de timeout
	taskChannel := make(chan struct{})
	timeoutChannel := time.After(q.timeout)

	// Executar a tarefa em uma goroutine
	go func() {
		result, err = task()
		close(taskChannel)
	}()

	// Aguardar a conclusão da tarefa ou timeout
	select {
	case <-taskChannel:
		// Tarefa concluída com sucesso
	case <-timeoutChannel:
		// Timeout atingido
		err = ErrTimeoutDistributedLock
		slog.ErrorContext(ctx, "Task timed out", slog.Duration("timeout", q.timeout), slog.String("error", err.Error()))
	}

	return result, err
}
