package services

import (
	"digisac-go/worker/core/services/account"
	answerService "digisac-go/worker/core/services/answer"
	campaignService "digisac-go/worker/core/services/campaign"
	"digisac-go/worker/core/services/contact/contact_service"
	contactBlockListService "digisac-go/worker/core/services/contact_block_list"
	cryptoService "digisac-go/worker/core/services/crypto"
	httpDispatcher "digisac-go/worker/core/services/dispatcher/http"
	queueDispatcher "digisac-go/worker/core/services/dispatcher/queue"
	evaluationService "digisac-go/worker/core/services/evaluation"
	eventService "digisac-go/worker/core/services/event"
	fileService "digisac-go/worker/core/services/file"
	messageForwarder "digisac-go/worker/core/services/message/message_forward"
	"digisac-go/worker/core/services/message/message_link"
	"digisac-go/worker/core/services/message/message_loader"
	"digisac-go/worker/core/services/message/message_receiver"
	"digisac-go/worker/core/services/message/message_sender"
	personService "digisac-go/worker/core/services/person"
	"digisac-go/worker/core/services/service/service_manager"
	stickerService "digisac-go/worker/core/services/sticker"
	storageService "digisac-go/worker/core/services/storage"
	templateService "digisac-go/worker/core/services/template"
	ticketService "digisac-go/worker/core/services/ticket"

	"go.uber.org/fx"
)

var Module = fx.Module("services", fx.Provide(
	queueDispatcher.NewQueueJobsDispatcherService,
	eventService.NewEventService,
	contact_service.NewContactService,
	contactBlockListService.NewContactBlockListService,
	cryptoService.NewAccountCryptor,
	message_loader.NewLoadService,
	messageForwarder.NewForwardService,
	message_sender.NewSendMessageService,
	message_link.NewLinkService,
	message_receiver.NewReceiveService,
	storageService.NewStorageService,
	ticketService.NewTicketService,
	ticketService.NewTicketOpenService,
	ticketService.NewTicketCloseService,
	ticketService.NewTicketTransferService,
	ticketService.NewMetrics,
	evaluationService.NewEvaluationService,
	answerService.NewAnswerService,
	service_manager.NewServiceManagerService,
	stickerService.NewStickerService,
	templateService.NewTemplateManagerService,
	campaignService.NewCampaignService,
	personService.NewPersonService,
	httpDispatcher.NewHttpJobsDispatcherService,
	account.NewAccountUsedHsmService,
	fileService.NewFileService,
))
