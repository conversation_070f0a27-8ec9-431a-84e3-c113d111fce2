package common

import (
	"context"
	"crypto/sha1"
	"encoding/hex"
	"fmt"
	"io"
	"log/slog"
)

// O valor abaixo representa o SHA-1 de um conteúdo vazio
var emptyReaderChecksum = "da39a3ee5e6b4b0d3255bfef95601890afd80709"

// GetChecksum calculates the SHA-1 checksum of data from an io.Reader
//
// Parameters:
//   - ctx: The context of origin
//   - reader: The io.Reader to calculate the checksum from
//
// Returns:
//   - string: The hex-encoded SHA-1 checksum
//   - error: Any error that occurred during calculation
func GetChecksum(ctx context.Context, reader io.Reader) (string, error) {
	// Tenta realizar o seek para o início, se suportado
	// Garantir que o reader esteja sempre no início
	seeker, ok := reader.(io.Seeker)
	if ok {
		_, err := seeker.Seek(0, io.SeekStart)
		if err != nil {
			slog.WarnContext(ctx, "Failed to seek the reader", slog.Any("error", err))
		}
	}

	hasher := sha1.New()

	_, err := io.Copy(hasher, reader)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to copy from reader to hasher", slog.Any("error", err))
		return "", fmt.Errorf("failed to copy from reader to hasher: %w", err)
	}

	result := hex.EncodeToString(hasher.Sum(nil))

	if result == emptyReaderChecksum {
		slog.WarnContext(ctx, "The checksum result is from a empty reader")
	}

	return result, nil
}
