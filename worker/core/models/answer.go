package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Answer struct {
	Id         uuid.UUID       `gorm:"type:uuid;primary_key" json:"id,omitempty"`
	Text       string          `gorm:"type:text" json:"text,omitempty"`
	Reason     string          `gorm:"type:string" json:"reason,omitempty"`
	TicketId   uuid.UUID       `gorm:"type:uuid;default:null" json:"ticketId,omitempty"`
	Ticket     *Ticket         `gorm:"foreignKey:TicketId" json:"ticket,omitempty"`
	QuestionId uuid.UUID       `gorm:"type:uuid;default:null" json:"questionId,omitempty"`
	Question   *Question       `gorm:"foreignKey:QuestionId" json:"question,omitempty"`
	CreatedAt  *time.Time      `json:"createdAt,omitempty"`
	UpdatedAt  *time.Time      `json:"updatedAt,omitempty"`
	DeletedAt  *gorm.DeletedAt `gorm:"index" json:"deletedAt,omitempty"`
}

func (Answer) TableName() string {
	return "answers"
}

func (a *Answer) BeforeCreate(tx *gorm.DB) (err error) {
	a.Id = uuid.New()
	return
}
