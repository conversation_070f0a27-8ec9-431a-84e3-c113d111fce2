package payloads

import "github.com/google/uuid"

type GenerateThumbnailPayloadPayload struct {
	FileId    uuid.UUID `json:"fileId" validate:"required"`
	AccountId uuid.UUID `json:"accountId" validate:"required"`
}

type GenerateThumbnailPayload struct {
	Payload  *GenerateThumbnailPayloadPayload `json:"payload" validate:"required"`
	Metadata *Metadata                        `json:"metadata"`
}

type GenerateThumbnailRequest struct {
	Payload *GenerateThumbnailPayload `json:"payload" validate:"required"`
}
