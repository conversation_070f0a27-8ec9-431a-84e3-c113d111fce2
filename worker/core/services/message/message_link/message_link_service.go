package message_link

import (
	"context"
	"fmt"
	"log/slog"

	"digisac-go/worker/core/models"
	"digisac-go/worker/core/repositories"
	urlUtils "digisac-go/worker/core/utils/url_utils"

	"github.com/google/uuid"
	gormrepository "github.com/ikateclab/gorm-repository"
)

type LinkService struct {
	linkRepository repositories.LinkRepository
}

func NewLinkService(
	linkRepository repositories.LinkRepository,
) *LinkService {
	return &LinkService{
		linkRepository: linkRepository,
	}
}

// ProcessMessageLinks extracts URLs from a message and creates link records
func (s *LinkService) ProcessMessageLinks(ctx context.Context, messageId uuid.UUID, text string, tx *gormrepository.Tx) error {
	if text == "" {
		return nil
	}

	// Extract URLs from the message text
	urls := urlUtils.ExtractURLs(text)
	if len(urls) == 0 {
		return nil
	}

	slog.InfoContext(ctx, "Found URLs in message", slog.String("messageId", messageId.String()), slog.Int("urlCount", len(urls)))

	// Create a link record for each URL
	for range urls {
		link := &models.Link{
			MessageId: messageId,
		}

		err := s.linkRepository.Create(ctx, link, repositories.WithTx(tx))

		if err != nil {
			slog.ErrorContext(ctx, "Failed to create link for URL", slog.String("messageId", messageId.String()), slog.String("error", err.Error()))
			return fmt.Errorf("failed to create link for URL: %w", err)
		}
	}

	return nil
}
