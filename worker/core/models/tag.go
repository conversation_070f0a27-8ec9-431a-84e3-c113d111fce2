package models

import (
	"time"

	"github.com/google/uuid"

	"gorm.io/gorm"
)

type Tag struct {
	Id              uuid.UUID       `gorm:"type:uuid;primary_key not null" json:"id,omitempty"`
	Label           string          `gorm:"type:text;not null" json:"label,omitempty"`
	BackgroundColor string          `gorm:"type:string" json:"backgroundColor,omitempty"`
	AccountId       uuid.UUID       `gorm:"type:uuid;not null" json:"accountId,omitempty"`
	Account         *Account        `gorm:"foreignKey:AccountId" json:"account,omitempty"`
	Contacts        []*Contact      `gorm:"many2many:contact_tags;foreignKey:Id;joinForeignKey:TagId;References:Id;joinReferences:ContactId" json:"contacts,omitempty"`
	CreatedAt       *time.Time      `json:"createdAt,omitempty"`
	UpdatedAt       *time.Time      `json:"updatedAt,omitempty"`
	DeletedAt       *gorm.DeletedAt `gorm:"index" json:"deletedAt,omitempty"`
}

func (Tag) TableName() string {
	return "tags"
}

func (t *Tag) BeforeCreate(tx *gorm.DB) (err error) {
	t.Id = uuid.New()
	return
}
