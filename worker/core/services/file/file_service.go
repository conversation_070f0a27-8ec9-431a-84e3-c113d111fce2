package files

import (
	"bytes"
	"context"
	"digisac-go/worker/core/http/payloads"
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/repositories"
	queueDispatcher "digisac-go/worker/core/services/dispatcher/queue"
	"digisac-go/worker/core/services/storage"
	"digisac-go/worker/core/utils/common"
	utilsConfig "digisac-go/worker/core/utils/config"
	streamUtils "digisac-go/worker/core/utils/stream_utils"
	"errors"
	"fmt"
	"image"
	"image/png"
	"io"
	"log/slog"
	"strings"

	"github.com/disintegration/imaging"
	"github.com/google/uuid"
	gormrepository "github.com/ikateclab/gorm-repository"
)

type CreateFilePayload struct {
	Name         string
	AttachedId   uuid.UUID
	AttachedType string
	Mimetype     string
	AccountId    uuid.UUID
	Stream       io.Reader
}

type CreatePreviewFilePayload struct {
	AttachedId uuid.UUID
	AccountId  uuid.UUID
	Mimetype   string
	Base64     string
}

type FileService struct {
	fileRepository  repositories.FileRepository
	storageService  *storage.StorageService
	queueDispatcher *queueDispatcher.QueueJobsDispatcherService
}

func NewFileService(
	fileRepository repositories.FileRepository,
	storageService *storage.StorageService,
	queueDispatcher *queueDispatcher.QueueJobsDispatcherService,
) *FileService {
	return &FileService{
		fileRepository:  fileRepository,
		storageService:  storageService,
		queueDispatcher: queueDispatcher,
	}
}

func resizeImageStream(ctx context.Context, r io.Reader, width, height int) (io.Reader, int64, error) {
	img, _, err := image.Decode(r)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to decode image", "error", err)
		return nil, 0, fmt.Errorf("failed to decode image: %w", err)
	}

	thumb := imaging.Fit(img, width, height, imaging.Lanczos)
	buf := new(bytes.Buffer)

	// Always encode as PNG with best compression
	imgFormat := imaging.PNG
	encodeOpts := []imaging.EncodeOption{
		imaging.PNGCompressionLevel(png.BestCompression),
	}

	if err := imaging.Encode(buf, thumb, imgFormat, encodeOpts...); err != nil {
		slog.ErrorContext(ctx, "Failed to encode thumbnail",
			slog.String("error", err.Error()),
		)
		return nil, 0, fmt.Errorf("failed to encode thumbnail: %w", err)
	}
	slog.DebugContext(ctx, "Thumbnail generated successfully",
		slog.Any("format", imgFormat),
		slog.Int("size", buf.Len()),
	)
	return buf, int64(buf.Len()), nil
}

func (s *FileService) GenerateThumbnail(ctx context.Context, data *payloads.GenerateThumbnailPayloadPayload) error {
	file, err := s.fileRepository.FindById(ctx, data.FileId)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to find file",
			slog.Any("fileId", data.FileId),
			slog.String("error", err.Error()),
		)
		return fmt.Errorf("failed to find file (id=%v): %w", data.FileId, err)
	}
	if !strings.Contains(file.Mimetype, "image") {
		err = errors.New("mimetype not allowed to generate image thumbanil")
		slog.ErrorContext(ctx, "Failed to generate thumbnail",
			slog.String("fileId", file.Id.String()),
			slog.String("mimetype", file.Mimetype),
			slog.String("error", err.Error()),
		)
		return fmt.Errorf("invalid mimetype: %w", err)
	}

	fileStream, err := s.storageService.GetStream(ctx, file)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to get file stream",
			slog.String("fileId", file.Id.String()),
			slog.String("error", err.Error()),
		)
		return fmt.Errorf("failed to get file stream (id=%v): %w", file.Id, err)
	}

	thumbnailStream, _, err := resizeImageStream(ctx, fileStream, 400, 400)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to generate thumbnail",
			slog.String("fileId", file.Id.String()),
			slog.String("error", err.Error()),
		)
		return fmt.Errorf("failed to generate thumbnail (id=%v): %w", file.Id, err)
	}

	thumbBytes, err := io.ReadAll(thumbnailStream)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to read thumbnail buffer",
			slog.String("fileId", file.Id.String()),
			slog.String("error", err.Error()),
		)
		return fmt.Errorf("failed to read thumbnail buffer (id=%v): %w", file.Id, err)
	}

	checksum, err := common.GetChecksum(ctx, bytes.NewReader(thumbBytes))
	if err != nil {
		slog.ErrorContext(ctx, "Failed to get checksum for thumbnail",
			slog.String("fileId", file.Id.String()),
			slog.String("error", err.Error()),
		)
		return fmt.Errorf("failed to get checksum for thumbnail (id=%v): %w", file.Id, err)
	}

	thumbnailFile := &models.File{
		AttachedId:   file.AttachedId,
		AccountId:    file.AccountId,
		Name:         strings.Split(file.Name, ".")[0] + ".png", // Sempre PNG
		Mimetype:     "image/png",                               // Sempre PNG
		Extension:    "png",                                     // Sempre PNG
		Storage:      file.Storage,
		AttachedType: "message.thumbnail",
		Checksum:     checksum,
	}

	if err := s.fileRepository.Create(ctx, thumbnailFile); err != nil {
		slog.ErrorContext(ctx, "Failed to create thumbnail record",
			slog.String("fileId", file.Id.String()),
			slog.String("error", err.Error()),
		)
		return fmt.Errorf("failed to create thumbnail record (fileId=%v): %w", file.Id, err)
	}

	if err := s.storageService.WriteStream(ctx, thumbnailFile, bytes.NewReader(thumbBytes), &storage.WriteStreamOptions{
		ContentLength: int64(len(thumbBytes)),
		ContentType:   "image/png",
	}); err != nil {
		slog.ErrorContext(ctx, "Failed to save thumbnail to storage",
			slog.String("thumbnailId", thumbnailFile.Id.String()),
			slog.String("error", err.Error()),
		)
		return fmt.Errorf("failed to save thumbnail to storage (thumbnailId=%v): %w", thumbnailFile.Id, err)
	}

	slog.DebugContext(ctx, "Thumbnail successfully saved",
		slog.String("thumbnailId", thumbnailFile.Id.String()),
		slog.Int("size", len(thumbBytes)),
	)
	return nil
}

func (s *FileService) CreateFile(ctx context.Context, createFilePayload *CreateFilePayload, tx *gormrepository.Tx) (*models.File, error) {
	size, bufferedStream, err := streamUtils.GetStreamSizeBuffered(createFilePayload.Stream)

	if err != nil {
		slog.ErrorContext(ctx, "Failed to get stream size buffered", slog.String("error", err.Error()), slog.Any("createFilePayload", createFilePayload))
		return nil, fmt.Errorf("failed to get stream size buffered: %w", err)
	}

	checksum, err := common.GetChecksum(ctx, createFilePayload.Stream)

	if err != nil {
		slog.ErrorContext(ctx, "Failed to get checksum", slog.String("error", err.Error()), slog.Any("createFilePayload", createFilePayload))
		return nil, fmt.Errorf("failed to get checksum: %w", err)
	}

	extension := strings.Split(strings.Split(createFilePayload.Mimetype, "/")[1], ";")[0]

	name := createFilePayload.Name
	if name == "" {
		mimetypeSplitted := strings.Split(createFilePayload.Mimetype, "/")

		if mimetypeSplitted[0] == "audio" {
			name = "recording." + extension
		} else {
			name = mimetypeSplitted[0] + "." + extension
		}
	}

	file := &models.File{
		Name:         name,
		AttachedType: createFilePayload.AttachedType,
		AttachedId:   createFilePayload.AttachedId,
		Mimetype:     createFilePayload.Mimetype,
		Extension:    extension,
		AccountId:    createFilePayload.AccountId,
		Storage:      models.StorageType(utilsConfig.GetRandomBuckets()),
		Checksum:     checksum,
	}

	var options repositories.Option
	if tx != nil {
		options = repositories.WithTx(tx)
	}

	err = s.fileRepository.Create(ctx, file, options)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to create file record", slog.String("fileId", file.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to create file record (fileId=%v): %w", file.Id, err)
	}

	err = s.storageService.WriteStream(ctx, file, bufferedStream, &storage.WriteStreamOptions{
		ContentLength: size,
		ContentType:   file.Mimetype,
	})

	if err != nil {
		slog.ErrorContext(ctx, "Failed to write stream", slog.String("error", err.Error()), slog.Any("createFilePayload", createFilePayload))
		return nil, fmt.Errorf("failed to write stream: %w", err)
	}

	if strings.Contains(file.Mimetype, "image") && file.AttachedType == "message.file" && size > 200_000 {
		err = s.queueDispatcher.Dispatch(ctx, string(queueDispatcher.GenerateThumbnailJobName), &payloads.GenerateThumbnailPayloadPayload{
			FileId:    file.Id,
			AccountId: file.AccountId,
		}, nil)

		if err != nil {
			slog.ErrorContext(ctx, "Failed to dispatch generate thumbnail", slog.String("error", err.Error()), slog.Any("createFilePayload", createFilePayload))
			return nil, fmt.Errorf("failed to dispatch generate thumbnail: %w", err)
		}
	}

	if err != nil {
		slog.ErrorContext(ctx, "Failed to dispatch generate thumbnail", slog.String("error", err.Error()), slog.Any("createFilePayload", createFilePayload))
		return nil, fmt.Errorf("failed to dispatch generate thumbnail: %w", err)
	}

	return file, nil
}

func (s *FileService) CreatePreviewFile(ctx context.Context, createPreviewFilePayload *CreatePreviewFilePayload) (*models.File, error) {
	file, _ := s.fileRepository.FindOne(ctx, repositories.WithQueryStruct(map[string]interface{}{
		"attachedId":   createPreviewFilePayload.AttachedId,
		"attachedType": "message.preview",
		"accountId":    createPreviewFilePayload.AccountId,
	}))
	if file != nil {
		return file, nil
	}

	stream, err := streamUtils.GetBase64Stream(createPreviewFilePayload.Base64)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to get base64 stream", slog.String("error", err.Error()), slog.Any("createPreviewFilePayload", createPreviewFilePayload))
		return nil, fmt.Errorf("failed to get base64 stream: %w", err)
	}

	filePreview, err := s.CreateFile(ctx, &CreateFilePayload{
		AttachedId:   createPreviewFilePayload.AttachedId,
		AttachedType: "message.preview",
		Mimetype:     createPreviewFilePayload.Mimetype,
		AccountId:    createPreviewFilePayload.AccountId,
		Stream:       stream,
	}, nil)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to create file preview", slog.String("error", err.Error()), slog.Any("createPreviewFilePayload", createPreviewFilePayload), slog.Any("filePreview", filePreview))
		return nil, fmt.Errorf("failed to create file preview: %w", err)
	}

	return filePreview, nil
}
