package ticket

import (
	"context"
	"errors"
	"fmt"
	"log/slog"
	"strings"
	"time"

	"digisac-go/worker/core/models"
	"digisac-go/worker/core/repositories"
	answer "digisac-go/worker/core/services/answer"
	"digisac-go/worker/core/services/event"
	"digisac-go/worker/core/utils/common"
	"digisac-go/worker/core/utils/concurrency"

	"github.com/go-playground/validator/v10"
	"github.com/google/uuid"
	gormrepository "github.com/ikateclab/gorm-repository"
	"github.com/redis/go-redis/v9"
	"gorm.io/gorm"
)

func NewTicketOpenService(
	redisClient *redis.Client,
	validator *validator.Validate,
	distributedLockFactory concurrency.DistributedLockFactory,
	accountRepository repositories.AccountRepository,
	ticketRepository repositories.TicketRepository,
	messageRepository repositories.MessageRepository,
	userRepository repositories.UserRepository,
	ticketTransferRepository repositories.TicketTransferRepository,
	contactRepository repositories.ContactRepository,
	answerService *answer.AnswerService,
	eventService *event.EventService,
) *TicketOpenService {
	return &TicketOpenService{
		redisClient:              redisClient,
		distributedLockFactory:   distributedLockFactory,
		validator:                validator,
		accountRepository:        accountRepository,
		ticketRepository:         ticketRepository,
		messageRepository:        messageRepository,
		userRepository:           userRepository,
		ticketTransferRepository: ticketTransferRepository,
		contactRepository:        contactRepository,
		answerService:            answerService,
		eventService:             eventService,
	}
}

func (s *TicketOpenService) GetProtocolFormat(format string) string {
	if format == "" {
		format = "{{date}}{{count}}"
	}
	if !strings.Contains(format, "{{count}}") {
		return format + "{{count}}"
	}
	return format
}

func (s *TicketOpenService) GenerateProtocolAndCount(ctx context.Context, account *models.Account, tx *gormrepository.Tx) (protocol string, count int, err error) {
	// YYYYMMDD
	date := time.Now().Format("********")

	key := "tickets-protocol-count:" + account.Id.String()

	count, err = s.redisClient.Get(ctx, key).Int()

	if err != nil && err != redis.Nil {
		slog.ErrorContext(ctx, "Failed to get count from redis", slog.String("key", key), slog.String("error", err.Error()))
		return "", 0, fmt.Errorf("failed to get count from redis: %w", err)
	}

	if count == 0 {

		queue := s.distributedLockFactory("generate-protocol-count-queue:"+account.Id.String(), 2*time.Minute)

		_, err = queue.Run(ctx, func() (interface{}, error) {

			if s.redisClient.Exists(ctx, key).Val() != 0 {
				return nil, nil
			}

			maxCount, err := s.ticketRepository.Max(ctx, "count", repositories.WithQueryStruct(map[string]interface{}{
				"accountId": account.Id,
			}))

			if err != nil {
				slog.ErrorContext(ctx, "Failed to get max count from database", slog.String("accountId", account.Id.String()), slog.String("error", err.Error()))
				return nil, fmt.Errorf("failed to get max count from database: %w", err)
			}

			err = s.redisClient.Set(ctx, key, maxCount, 2*time.Minute).Err()
			if err != nil {
				slog.ErrorContext(ctx, "Failed to set max count to redis", slog.String("key", key), slog.Int("maxCount", maxCount), slog.String("error", err.Error()))
				return nil, fmt.Errorf("failed to set max count to redis: %w", err)
			}

			return nil, nil
		})

		if err != nil {
			slog.ErrorContext(ctx, "Failed to run distributed lock for generating protocol count", slog.String("accountId", account.Id.String()), slog.String("error", err.Error()))
			return "", 0, fmt.Errorf("failed to run distributed lock for generating protocol count: %w", err)
		}
	}

	incrementedCount, err := s.redisClient.Incr(ctx, key).Result()

	if err != nil {
		slog.ErrorContext(ctx, "Failed to increment count in redis", slog.String("key", key), slog.String("error", err.Error()))
		return "", 0, fmt.Errorf("failed to increment count in redis: %w", err)
	}

	count = int(incrementedCount)

	protocol = common.Interpolate(s.GetProtocolFormat(account.Settings.ProtocolFormat), map[string]interface{}{
		"date":  date,
		"count": count,
	})

	return protocol, count, nil
}

func (s *TicketOpenService) OpenTicket(ctx context.Context, data *OpenData, tx *gormrepository.Tx, etx *event.Etx) (*OpenTicketResult, error) {
	err := s.validator.Struct(data)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to validate open ticket data", slog.Any("data", data), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to validate open ticket data: %w", err)
	}

	var result *OpenTicketResult

	contact := data.Contact
	message := data.Message
	account := contact.Account

	now := time.Now()

	if data.StartedAt == nil || data.StartedAt.IsZero() {
		data.StartedAt = &now
	}

	etx2 := s.eventService.BeginTransaction(etx)

	if tx == nil {
		tx = s.ticketRepository.BeginTransaction()
		defer tx.Finish(&err)
	}
	if account == nil {
		account, err = s.accountRepository.FindById(ctx, contact.AccountId, repositories.WithTx(tx))
		slog.Warn("contact came without account, had to fetch.")
		if err != nil {
			slog.ErrorContext(ctx, "Failed to find account by id", slog.String("accountId", contact.AccountId.String()), slog.String("error", err.Error()))
			return nil, fmt.Errorf("failed to find account by id: %w", err)
		}
	}

	if contact.Data.Survey != nil {
		err = s.answerService.UnflagSurveyFromContact(ctx, contact, tx, etx2)

		if err != nil {
			slog.ErrorContext(ctx, "Failed to unflag survey from contact", slog.String("contactId", contact.Id.String()), slog.String("error", err.Error()))
			return nil, fmt.Errorf("failed to unflag survey from contact: %w", err)
		}
	}

	// Verifica se o usuário ainda está vinculado ao departamento, se não estiver, transfere para a fila do departamento selecionado
	if data.UserId != uuid.Nil {
		_, err := s.userRepository.FindById(ctx,
			data.UserId,
			repositories.WithQuery(func(db *gorm.DB) *gorm.DB {
				return db.Joins(`JOIN user_departments ON users.id = user_departments."userId"`).
					Where(`user_departments."departmentId" = ?`, data.DepartmentId)
			}))

		if err != nil {
			if errors.Is(err, gorm.ErrRecordNotFound) {
				// Usuário não pertence ao departamento
				data.UserId = uuid.Nil
			} else {
				slog.ErrorContext(ctx, "Failed to check if user belongs to department", slog.String("userId", data.UserId.String()), slog.String("departmentId", data.DepartmentId.String()), slog.String("error", err.Error()))
				return nil, fmt.Errorf("failed to check if user belongs to department: %w", err)
			}
		}
	}

	ticket, err := s.createTicket(ctx, account, data, contact, message, tx, etx2)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to create ticket", slog.Any("data", data), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to create ticket: %w", err)
	}

	ticketOpenMessage, err := s.createTicketOpenMessage(ctx, contact, message, ticket, tx, etx2)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to create ticket open message", slog.String("ticketId", ticket.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to create ticket open message: %w", err)
	}
	ticketTransfer, err := s.createTicketTransfer(ctx, contact, data, ticketOpenMessage, ticket, message, tx)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to create ticket transfer", slog.String("ticketId", ticket.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to create ticket transfer: %w", err)
	}

	if message != nil {
		message.TicketId = ticket.Id
		message.TicketUserId = ticket.UserId
		message.TicketDepartmentId = ticket.DepartmentId
		err := s.messageRepository.UpdateById(ctx, message.Id, message, repositories.WithTx(tx))

		if err != nil {
			slog.ErrorContext(ctx, "Failed to update message", slog.String("messageId", message.Id.String()), slog.String("error", err.Error()))
			return nil, fmt.Errorf("failed to update message: %w", err)
		}

		err = s.eventService.Dispatch(
			ctx,
			event.EventMessageUpdated,
			&event.DispatchPayload{
				Id:        message.Id,
				AccountId: message.AccountId,
			},
			&event.EventServiceDispatchOptions{
				DebounceKey: message.Id.String(),
				Etx:         etx2,
			})

		if err != nil {
			slog.ErrorContext(ctx, "Failed to dispatch message updated event", slog.String("error", err.Error()), slog.Any("message", message))
			return nil, fmt.Errorf("failed to dispatch message updated event: %w", err)
		}
	}

	ticket.CurrentTicketTransferId = ticketTransfer.Id
	err = s.ticketRepository.UpdateById(ctx, ticket.Id, ticket, repositories.WithTx(tx))
	if err != nil {
		slog.ErrorContext(ctx, "Failed to update ticket", slog.String("ticketId", ticket.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to update ticket: %w", err)
	}

	err = s.eventService.Dispatch(
		ctx,
		event.EventTicketUpdated,
		&event.DispatchPayload{
			Id:        ticket.Id,
			AccountId: ticket.AccountId,
		},
		&event.EventServiceDispatchOptions{
			DebounceKey: ticket.Id.String(),
			Etx:         etx2,
		})

	if err != nil {
		slog.ErrorContext(ctx, "Failed to dispatch ticket updated event", slog.String("error", err.Error()), slog.Any("ticket", ticket))
		return nil, fmt.Errorf("failed to dispatch ticket updated event: %w", err)
	}

	contact.CurrentTicketId = ticket.Id

	if data.ByUserId != uuid.Nil {
		contact.Data.BotIsRunning = false
	}

	err = s.contactRepository.UpdateById(ctx, contact.Id, contact, repositories.WithTx(tx))

	if err != nil {
		slog.ErrorContext(ctx, "Failed to update contact", slog.String("contactId", contact.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to update contact: %w", err)
	}

	err = s.eventService.Dispatch(
		ctx,
		event.EventContactUpdated,
		&event.DispatchPayload{
			Id:        contact.Id,
			AccountId: contact.AccountId,
		},
		&event.EventServiceDispatchOptions{
			DebounceKey: contact.Id.String(),
			Etx:         etx2,
		})

	if err != nil {
		slog.ErrorContext(ctx, "Failed to dispatch contact updated event", slog.String("error", err.Error()), slog.Any("contact", contact))
		return nil, fmt.Errorf("failed to dispatch contact updated event: %w", err)
	}

	err = s.eventService.Dispatch(
		ctx,
		event.EventTicketOpened,
		&event.DispatchPayload{
			Id:        ticket.Id,
			AccountId: ticket.AccountId,
		},
		&event.EventServiceDispatchOptions{
			DebounceKey: ticket.Id.String(),
			Etx:         etx,
		})

	if err != nil {
		slog.ErrorContext(ctx, "Ticket open failed on dispatch ticket open event", slog.String("error", err.Error()), slog.Any("data", data))
		return nil, fmt.Errorf("ticket open failed on dispatch ticket open event: %w", err)
	}

	// Não comita se for etx aninhada
	if etx == nil {
		err = etx2.Commit()
		if err != nil {
			slog.ErrorContext(ctx, "Ticket open failed to commit event transaction", slog.String("error", err.Error()))
			return nil, fmt.Errorf("ticket open failed to commit event transaction: %w", err)
		}
	}

	result = &OpenTicketResult{
		Message:           message,
		Ticket:            ticket,
		TicketOpenMessage: ticketOpenMessage,
		UpdatedContact:    contact,
		TicketTransfer:    ticketTransfer,
	}

	return result, nil
}

func (s *TicketOpenService) createTicketTransfer(ctx context.Context, contact *models.Contact, data *OpenData, ticketOpenMessage *models.Message, ticket *models.Ticket, message *models.Message, tx *gormrepository.Tx) (*models.TicketTransfer, error) {
	ticketTransfer := &models.TicketTransfer{
		Action:               "opened",
		AccountId:            contact.AccountId,
		ToDepartmentId:       data.DepartmentId,
		ToUserId:             data.UserId,
		Comments:             data.Comments,
		TransferredMessageId: ticketOpenMessage.Id,
		TicketId:             ticket.Id,
		ByUserId:             data.ByUserId,
		StartedAt:            data.StartedAt,
		FirstMessageId:       message.Id,
		LastMessageId:        message.Id,
	}
	err := s.ticketTransferRepository.Create(ctx, ticketTransfer, repositories.WithTx(tx))

	if err != nil {
		slog.ErrorContext(ctx, "Failed to create ticket transfer", slog.String("ticketId", ticket.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to create ticket transfer: %w", err)
	}

	return ticketTransfer, nil
}

func (s *TicketOpenService) createTicket(ctx context.Context, account *models.Account, data *OpenData, contact *models.Contact, message *models.Message, tx *gormrepository.Tx, etx *event.Etx) (*models.Ticket, error) {
	protocol, count, err := s.GenerateProtocolAndCount(ctx, account, tx)

	if err != nil {
		slog.ErrorContext(ctx, "Failed to generate protocol and count", slog.String("accountId", account.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to generate protocol and count: %w", err)
	}

	ticket := &models.Ticket{
		IsOpen:         true,
		StartedAt:      data.StartedAt,
		DepartmentId:   data.DepartmentId,
		UserId:         data.UserId,
		Protocol:       protocol,
		Count:          count,
		Origin:         data.Origin,
		ContactId:      contact.Id,
		AccountId:      contact.AccountId,
		FirstMessageId: message.Id,
		LastMessageId:  message.Id,
	}
	err = s.ticketRepository.Create(ctx, ticket, repositories.WithTx(tx))

	if err != nil {
		if errors.Is(err, gorm.ErrRecordNotFound) {
			slog.Warn("Trying again create ticket...")
			return s.createTicket(ctx, account, data, contact, message, tx, etx)
		}
		slog.ErrorContext(ctx, "Failed to create ticket", slog.Any("data", data), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to create ticket: %w", err)
	}

	err = s.eventService.Dispatch(
		ctx,
		event.EventTicketCreated,
		&event.DispatchPayload{
			Id:        ticket.Id,
			AccountId: ticket.AccountId,
		},
		&event.EventServiceDispatchOptions{
			Etx: etx,
		})

	if err != nil {
		slog.ErrorContext(ctx, "Failed to dispatch ticket created event", slog.String("error", err.Error()), slog.Any("ticket", ticket))
		return nil, fmt.Errorf("failed to dispatch ticket created event: %w", err)
	}

	return ticket, nil
}

func (s *TicketOpenService) createTicketOpenMessage(ctx context.Context, contact *models.Contact, message *models.Message, ticket *models.Ticket, tx *gormrepository.Tx, etx *event.Etx) (*models.Message, error) {
	timestamp := ticket.StartedAt

	if message.Timestamp != nil && !message.Timestamp.IsZero() {
		timestamp = message.Timestamp

		milliseconds := time.Duration(-1) * time.Millisecond

		*timestamp = timestamp.Add(milliseconds)
	}

	ticketOpenMessage := &models.Message{
		Timestamp: timestamp,
		TicketId:  ticket.Id,
		ContactId: contact.Id,
		ServiceId: contact.ServiceId,
		AccountId: contact.AccountId,
		Origin:    "ticket",
		Type:      "ticket",
		Data: &models.MessageData{
			TicketOpen: true,
		},
	}
	err := s.messageRepository.Create(ctx, ticketOpenMessage, repositories.WithTx(tx))

	if err != nil {
		slog.ErrorContext(ctx, "Failed to create ticket open message", slog.String("ticketId", ticket.Id.String()), slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to create ticket open message: %w", err)
	}

	err = s.eventService.Dispatch(
		ctx,
		event.EventMessageCreated,
		&event.DispatchPayload{
			Id:        ticketOpenMessage.Id,
			AccountId: ticketOpenMessage.AccountId,
		},
		&event.EventServiceDispatchOptions{
			Etx: etx,
		})

	if err != nil {
		slog.ErrorContext(ctx, "Failed to dispatch ticket open message created event", slog.String("error", err.Error()), slog.Any("ticketOpenMessage", ticketOpenMessage))
		return nil, fmt.Errorf("failed to dispatch ticket open message created event: %w", err)
	}

	return ticketOpenMessage, nil
}
