//go:build unit

package models

import (
	"fmt"
	"reflect"
	"regexp"
	"strings"
	"testing"

	"github.com/DATA-DOG/go-sqlmock"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"gorm.io/driver/postgres"
	"gorm.io/gorm"
)

func TestTableName(t *testing.T) {
	testCases := []struct {
		model    interface{ TableName() string }
		expected string
	}{
		{&Account{}, "accounts"},
		{&Answer{}, "answers"},
		{&Campaign{}, "campaigns"},
		{&CampaignMessage{}, "campaign_messages"},
		{&CampaignMessageProgress{}, "campaign_messages_progress"},
		{&Contact{}, "contacts"},
		{&ContactBlockList{}, "contact_block_lists"},
		{&ContactBlockListControl{}, "contact_block_lists_controls"},
		{&ContactBlockListItem{}, "contact_block_list_items"},
		{&Department{}, "departments"},
		{&File{}, "files"},
		{&<PERSON>{}, "links"},
		{&Message{}, "messages"},
		{&Organization{}, "organizations"},
		{&Person{}, "people"},
		{&Question{}, "questions"},
		{&Service{}, "services"},
		{&Sticker{}, "stickers"},
		{&StickerUser{}, "sticker_users"},
		{&Tag{}, "tags"},
		{&Ticket{}, "tickets"},
		{&TicketTopic{}, "ticket_topics"},
		{&TicketTransfer{}, "ticket_transfers"},
		{&User{}, "users"},
		{&WhatsappBusinessTemplate{}, "whatsapp_business_templates"},
		{&WhatsappBusinessTemplateHistory{}, "whatsapp_business_templates_history"},
	}

	for _, tc := range testCases {
		modelType := reflect.TypeOf(tc.model).Elem().Name()
		t.Run(fmt.Sprintf("Model_%s", modelType), func(t *testing.T) {
			if got := tc.model.TableName(); got != tc.expected {
				t.Errorf("for model %s, expected table name to be '%s', got '%s'", modelType, tc.expected, got)
			}
		})
	}
}

func TestBeforeCreate(t *testing.T) {
	testCases := []interface{ BeforeCreate(*gorm.DB) error }{
		&Account{},
		&Answer{},
		&Campaign{},
		&CampaignMessage{},
		&CampaignMessageProgress{},
		&Contact{},
		&ContactBlockList{},
		&ContactBlockListControl{},
		&ContactBlockListItem{},
		&Department{},
		&File{},
		&Link{},
		&Message{},
		&Organization{},
		&Person{},
		&Question{},
		&Service{},
		&Sticker{},
		&Tag{},
		&Ticket{},
		&TicketTopic{},
		&TicketTransfer{},
		&User{},
		&WhatsappBusinessTemplate{},
		&WhatsappBusinessTemplateHistory{},
	}

	for _, model := range testCases {
		// Get the type and value of the model pointer
		modelValue := reflect.ValueOf(model).Elem()
		modelType := modelValue.Type().Name()

		t.Run(fmt.Sprintf("Model_%s", modelType), func(t *testing.T) {
			// Find the 'Id' field by name
			idField := modelValue.FieldByName("Id")
			if !idField.IsValid() {
				t.Fatalf("model %s does not have an 'Id' field", modelType)
			}

			// 1. Check if the ID is initially uuid.Nil
			initialId, ok := idField.Interface().(uuid.UUID)
			assert.True(t, ok, "Id field is not of type uuid.UUID")
			assert.Equal(t, uuid.Nil, initialId, "ID should be nil before BeforeCreate is called")

			// 2. Call the BeforeCreate hook
			err := model.BeforeCreate(nil)
			assert.NoError(t, err, "BeforeCreate should not return an error")

			// 3. Check if the ID has been set to a new UUID
			assert.NotEqual(t, uuid.Nil, idField.Interface().(uuid.UUID), "ID should not be nil after BeforeCreate is called")
		})
	}
}

func TestFileHooks(t *testing.T) {
	accountId := uuid.New()

	t.Run("AfterFind hook", func(t *testing.T) {
		t.Run("should set correct filenames when name is present", func(t *testing.T) {
			file := &File{
				Id:        uuid.New(),
				Name:      "documento_importante.pdf",
				Extension: "pdf",
				AccountId: accountId,
				Mimetype:  "application/pdf",
			}

			err := file.AfterFind(nil)
			assert.NoError(t, err)

			expectedFilename := fmt.Sprintf("%s.pdf", file.Id)
			assert.Equal(t, expectedFilename, file.Filename)
			assert.Equal(t, "documento_importante.pdf", file.PublicFilename)
			assert.Equal(t, fmt.Sprintf("%s/%s", accountId, expectedFilename), file.Filepath)
		})

		t.Run("should set correct public filename for images", func(t *testing.T) {
			file := &File{
				Id:        uuid.New(),
				Extension: "png",
				AccountId: accountId,
				Mimetype:  "image/*",
			}

			err := file.AfterFind(nil)
			assert.NoError(t, err)

			assert.Equal(t, fmt.Sprintf("%s.jpeg", file.Id), file.PublicFilename)
		})

		t.Run("should fallback public filename to filename when name is missing", func(t *testing.T) {
			file := &File{
				Id:        uuid.New(),
				Extension: "zip",
				AccountId: accountId,
				Mimetype:  "application/zip",
			}

			err := file.AfterFind(nil)
			assert.NoError(t, err)

			expectedFilename := fmt.Sprintf("%s.zip", file.Id)
			assert.Equal(t, expectedFilename, file.Filename)
			assert.Equal(t, expectedFilename, file.PublicFilename)
		})
	})

	t.Run("AfterCreate hook should call AfterFind", func(t *testing.T) {
		file := &File{
			Id:        uuid.New(),
			Extension: "txt",
			AccountId: accountId,
			Mimetype:  "text/plain",
		}

		err := file.AfterCreate(nil)
		assert.NoError(t, err)

		expectedFilename := fmt.Sprintf("%s.txt", file.Id)
		assert.Equal(t, expectedFilename, file.Filename, "AfterCreate should trigger the same logic as AfterFind")
	})
}

func TestCreateEnums(t *testing.T) {
	sqlDB, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}

	dialector := postgres.New(postgres.Config{
		Conn: sqlDB,
	})
	db, err := gorm.Open(dialector, &gorm.Config{})
	assert.NoError(t, err)

	buildExpectedQuery := func(enumName string, values []string) string {
		quotedValues := make([]string, len(values))
		for i, v := range values {
			quotedValues[i] = fmt.Sprintf("'%s'", v)
		}
		// This replicates the exact format from helpers.go to ensure the mock matches.
		return fmt.Sprintf(`
        DO $$ 
        BEGIN
            IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = '%s') THEN
                CREATE TYPE %s AS ENUM (%s);
            END IF;
        END $$;
    `, enumName, enumName, strings.Join(quotedValues, ", "))
	}

	testCases := []struct {
		model      CreateEnumInterface
		enumName   string
		enumValues []string
	}{
		{model: &Campaign{}, enumName: "enum_campaigns_status", enumValues: []string{"ready", "queued", "processing", "paused", "waiting_connection", "canceled", "insufficient_credits", "hsm_limit_exceeded", "import_error", "error", "done"}},
		{model: &Contact{}, enumName: "enum_contacts_origin", enumValues: []string{"api", "app", "campaign", "driver", "import", "web"}},
		{model: &Question{}, enumName: "enum_questions_type", enumValues: []string{"nps", "csat", "custom"}},
		{model: &Ticket{}, enumName: "enum_tickets_origin", enumValues: []string{"automatic", "manual", "widget"}},
		{model: &TicketTransfer{}, enumName: "enum_ticket_transfers_action", enumValues: []string{"opened", "transferred", "closed"}},
		{model: &User{}, enumName: "enum_users_status", enumValues: []string{"online", "offline", "away"}},
	}

	for _, tc := range testCases {
		modelType := reflect.TypeOf(tc.model).Elem().Name()
		t.Run(fmt.Sprintf("Model_%s", modelType), func(t *testing.T) {
			expectedQuery := buildExpectedQuery(tc.enumName, tc.enumValues)

			mock.ExpectExec(regexp.QuoteMeta(expectedQuery)).WillReturnResult(sqlmock.NewResult(0, 0))

			err := tc.model.CreateEnums(db)
			assert.NoError(t, err)

			err = mock.ExpectationsWereMet()
			assert.NoError(t, err, "Expectations were not met for model %s", modelType)
		})
	}
}

func TestCreateEnumIfNotExists(t *testing.T) {
	sqlDB, mock, err := sqlmock.New()
	if err != nil {
		t.Fatalf("an error '%s' was not expected when opening a stub database connection", err)
	}

	dialector := postgres.New(postgres.Config{
		Conn: sqlDB,
	})
	db, err := gorm.Open(dialector, &gorm.Config{})
	assert.NoError(t, err)

	buildExpectedQuery := func(enumName string, values []string) string {
		quotedValues := make([]string, len(values))
		for i, v := range values {
			quotedValues[i] = fmt.Sprintf("'%s'", v)
		}
		return fmt.Sprintf(`
        DO $$ 
        BEGIN
            IF NOT EXISTS (SELECT 1 FROM pg_type WHERE typname = '%s') THEN
                CREATE TYPE %s AS ENUM (%s);
            END IF;
        END $$;
    `, enumName, enumName, strings.Join(quotedValues, ", "))
	}

	t.Run("should create enum successfully", func(t *testing.T) {
		enumName := "test_enum_status"
		enumValues := []string{"active", "inactive", "pending"}
		expectedQuery := buildExpectedQuery(enumName, enumValues)
		mock.ExpectExec(regexp.QuoteMeta(expectedQuery)).WillReturnResult(sqlmock.NewResult(0, 0))
		err := CreateEnumIfNotExists(db, enumName, enumValues)
		assert.NoError(t, err)
		assert.NoError(t, mock.ExpectationsWereMet())
	})

	t.Run("should return error on db failure", func(t *testing.T) {
		enumName := "test_enum_failure"
		enumValues := []string{"fail", "ok"}
		expectedQuery := buildExpectedQuery(enumName, enumValues)
		dbError := fmt.Errorf("database error")
		mock.ExpectExec(regexp.QuoteMeta(expectedQuery)).WillReturnError(dbError)
		err := CreateEnumIfNotExists(db, enumName, enumValues)
		assert.Error(t, err)
		assert.ErrorIs(t, err, dbError)
		assert.Contains(t, err.Error(), "failed to create enum test_enum_failure")
		assert.NoError(t, mock.ExpectationsWereMet())
	})
}
