package repositories

import (
	"context"

	gr "github.com/ikateclab/gorm-repository"
	"gorm.io/gorm"
)

// Re-export types from gorm-repository to avoid direct imports throughout the codebase

// Repository is the main repository interface that provides CRUD operations
type Repository[T any] interface {
	gr.Repository[T]
	Max(ctx context.Context, column string, options ...Option) (int, error)
}

// GormRepository is the concrete implementation of Repository using GORM
type GormRepository[T any] struct {
	*gr.GormRepository[T]
}

// Option represents configuration options for repository operations
type Option = gr.Option

// PaginationResult represents a paginated query result
type PaginationResult[T any] struct {
	*gr.PaginationResult[T]
}

// NewGormRepository creates a new GORM repository instance
func NewGormRepository[T any](db *gorm.DB) *GormRepository[T] {
	return &GormRepository[T]{
		GormRepository: gr.NewGormRepository[T](db),
	}
}

// Option functions - re-export from gorm-repository

// WithQuery allows adding custom query conditions
func WithQuery(queryFunc func(*gorm.DB) *gorm.DB) Option {
	return gr.WithQuery(queryFunc)
}

// WithTx specifies a transaction to use for the operation
func WithTx(tx *gr.Tx) Option {
	return gr.WithTx(tx)
}

// WithRelations specifies which relations to preload
func WithRelations(relations ...string) Option {
	return gr.WithRelations(relations...)
}

// WithQueryStruct allows querying using a struct with field values
func WithQueryStruct(query map[string]interface{}) Option {
	return gr.WithQueryStruct(query)
}

func (r *GormRepository[T]) Max(ctx context.Context, column string, options ...Option) (int, error) {
	var entity T
	var max *int

	db := r.DB.WithContext(ctx)

	if err := db.Model(&entity).Select("MAX(?)", gorm.Expr(column)).Scan(&max).Error; err != nil {
		return 0, err
	}

	if max == nil {
		return 0, nil
	}

	return *max, nil

}
