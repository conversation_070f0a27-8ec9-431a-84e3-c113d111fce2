package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type ContactBlockListItem struct {
	Id                 uuid.UUID         `gorm:"primaryKey" json:"id"`
	IdFromService      string            `gorm:"type:string;not null" json:"idFromService"` // identificador do contato (número de telefone, email, etc)
	ContactBlockListId uuid.UUID         `gorm:"type:uuid;not null" json:"contactBlockListId"`
	ContactBlockList   *ContactBlockList `gorm:"foreignKey:ContactBlockListId" json:"contactBlockList"`
	AccountId          uuid.UUID         `gorm:"type:uuid;not null" json:"accountId"`
	Account            *Account          `gorm:"foreignKey:AccountId" json:"account"`
	CreatedAt          time.Time         `gorm:"type:timestamptz;not null;default:now()" json:"createdAt"`
	UpdatedAt          time.Time         `gorm:"type:timestamptz;not null;default:now()" json:"updatedAt"`
}

func (ContactBlockListItem) TableName() string {
	return "contact_block_list_items"
}

func (c *ContactBlockListItem) BeforeCreate(tx *gorm.DB) (err error) {
	c.Id = uuid.New()
	return
}
