//go:build unit

package gupshup_adapter

import (
	"context"
	"digisac-go/worker/core/adapters/adapter_types"
	"testing"

	"github.com/stretchr/testify/assert"
)

func TestTryProcessTemplateWebhook(t *testing.T) {
	adapter := &GupshupAdapter{}

	tests := []struct {
		name           string
		payload        interface{}
		expectedResult *adapter_types.BuiltWebhook
		expectedBool   bool
	}{
		{
			name: "status update webhook",
			payload: &GupshupTemplateWebhook{
				Type: GupshupWebhookTypeTemplateEvent,
				Payload: &GupshupTemplateWebhookPayload{
					Id:             "template123",
					Type:           GupshupTemplateEventTypeStatusUpdate,
					Status:         "APPROVED",
					RejectedReason: "",
				},
			},
			expectedResult: &adapter_types.BuiltWebhook{
				Template: &adapter_types.WebhookTemplate{
					Id:             "template123",
					Type:           adapter_types.WebhookTemplateType(GupshupWebhookTypeTemplateEvent),
					Status:         "APPROVED",
					RejectedReason: "",
				},
			},
			expectedBool: true,
		},
		{
			name: "quality update webhook",
			payload: &GupshupTemplateWebhook{
				Type: GupshupWebhookTypeTemplateEvent,
				Payload: &GupshupTemplateWebhookPayload{
					Id:      "template123",
					Type:    GupshupTemplateEventTypeQualityUpdate,
					Quality: string(GupshupTemplateQualityGreen),
				},
			},
			expectedResult: &adapter_types.BuiltWebhook{
				Template: &adapter_types.WebhookTemplate{
					Id:      "template123",
					Type:    adapter_types.WebhookTemplateType(GupshupWebhookTypeTemplateEvent),
					Quality: string(GupshupTemplateQualityGreen),
				},
			},
			expectedBool: true,
		},
		{
			name: "invalid webhook type",
			payload: &GupshupTemplateWebhook{
				Type:    "unknown-event",
				Payload: nil,
			},
			expectedResult: nil,
			expectedBool:   false,
		},
		{
			name:           "nil payload",
			payload:        nil,
			expectedResult: nil,
			expectedBool:   false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result, ok := adapter.tryProcessTemplateWebhook(context.Background(), tt.payload)
			assert.Equal(t, tt.expectedBool, ok)

			if tt.expectedResult != nil && result != nil {
				assert.Equal(t, tt.expectedResult.Template.Id, result.Template.Id)
				assert.Equal(t, tt.expectedResult.Template.Type, result.Template.Type)
				if tt.expectedResult.Template.Status != "" {
					assert.Equal(t, tt.expectedResult.Template.Status, result.Template.Status)
				}
				if tt.expectedResult.Template.Quality != "" {
					assert.Equal(t, tt.expectedResult.Template.Quality, result.Template.Quality)
				}
			} else {
				assert.Equal(t, tt.expectedResult, result)
			}
		})
	}
}
