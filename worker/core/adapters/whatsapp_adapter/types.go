package whatsapp_adapter

import (
	pb "digisac-go/common/grpc/api"
	"digisac-go/worker/config"
	"digisac-go/worker/core/repositories"
	"net/http"
	"time"

	"github.com/google/uuid"
)

type Event string

type WebhookServiceStatus struct {
	MyId                string     `json:"myId,omitempty"`
	MyNumber            string     `json:"myNumber,omitempty"`
	MyName              string     `json:"myName,omitempty"`
	IsLoading           bool       `json:"isLoading,omitempty"`
	QrCodeUrl           string     `json:"qrCodeUrl,omitempty"`
	QrCodeExpiresAt     *time.Time `json:"qrCodeExpiresAt,omitempty"`
	IsQrCodeExpired     bool       `json:"isQrCodeExpired,omitempty"`
	Mode                string     `json:"mode,omitempty"`
	IsWebConnected      bool       `json:"isWebConnected,omitempty"`
	IsOnChatPage        bool       `json:"isOnChatPage,omitempty"`
	IsConnected         bool       `json:"isConnected,omitempty"`
	IsOnQrPage          bool       `json:"isOnQrPage,omitempty"`
	IsConflicted        bool       `json:"isConflicted,omitempty"`
	IsWebSyncing        bool       `json:"isWebSyncing,omitempty"`
	State               string     `json:"state,omitempty"`
	WaVersion           string     `json:"waVersion,omitempty"`
	EnteredQrCodePageAt *time.Time `json:"enteredQrCodePageAt,omitempty"`
	DisconnectedAt      *time.Time `json:"disconnectedAt,omitempty"`
}

type WebhookServiceData struct {
	Status *WebhookServiceStatus `json:"status"`
}

type WebhookServiceSettings struct {
	KeepOnline bool `json:"keepOnline"`
}

const (
	EventServiceUpdated          Event = "service.updated"
	EventWhatsappMessageCreated  Event = "whatsapp.message.created"
	EventWhatsappMessageUpdated  Event = "whatsapp.message.updated"
	EventWhatsappMessageReaction Event = "whatsapp.message.reaction"
)

type WebhookServicePayload struct {
	AccountId string                  `json:"accountId"`
	CreatedAt *time.Time              `json:"createdAt"`
	Data      *WebhookServiceData     `json:"data"`
	Id        string                  `json:"id"`
	Name      string                  `json:"name"`
	Settings  *WebhookServiceSettings `json:"settings"`
	UpdatedAt *time.Time              `json:"updatedAt"`
}

type WebhookMessageTypeEnum string

const (
	WhatsappMessageTypeMultiVCard           WebhookMessageTypeEnum = "multi_vcard"
	WhatsappMessageTypeVCard                WebhookMessageTypeEnum = "vcard"
	WhatsappMessageTypeLocation             WebhookMessageTypeEnum = "location"
	WhatsappMessageTypePtt                  WebhookMessageTypeEnum = "ptt"
	WhatsappMessageTypeAudio                WebhookMessageTypeEnum = "audio"
	WhatsappMessageTypeImage                WebhookMessageTypeEnum = "image"
	WhatsappMessageTypeVideo                WebhookMessageTypeEnum = "video"
	WhatsappMessageTypeDocument             WebhookMessageTypeEnum = "document"
	WhatsappMessageTypeSticker              WebhookMessageTypeEnum = "sticker"
	WhatsappMessageTypeChat                 WebhookMessageTypeEnum = "chat"
	WhatsappMessageTypeCallLog              WebhookMessageTypeEnum = "call_log"
	WhatsappMessageTypeE2eNotification      WebhookMessageTypeEnum = "e2e_notification"
	WhatsappMessageTypeNotificationTemplate WebhookMessageTypeEnum = "notification_template"
	WhatsappMessageTypeGp2                  WebhookMessageTypeEnum = "gp2"
	WhatsappMessageTypeEventCreation        WebhookMessageTypeEnum = "event_creation"
	WhatsappMessageTypePollCreation         WebhookMessageTypeEnum = "poll_creation"
)

type Contact struct {
	AvatarUrl   string `json:"avatarUrl,omitempty"`
	Id          string `json:"id,omitempty"`
	Name        string `json:"name,omitempty"`
	Number      string `json:"number,omitempty"`
	ProfileName string `json:"profileName,omitempty"`
	IsGroup     bool   `json:"isGroup,omitempty"`
	IsMe        bool   `json:"isMe,omitempty"`
}

type Preview struct {
	Mimetype string `json:"mimetype,omitempty"`
	Url      string `json:"url,omitempty"`
}

type File struct {
	Mimetype string `json:"mimetype,omitempty"`
	Url      string `json:"url,omitempty"`
	Filename string `json:"filename,omitempty"`
}

type Location struct {
	Lat           float64 `json:"lat,omitempty"`
	Lng           float64 `json:"lng,omitempty"`
	MapPreviewUrl string  `json:"mapPreviewUrl,omitempty"`
}

type CtwaContext struct {
	ConversionSource string `json:"conversionSource,omitempty"`
	Description      string `json:"description,omitempty"`
	IsSuspiciousLink string `json:"isSuspiciousLink,omitempty"`
	MediaType        int32  `json:"mediaType,omitempty"`
	MediaUrl         string `json:"mediaUrl,omitempty"`
	SourceUrl        string `json:"sourceUrl,omitempty"`
	ThumbnailUrl     string `json:"thumbnailUrl,omitempty"`
	Title            string `json:"title,omitempty"`
}

type MessageData struct {
	Ack         interface{}  `json:"ack,omitempty"`
	VCard       string       `json:"vcard,omitempty"`
	VCards      []string     `json:"vcards,omitempty"`
	Location    *Location    `json:"location,omitempty"`
	CtwaContext *CtwaContext `json:"ctwaContext,omitempty"`
}

type Message struct {
	Ack       string       `json:"ack,omitempty"`
	Error     string       `json:"error,omitempty"`
	Contact   *Contact     `json:"contact,omitempty"`
	ContactId string       `json:"contactId,omitempty"`
	Data      *MessageData `json:"data,omitempty"`
	FromId    string       `json:"fromId,omitempty"`
	Id        string       `json:"id,omitempty"`
	IsFromMe  bool         `json:"isFromMe,omitempty"`
	Text      string       `json:"text,omitempty"`
	Timestamp time.Time    `json:"timestamp,omitempty"`
	Type      string       `json:"type,omitempty"`
}

type WebhookMessagePayload struct {
	Ack           interface{}            `json:"ack,omitempty"`
	Error         string                 `json:"error,omitempty"`
	Contact       *Contact               `json:"contact,omitempty"`
	ContactId     string                 `json:"contactId,omitempty"`
	Data          *MessageData           `json:"data,omitempty"`
	FromId        string                 `json:"fromId,omitempty"`
	From          *Contact               `json:"from,omitempty"`
	Id            string                 `json:"id,omitempty"`
	IsFromMe      bool                   `json:"isFromMe,omitempty"`
	Text          string                 `json:"text,omitempty"`
	Timestamp     *time.Time             `json:"timestamp,omitempty"`
	Type          WebhookMessageTypeEnum `json:"type,omitempty"`
	QuotedMessage *WebhookMessagePayload `json:"quotedMessage,omitempty"`
	IsStatus      bool                   `json:"isStatus,omitempty"`
	Preview       *Preview               `json:"preview,omitempty"`
	File          *File                  `json:"file,omitempty"`
	ServiceId     string                 `json:"serviceId,omitempty"`
	AccountId     uuid.UUID              `json:"accountId,omitempty"`
}

type WebhookReactionPayload struct {
	MessageId string     `json:"messageId,omitempty"`
	Id        string     `json:"id,omitempty"`
	From      *Contact   `json:"from,omitempty"`
	FromId    string     `json:"fromId,omitempty"`
	Contact   *Contact   `json:"contact,omitempty"`
	ContactId string     `json:"contactId,omitempty"`
	Reaction  string     `json:"reaction,omitempty"`
	Timestamp *time.Time `json:"timestamp,omitempty"`
	IsFromMe  bool       `json:"isFromMe,omitempty"`
	ServiceId string     `json:"serviceId,omitempty"`
	AccountId string     `json:"accountId,omitempty"`
}

type WhatsappWebhookPayloadBody struct {
	Data      interface{} `json:"data,omitempty"` // oneOf WebhookServicePayload, WebhookMessagePayload, []WebhookMessagePayload
	Event     Event       `json:"event,omitempty"`
	Timestamp *time.Time  `json:"timestamp,omitempty"`
	WebhookId string      `json:"webhookId,omitempty"`
}
type WhatsappWebhookPayload struct {
	AccountId string                     `json:"accountId,omitempty"`
	ServiceId string                     `json:"serviceId,omitempty"`
	Payload   WhatsappWebhookPayloadBody `json:"payload,omitempty"`
}

type WhatsappService struct {
	Id        string `json:"id,omitempty"`
	Name      string `json:"name,omitempty"`
	AccountId string `json:"accountId,omitempty"`
	CreatedAt string `json:"createdAt,omitempty"`
	UpdatedAt string `json:"updatedAt,omitempty"`
}

type WhatsappWebhook struct {
	Id        string `json:"id,omitempty"`
	Name      string `json:"name,omitempty"`
	AccountId string `json:"accountId,omitempty"`
	CreatedAt string `json:"createdAt,omitempty"`
	UpdatedAt string `json:"updatedAt,omitempty"`
}

type WhatsappAdapter struct {
	serviceRepository repositories.ServiceRepository
	Clients           map[string]*http.Client
	grpcWppClient     pb.PublicServiceClient
	config            *config.Config
}
