//go:build unit

package crypto_test

import (
	"bytes"
	"testing"

	crypto "digisac-go/worker/core/utils/crypto_utils"
)

func TestRandomBytes(t *testing.T) {
	length := 16
	bytes, err := crypto.RandomBytes(length)
	if err != nil {
		t.Fatalf("Expected no error, got %v", err)
	}

	if len(bytes) != length {
		t.Fatalf("Expected length %d, got %d", length, len(bytes))
	}
}

func TestEncryptDecryptText(t *testing.T) {
	key, err := crypto.RandomBytes(crypto.KeyLength)
	if err != nil {
		t.Fatalf("Error generating key: %v", err)
	}

	originalText := "Hello, World!"
	encryptedText, err := crypto.EncryptText(originalText, key)
	if err != nil {
		t.Fatalf("Error encrypting text: %v", err)
	}

	decryptedText, err := crypto.DecryptText(encryptedText, key)
	if err != nil {
		t.Fatalf("Error decrypting text: %v", err)
	}

	if decryptedText != originalText {
		t.Errorf("expected decrypted text to be '%s', got '%s'", originalText, decryptedText)
	}
}

func TestEncryptDecryptBufferWithConcatenatedIv(t *testing.T) {
	key, err := crypto.RandomBytes(crypto.KeyLength)
	if err != nil {
		t.Fatalf("Error generating key: %v", err)
	}

	originalBuffer := []byte("Test buffer data")
	encryptedBuffer, err := crypto.EncryptBufferWithConcatenatedIv(originalBuffer, key)
	if err != nil {
		t.Fatalf("Error encrypting buffer: %v", err)
	}

	decryptedBuffer, err := crypto.DecryptBufferWithConcatenatedIv(encryptedBuffer, key)
	if err != nil {
		t.Fatalf("Error decrypting buffer: %v", err)
	}

	if !bytes.Equal(decryptedBuffer, originalBuffer) {
		t.Errorf("expected decrypted buffer to be '%v', got '%v'", originalBuffer, decryptedBuffer)
	}
}

func TestEncryptDecryptBufferWithSeparatedIv(t *testing.T) {
	key, err := crypto.RandomBytes(crypto.KeyLength)
	if err != nil {
		t.Fatalf("Error generating key: %v", err)
	}

	originalBuffer := []byte("Test buffer data")
	encryptedBuffer, iv, err := crypto.EncryptBufferWithSeparatedIv(originalBuffer, key)
	if err != nil {
		t.Fatalf("Error encrypting buffer: %v", err)
	}

	decryptedBuffer, err := crypto.DecryptBufferWithSeparatedIv(encryptedBuffer, iv, key)
	if err != nil {
		t.Fatalf("Error decrypting buffer: %v", err)
	}

	if !bytes.Equal(decryptedBuffer, originalBuffer) {
		t.Errorf("expected decrypted buffer to be '%v', got '%v'", originalBuffer, decryptedBuffer)
	}
}

func TestGenerateToken(t *testing.T) {
	token, err := crypto.GenerateToken()
	if err != nil {
		t.Fatalf("Error generating token: %v", err)
	}

	if len(token) == 0 {
		t.Errorf("expected non-empty token")
	}
}
