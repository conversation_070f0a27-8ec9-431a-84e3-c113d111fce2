package gupshup_adapter

type GupshupTemplateQualityEnum string

// Constantes para qualidade de template
const (
	GupshupTemplateQualityUnknown GupshupTemplateQualityEnum = "UNKNOWN"
	GupshupTemplateQualityGreen   GupshupTemplateQualityEnum = "GREEN"
	GupshupTemplateQualityYellow  GupshupTemplateQualityEnum = "YELLOW"
	GupshupTemplateQualityRed     GupshupTemplateQualityEnum = "RED"
	GupshupTemplateQualityHigh    GupshupTemplateQualityEnum = "ACTIVE - HIGH QUALITY"
	GupshupTemplateQualityMedium  GupshupTemplateQualityEnum = "ACTIVE - MEDIUM QUALITY"
	GupshupTemplateQualityLow     GupshupTemplateQualityEnum = "ACTIVE - LOW QUALITY"
	GupshupTemplateQualityPending GupshupTemplateQualityEnum = "ACTIVE - QUALITY PENDING"
)

// GupshupTemplate representa um template individual da Gupshup
type GupshupTemplate struct {
	AppId           string                     `json:"appId"`                     // Id da aplicação
	ButtonSupported string                     `json:"buttonSupported,omitempty"` // Tipos de botões suportados (QR, PN, URL)
	Category        string                     `json:"category"`                  // Categoria do template (MARKETING, UTILITY)
	ElementName     string                     `json:"elementName"`               // Nome do template
	ExternalId      string                     `json:"externalId,omitempty"`      // Id externo do template
	Id              string                     `json:"id"`                        // Id do template
	LanguageCode    string                     `json:"languageCode"`              // Código do idioma (pt_BR, en_US)
	Status          string                     `json:"status"`                    // Status do template (APPROVED, PENDING, FAILED)
	TemplateType    string                     `json:"templateType"`              // Tipo do template (TEXT, IMAGE, VIDEO, DOCUMENT)
	Vertical        string                     `json:"vertical"`                  // Vertical do template
	WabaId          string                     `json:"wabaId"`                    // Id do WhatsApp Business Account
	Data            string                     `json:"data"`                      // Texto do template
	Meta            string                     `json:"meta,omitempty"`            // Metadados do template (JSON string)
	ContainerMeta   string                     `json:"containerMeta,omitempty"`   // Metadados do container (JSON string)
	CreatedOn       int64                      `json:"createdOn,omitempty"`       // Data de criação (timestamp)
	ModifiedOn      int64                      `json:"modifiedOn,omitempty"`      // Data de modificação (timestamp)
	Priority        int                        `json:"priority,omitempty"`        // Prioridade do template
	Namespace       string                     `json:"namespace,omitempty"`       // Namespace do template
	LanguagePolicy  string                     `json:"languagePolicy,omitempty"`  // Política de idioma
	Reason          string                     `json:"reason,omitempty"`          // Razão de falha (se status for FAILED)
	Quality         GupshupTemplateQualityEnum `json:"quality,omitempty"`         // Qualidade do template (GREEN,YELLOW,RED,ACTIVE - HIGH QUALITY,ACTIVE - MEDIUM QUALITY)
}

// GupshupTemplatesResponse representa a resposta da API de templates da Gupshup
type GupshupTemplatesResponse struct {
	Status    string             `json:"status"`    // Status da resposta (success, error)
	Templates []*GupshupTemplate `json:"templates"` // Lista de templates
}

// GupshupTemplateMeta representa a estrutura do campo Meta nos templates da Gupshup
type GupshupTemplateMeta struct {
	Example string `json:"example,omitempty"` // Texto de exemplo para o template
	MediaId string `json:"mediaId,omitempty"` // Id da mídia associada ao template (para templates com imagem/vídeo)
}

// GupshupTemplateContainerMeta representa a estrutura do campo ContainerMeta nos templates da Gupshup
type GupshupTemplateContainerMeta struct {
	AppId                       string           `json:"appId,omitempty"`                       // Id da aplicação
	Data                        string           `json:"data,omitempty"`                        // Texto principal do template
	Buttons                     []*GupshupButton `json:"buttons,omitempty"`                     // Botões do template
	Header                      string           `json:"header,omitempty"`                      // Texto do cabeçalho
	Footer                      string           `json:"footer,omitempty"`                      // Texto do rodapé
	SampleText                  string           `json:"sampleText,omitempty"`                  // Texto de exemplo
	SampleHeader                string           `json:"sampleHeader,omitempty"`                // Exemplo de cabeçalho
	SampleMedia                 string           `json:"sampleMedia,omitempty"`                 // Id da mídia de exemplo
	EnableSample                bool             `json:"enableSample,omitempty"`                // Se o exemplo está habilitado
	EditTemplate                bool             `json:"editTemplate,omitempty"`                // Se o template pode ser editado
	AllowTemplateCategoryChange bool             `json:"allowTemplateCategoryChange,omitempty"` // Se a categoria pode ser alterada
	AddSecurityRecommendation   bool             `json:"addSecurityRecommendation,omitempty"`   // Se recomendações de segurança devem ser adicionadas
	MediaId                     string           `json:"mediaId,omitempty"`                     // Id da mídia
	MessageSendTTLSeconds       int              `json:"messageSendTTLSeconds,omitempty"`       // Tempo de vida da mensagem em segundos
}

type CreateTemplateGupshupResponse struct {
	HandleId *struct {
		Message string `json:"message"`
	} `json:"handleId"`
	Status string `json:"status"` // "success"
}

// GupshupTemplateRequestPayload representa o payload para criação de template na API Gupshup
type GupshupTemplateRequestPayload struct {
	ElementName                 string // Nome do template
	LanguageCode                string // Código do idioma
	Category                    string // Categoria do template
	TemplateType                string // Tipo do template (TEXT, IMAGE, VIDEO, DOCUMENT)
	Vertical                    string // Vertical do template
	Header                      string // Texto do cabeçalho
	ExampleHeader               string // Exemplo de cabeçalho
	Content                     string // Conteúdo do template (corpo)
	Example                     string // Exemplo para o corpo
	Footer                      string // Texto do rodapé
	Buttons                     string // Botões serializados como JSON
	ExampleMedia                string // Id da mídia de exemplo
	EnableSample                string // Se o exemplo está habilitado ("true"/"false")
	AllowTemplateCategoryChange string // Se a categoria pode ser alterada ("true"/"false")
	AddSecurityRecommendation   string // Se recomendações de segurança devem ser adicionadas ("true"/"false")
	CodeExpirationMinutes       string // Tempo de expiração do código em minutos (para templates de autenticação)
}

// GupshupButtonType representa os tipos de botões suportados
type GupshupButtonType string

const (
	GupshupButtonTypeQuickReply  GupshupButtonType = "QUICK_REPLY"
	GupshupButtonTypePhoneNumber GupshupButtonType = "PHONE_NUMBER"
	GupshupButtonTypeURL         GupshupButtonType = "URL"
	GupshupButtonTypeOTP         GupshupButtonType = "OTP"
)

// GupshupOTPType representa os tipos de OTP suportados
type GupshupOTPType string

const (
	GupshupOTPTypeCopyCode GupshupOTPType = "COPY_CODE"
	GupshupOTPTypeOneTap   GupshupOTPType = "ONE_TAP"
)

// GupshupButton é uma struct completa que contém todos os campos possíveis para qualquer tipo de botão
type GupshupButton struct {
	Type          GupshupButtonType `json:"type"`                     // Tipo do botão (QUICK_REPLY, PHONE_NUMBER, URL, OTP)
	Text          string            `json:"text"`                     // Texto do botão
	PhoneNumber   string            `json:"phone_number,omitempty"`   // Número de telefone (para botões PHONE_NUMBER)
	URL           string            `json:"url,omitempty"`            // URL (para botões URL)
	Example       []string          `json:"example,omitempty"`        // Exemplos para o botão (para botões URL)
	OTPType       GupshupOTPType    `json:"otp_type,omitempty"`       // Tipo de OTP (COPY_CODE, ONE_TAP)
	AutofillText  string            `json:"autofill_text,omitempty"`  // Texto para autofill (apenas para ONE_TAP)
	PackageName   string            `json:"package_name,omitempty"`   // Nome do pacote Android (apenas para ONE_TAP)
	SignatureHash string            `json:"signature_hash,omitempty"` // Hash de assinatura (apenas para ONE_TAP)
}
