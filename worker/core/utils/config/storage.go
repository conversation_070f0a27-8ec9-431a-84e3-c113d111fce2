package config

import (
	"digisac-go/worker/config"
	"fmt"
	"math/rand"
	"strings"
	"time"
)

// GetRandomBuckets returns a random bucket name based on configuration
func GetRandomBuckets() string {
	if config.GetStorageDriver() == "oracle" {
		buckets := strings.Split(config.GetOracleBucketsNames(), ",")

		rng := rand.New(rand.NewSource(time.Now().UnixNano()))
		randomIndex := rng.Intn(len(buckets)) + 1

		if randomIndex == 1 {
			return "oracle"
		}

		return fmt.Sprintf("oracle%d", randomIndex)
	}

	return config.GetStorageDriver()
}
