package whatsapp_adapter

import (
	"context"
	"digisac-go/worker/core/adapters/adapter_types"
	"digisac-go/worker/core/models"
	"fmt"
	"log/slog"
	"strconv"
)

func getErrorResponse(ctx context.Context, err error) (*adapter_types.SendMessageResponse, error) {
	slog.InfoContext(ctx, "Send message error", slog.String("error", err.Error()), slog.Any("err", err))

	responseError := &models.MessageError{
		Code:          500, // @TODO: pegar o código de erro
		OriginalError: err.Error(),
	}

	sendMessageResponse := &adapter_types.SendMessageResponse{
		Error: responseError,
		Ack:   "error",
	}

	return sendMessageResponse, nil
}

func parseAckToString(ctx context.Context, ack interface{}) (string, error) {
	if ack == nil {
		return "0", nil
	}

	switch v := ack.(type) {
	case string:
		return v, nil
	case float64:
		return strconv.FormatInt(int64(v), 10), nil
	case int:
		return strconv.FormatInt(int64(v), 10), nil
	case int32:
		return strconv.FormatInt(int64(v), 10), nil
	case int64:
		return strconv.FormatInt(v, 10), nil
	default:
		err := fmt.Errorf("unexpected type for Ack: %T", ack)
		slog.InfoContext(ctx, "Error in parseAckToString", slog.String("ack", fmt.Sprintf("%v", ack)), slog.Any("error", err))
		return "", err
	}
}

func getWebhookType(ctx context.Context, p *WhatsappWebhookPayload) (webhookType string, err error) {
	webhookEvent := p.Payload.Event

	switch webhookEvent {
	case EventServiceUpdated:
		return "service", nil
	case EventWhatsappMessageCreated, EventWhatsappMessageUpdated:
		return "message", nil
	case EventWhatsappMessageReaction:
		return "reaction", nil
	default:
		err := fmt.Errorf("unknown webhook event type %s", webhookEvent)
		slog.InfoContext(ctx, "Error in getWebhookType", slog.String("event", string(webhookEvent)), slog.Any("error", err))
		return "", err
	}
}

func getWebhookMessageType(ctx context.Context, p *WebhookMessagePayload) (string, error) {
	switch p.Type {
	case WhatsappMessageTypeLocation:
		return string(adapter_types.MESSAGE_TYPE_LOCATION), nil
	case WhatsappMessageTypeVCard:
		return string(adapter_types.MESSAGE_TYPE_VCARD), nil
	case WhatsappMessageTypeMultiVCard:
		return string(adapter_types.MESSAGE_TYPE_VCARD), nil // Digisac não tem multi_vcard, criamos uma mensagem para cada vcard
	case WhatsappMessageTypePtt:
		return string(adapter_types.MESSAGE_TYPE_VOICE), nil
	case WhatsappMessageTypeAudio:
		return string(adapter_types.MESSAGE_TYPE_VOICE), nil
	case WhatsappMessageTypeImage:
		return string(adapter_types.MESSAGE_TYPE_IMAGE), nil
	case WhatsappMessageTypeVideo:
		return string(adapter_types.MESSAGE_TYPE_VIDEO), nil
	case WhatsappMessageTypeDocument:
		return string(adapter_types.MESSAGE_TYPE_DOCUMENT), nil
	case WhatsappMessageTypeSticker:
		return string(adapter_types.MESSAGE_TYPE_STICKER), nil
	case WhatsappMessageTypeChat:
		return string(adapter_types.MESSAGE_TYPE_TEXT), nil
	case WhatsappMessageTypeCallLog:
		return string(adapter_types.MESSAGE_TYPE_CALL_LOG), nil
	case WhatsappMessageTypeE2eNotification:
		return string(adapter_types.MESSAGE_TYPE_E2E_NOTIFICATION), nil
	case WhatsappMessageTypeNotificationTemplate:
		return string(adapter_types.MESSAGE_TYPE_E2E_NOTIFICATION), nil
	case WhatsappMessageTypeGp2:
		return string(adapter_types.MESSAGE_TYPE_E2E_NOTIFICATION), nil
	case WhatsappMessageTypeEventCreation:
		return string(adapter_types.MESSAGE_TYPE_EVENT_CREATION), nil
	case WhatsappMessageTypePollCreation:
		return string(adapter_types.MESSAGE_TYPE_POLL_CREATION), nil
	}

	err := fmt.Errorf("webhook message type is unknown: %s", p.Type)
	slog.InfoContext(ctx, "Error in getWebhookMessageType", slog.String("type", string(p.Type)), slog.Any("error", err))
	return "", err
}

func getContactName(contact *Contact) string {
	if contact.IsGroup {
		// No caso de grupos, o name representa o nome do grupo no WhatsApp
		return contact.Name
	}

	if contact.ProfileName != "" {
		// Nome configurado pelo contato no WhatsApp
		return contact.ProfileName
	}

	if contact.Name != "" {
		// Nome do contato salvo na agenda do celular
		return contact.Name
	}

	return contact.Number
}

func getContactAlternativeName(contact *Contact) string {
	if contact.IsGroup {
		// No caso de grupos, não existe um nome alternativo
		return ""
	}

	if contact.Name != "" {
		// Nome do contato salvo na agenda do celular
		return contact.Name
	}

	return contact.Number
}
