package account

import (
	"context"
	"digisac-go/worker/config"
	"digisac-go/worker/core/repositories"
	"log/slog"

	"github.com/google/uuid"
)

type AccountUsedHsmService struct {
	accountRepository repositories.AccountRepository
	config            *config.Config
}

func NewAccountUsedHsmService(accountRepository repositories.AccountRepository, config *config.Config) *AccountUsedHsmService {
	return &AccountUsedHsmService{
		accountRepository: accountRepository,
		config:            config,
	}
}

func (a *AccountUsedHsmService) IncrementHsmUsedLimit(ctx context.Context, accountId uuid.UUID) error {
	account, err := a.accountRepository.FindById(ctx, accountId)
	if err != nil {
		return err
	}

	if account.Settings.Flags.DisableHsmLimit {
		return nil
	}

	account.Plan.HsmUsedLimit++

	err = a.accountRepository.UpdateById(ctx, account.Id, account)

	if err != nil {
		slog.ErrorContext(ctx, "Failed to update account hsm used limit", slog.String("accountId", accountId.String()), slog.String("error", err.Error()))
		return err
	}

	return nil
}

func (a *AccountUsedHsmService) DecrementHsmUsedLimit(ctx context.Context, accountId uuid.UUID) error {
	account, err := a.accountRepository.FindById(ctx, accountId)
	if err != nil {
		return err
	}

	if account.Settings.Flags.DisableHsmLimit {
		return nil
	}

	account.Plan.HsmUsedLimit--

	err = a.accountRepository.UpdateById(ctx, account.Id, account)

	if err != nil {
		slog.ErrorContext(ctx, "Failed to update account hsm used limit", slog.String("accountId", accountId.String()), slog.String("error", err.Error()))
		return err
	}

	return nil
}
