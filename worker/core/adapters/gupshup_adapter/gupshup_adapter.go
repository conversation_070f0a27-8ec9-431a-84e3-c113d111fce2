package gupshup_adapter

import (
	"bytes"
	"context"
	"digisac-go/worker/config"
	"digisac-go/worker/core/adapters/adapter_types"
	"digisac-go/worker/core/adapters/base_waba_adapter"
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/repositories"
	"digisac-go/worker/core/services/storage"
	"digisac-go/worker/core/utils/common"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"mime/multipart"
	"net/http"
	"net/url"
	"strings"

	"github.com/goforj/godump"
	"github.com/google/uuid"
)

type GupshupAdapter struct {
	*base_waba_adapter.BaseWabaAdapter
	partnerApiUrl           string
	ServiceRepository       repositories.ServiceRepository
	templateRepository      repositories.WhatsappBusinessTemplateRepository
	fileRepository          repositories.FileRepository
	storageService          *storage.StorageService
	UploadTemplateMediaFunc func(ctx context.Context, hsmId uuid.UUID, appId string, appToken string) (string, error)
}

func NewGupshupAdapter(
	serviceRepository repositories.ServiceRepository,
	templateRepository repositories.WhatsappBusinessTemplateRepository,
	fileRepository repositories.FileRepository,
	config *config.Config,
	storageService *storage.StorageService,
) *GupshupAdapter {
	gupshupAdapter := &GupshupAdapter{
		partnerApiUrl:      "https://partner.gupshup.io/partner",
		ServiceRepository:  serviceRepository,
		templateRepository: templateRepository,
		fileRepository:     fileRepository,
		storageService:     storageService,
	}

	gupshupAdapter.UploadTemplateMediaFunc = gupshupAdapter.uploadTemplateMedia

	gupshupAdapter.BaseWabaAdapter = base_waba_adapter.NewBaseWabaAdapter(
		serviceRepository,
		templateRepository,
		fileRepository,
		storageService,
		config,
		gupshupAdapter)

	return gupshupAdapter
}

func (a *GupshupAdapter) GetMessageApiUrl(ctx context.Context, service *models.Service) (string, error) {
	return a.partnerApiUrl + "/app/" + service.InternalData.Id + "/v3/message", nil
}

func (a *GupshupAdapter) GetTemplatesApiUrl(ctx context.Context, service *models.Service) (string, error) {
	return a.partnerApiUrl + "/app/" + service.InternalData.Id + "/templates", nil
}

// https://partner-docs.gupshup.io/reference/delete_partner-app-appid-template-elementname
func (a *GupshupAdapter) DeleteTemplateApiUrl(ctx context.Context, service *models.Service, templateName string) (string, error) {
	return a.partnerApiUrl + "/app/" + service.InternalData.Id + "/template/" + templateName, nil
}

func (a *GupshupAdapter) DeleteTemplateApiHeaders(ctx context.Context, service *models.Service) (map[string]string, error) {
	return map[string]string{
		"Authorization": "Bearer " + service.InternalData.AppToken,
	}, nil
}

func (a *GupshupAdapter) GetMessageApiHeaders(ctx context.Context, service *models.Service) (map[string]string, error) {
	return map[string]string{
		"Content-Type":  "application/json",
		"Authorization": "Bearer " + service.InternalData.AppToken,
	}, nil
}

func (a *GupshupAdapter) Start(ctx context.Context, serviceId uuid.UUID) (err error) {
	_, err = a.GetClient(ctx, serviceId)

	if err != nil {
		slog.ErrorContext(ctx, "Error starting Whatsapp Business adapter", slog.String("serviceId", serviceId.String()), slog.Any("error", err))
		return fmt.Errorf("failed to start adapter for serviceId %s: %w", serviceId, err)
	}

	loginResponse, err := a.loginPartner(ctx, serviceId)

	if err != nil {
		return fmt.Errorf("failed to login partner: %w", err)
	}

	service, err := a.ServiceRepository.FindById(ctx, serviceId)

	if err != nil {
		return fmt.Errorf("failed to find service with serviceId %s: %w", serviceId, err)
	}

	service.InternalData.PartnerToken = loginResponse.Token

	err = a.ServiceRepository.UpdateById(ctx, service.Id, service)

	if err != nil {
		return fmt.Errorf("failed to update service: %w", err)
	}

	partnerAppsResponse, err := a.getPartnerApps(ctx, serviceId)

	if err != nil {
		return fmt.Errorf("failed to get partner apps: %w", err)
	}

	if len(partnerAppsResponse.PartnerAppsList) > 0 {
		for _, app := range partnerAppsResponse.PartnerAppsList {
			if app.Name == service.Data.AppName {
				service.InternalData.Id = app.Id
				service.InternalData.Phone = app.Phone
				break
			}
		}
	} else {
		setPartnerResponse, err := a.setPartner(ctx, serviceId)

		if err != nil {
			return fmt.Errorf("failed to link client gupshup app with partner: %w", err)
		}

		service.InternalData.Id = setPartnerResponse.PartnerApp.Id
		service.InternalData.Phone = setPartnerResponse.PartnerApp.Phone
	}

	err = a.ServiceRepository.UpdateById(ctx, service.Id, service)

	if err != nil {
		return fmt.Errorf("failed to update service: %w", err)
	}

	setPartnerResponse, err := a.getAppToken(ctx, serviceId)

	if err != nil {
		return fmt.Errorf("failed to get app token: %w", err)
	}

	service.InternalData.AppToken = setPartnerResponse.Token.Token

	err = a.ServiceRepository.UpdateById(ctx, service.Id, service)

	if err != nil {
		return fmt.Errorf("failed to update service: %w", err)
	}

	return nil
}

func (a *GupshupAdapter) SetWebhook(ctx context.Context, serviceId uuid.UUID, url string) error {
	slog.InfoContext(ctx, "SetWebhook called", slog.String("serviceId", serviceId.String()), slog.String("url", url))

	subscriptions, err := a.getAllSubscriptions(ctx, serviceId)

	if err != nil {
		return fmt.Errorf("failed to get all subscriptions: %w", err)
	}

	// Deleta o webhook antigo
	if len(subscriptions.Subscriptions) > 0 {
		for _, subscription := range subscriptions.Subscriptions {
			if subscription.Tag != "default_callback" {
				continue
			}

			err := a.deleteSubscription(ctx, serviceId, subscription.Id)

			if err != nil {
				return fmt.Errorf("failed to delete subscription: %w", err)
			}
		}
	}

	// Cria o novo webhook
	_, err = a.setSubscription(ctx, serviceId, url)

	if err != nil {
		return fmt.Errorf("failed to set subscription: %w", err)
	}

	return nil
}

func (a *GupshupAdapter) GetTemplates(ctx context.Context, serviceId uuid.UUID) ([]*models.WhatsappBusinessTemplate, error) {
	slog.InfoContext(ctx, "GetTemplates called", slog.String("serviceId", serviceId.String()))

	client, err := a.GetClient(ctx, serviceId)
	if err != nil {
		return nil, fmt.Errorf("failed to get client for serviceId %s: %w", serviceId, err)
	}

	service, err := a.ServiceRepository.FindById(ctx, serviceId)
	if err != nil {
		return nil, fmt.Errorf("failed to find service with serviceId %s: %w", serviceId, err)
	}

	templateApiUrl, err := a.GetTemplatesApiUrl(ctx, service)
	if err != nil {
		return nil, fmt.Errorf("failed to get template api url: %w", err)
	}

	req, err := http.NewRequest("GET", templateApiUrl, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Add("accept", "application/json")
	req.Header.Add("Authorization", service.InternalData.AppToken)

	res, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}

	defer func() {
		if err := res.Body.Close(); err != nil {
			fmt.Println("Error closing response body:", err)
		}
	}()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	if res.StatusCode != 200 {
		return nil, fmt.Errorf("failed to get templates: %d", res.StatusCode)
	}

	// Use a struct definida em template_types.go para a resposta da Gupshup
	var gupshupResponse *GupshupTemplatesResponse

	err = json.Unmarshal(body, &gupshupResponse)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal response body: %w", err)
	}

	slog.DebugContext(ctx, "Gupshup templates response", slog.Int("template_count", len(gupshupResponse.Templates)))

	type byStatus struct {
		template   *GupshupTemplate
		isApproved bool
	}

	templatesByElement := make(map[string]byStatus)

	for _, t := range gupshupResponse.Templates {
		currentIsApproved := t.Status == "APPROVED"
		entry, exists := templatesByElement[t.ElementName]

		if !exists {
			templatesByElement[t.ElementName] = byStatus{
				template:   t,
				isApproved: currentIsApproved,
			}
			continue
		}

		// Se novo for aprovado e o atual não, substitui
		if currentIsApproved && !entry.isApproved {
			templatesByElement[t.ElementName] = byStatus{template: t, isApproved: true}
			continue
		}

		// !currentIsApproved && entry.isApproved ignoramos

		// Se ambos têm o mesmo status, mantém o mais novo
		if entry.isApproved == currentIsApproved && t.CreatedOn > entry.template.CreatedOn {
			templatesByElement[t.ElementName] = byStatus{template: t, isApproved: currentIsApproved}
		}
	}

	var templates []*models.WhatsappBusinessTemplate

	for elementName, entry := range templatesByElement {
		slog.DebugContext(ctx, "Including template", slog.String("ElementName", elementName))
		whatsappTemplate := a.processTemplate(ctx, entry.template, service, serviceId)
		templates = append(templates, whatsappTemplate)
	}

	return templates, nil
}

// processTemplate processa um template do Gupshup para o formato padrão
func (a *GupshupAdapter) processTemplate(ctx context.Context, template *GupshupTemplate, service *models.Service, serviceId uuid.UUID) *models.WhatsappBusinessTemplate {
	var components []*models.WhatsappBusinessComponent

	var containerMetaData = &GupshupTemplateContainerMeta{}
	var metaData = &GupshupTemplateMeta{}

	if template.ContainerMeta != "" {
		if err := json.Unmarshal([]byte(template.ContainerMeta), &containerMetaData); err != nil {
			slog.DebugContext(ctx, "Failed to parse ContainerMeta JSON", slog.String("containerMeta", template.ContainerMeta), slog.String("error", err.Error()))
		}
	}

	if template.Meta != "" {
		if err := json.Unmarshal([]byte(template.Meta), &metaData); err != nil {
			slog.DebugContext(ctx, "Failed to parse Meta JSON", slog.String("meta", template.Meta), slog.String("error", err.Error()))
		}
	}

	var messageType = models.WhatsappBusinessMessageTypeTextOnly
	headerFormat := adapter_types.ComponentFormatText

	switch template.TemplateType {
	case "IMAGE":
		messageType = models.WhatsappBusinessMessageTypeInteractive
		headerFormat = adapter_types.ComponentFormatImage
	case "VIDEO":
		messageType = models.WhatsappBusinessMessageTypeInteractive
		headerFormat = adapter_types.ComponentFormatVideo
	case "DOCUMENT":
		messageType = models.WhatsappBusinessMessageTypeInteractive
		headerFormat = adapter_types.ComponentFormatDocument
	case "AUDIO":
		messageType = models.WhatsappBusinessMessageTypeInteractive
		headerFormat = adapter_types.ComponentFormatText
	}

	if len(containerMetaData.Buttons) > 0 {
		messageType = models.WhatsappBusinessMessageTypeInteractive
	}

	isAuthenticationTemplate := template.Category == "AUTHENTICATION"

	// Extrair header do ContainerMeta
	if containerMetaData.Header != "" {
		headerComponent := &models.WhatsappBusinessComponent{
			Type:   adapter_types.ComponentTypeHeader,
			Format: headerFormat,
			Text:   containerMetaData.Header,
		}

		// Se for um template com mídia, adicionar exemplo de mídia
		if template.TemplateType == "IMAGE" || template.TemplateType == "VIDEO" || template.TemplateType == "DOCUMENT" {
			example := &models.WhatsappBusinessComponentExample{
				HeaderHandle: []string{containerMetaData.SampleMedia},
			}

			if containerMetaData.MediaId != "" {
				example = &models.WhatsappBusinessComponentExample{
					HeaderHandle: []string{containerMetaData.MediaId},
				}
			}

			headerComponent.Format = headerFormat
			headerComponent.Text = ""
			headerComponent.Example = example
		}

		components = append(components, headerComponent)
	} else if template.TemplateType == "IMAGE" || template.TemplateType == "VIDEO" || template.TemplateType == "DOCUMENT" {
		// Se não tem header explícito mas é um template de mídia, criar um header com a mídia
		headerComponent := &models.WhatsappBusinessComponent{
			Type:   adapter_types.ComponentTypeHeader,
			Format: headerFormat,
		}

		example := &models.WhatsappBusinessComponentExample{
			HeaderHandle: []string{containerMetaData.SampleMedia},
		}

		if containerMetaData.MediaId != "" {
			example = &models.WhatsappBusinessComponentExample{
				HeaderHandle: []string{containerMetaData.MediaId},
			}
		}

		headerComponent.Example = example

		components = append(components, headerComponent)
	}

	// Adicionar componente BODY com o texto do template
	// Remover qualquer texto de botão que possa estar no final do texto
	bodyText := template.Data
	if idx := strings.Index(bodyText, " | ["); idx != -1 {
		bodyText = bodyText[:idx]
	}

	// Bug na gupshup
	// Remover texto do cabeçalho do corpo, se o mesmo texto estiver presente no cabeçalho
	if containerMetaData.Header != "" && strings.HasPrefix(bodyText, containerMetaData.Header+"\n") {
		bodyText = strings.TrimPrefix(bodyText, containerMetaData.Header+"\n")
	}

	// Bug na gupshup
	// Remover texto do rodapé do corpo, se o mesmo texto estiver presente no rodapé
	if containerMetaData.Footer != "" && strings.Contains(bodyText, "\n"+containerMetaData.Footer) {
		bodyText = strings.Replace(bodyText, "\n"+containerMetaData.Footer, "", 1)
	}

	// Se for um template de autenticação, usar o texto padrão de autenticação
	if isAuthenticationTemplate {
		// Criar um template temporário para passar para a função de processamento
		tempTemplate := &models.WhatsappBusinessTemplate{
			Language: template.LanguageCode,
			Category: template.Category,
		}
		// Usar a função ProcessAuthenticationTemplate
		components = a.ProcessAuthenticationTemplate(ctx, tempTemplate, components)
	} else {
		// Adicionar componente BODY normal
		components = append(components, &models.WhatsappBusinessComponent{
			Type: adapter_types.ComponentTypeBody,
			Text: bodyText,
		})
	}

	// Extrair footer do ContainerMeta (apenas para templates não-autenticação)
	if containerMetaData.Footer != "" && !isAuthenticationTemplate {
		components = append(components, &models.WhatsappBusinessComponent{
			Type: adapter_types.ComponentTypeFooter,
			Text: containerMetaData.Footer,
		})
	}

	// Extrair botões do ContainerMeta
	if len(containerMetaData.Buttons) > 0 {
		var buttons []*models.WhatsappBusinessComponentParameterButton

		for _, buttonData := range containerMetaData.Buttons {
			if string(buttonData.Type) != "" {
				button := &models.WhatsappBusinessComponentParameterButton{
					Type:        models.WhatsappBusinessButtonTypeEnum(string(buttonData.Type)),
					Text:        buttonData.Text,
					PhoneNumber: buttonData.PhoneNumber,
					URL:         buttonData.URL,
				}

				// Se for um botão de URL com exemplo, adicionar o exemplo
				if string(buttonData.Type) == adapter_types.ButtonTypeURL && len(buttonData.Example) > 0 {
					button.Example = buttonData.Example
				}

				buttons = append(buttons, button)
			}
		}

		if len(buttons) > 0 {
			components = append(components, &models.WhatsappBusinessComponent{
				Type:    adapter_types.ComponentTypeButtons,
				Buttons: buttons,
			})
		}
	}

	// Converter o status para a enumeração
	status := base_waba_adapter.ParseStatus(template.Status)

	// Criar o template com os dados processados
	return &models.WhatsappBusinessTemplate{
		Name:           template.ElementName,
		Language:       template.LanguageCode,
		Status:         status,
		Category:       template.Category,
		IdGupshup:      template.Id,
		Components:     components,
		MessageType:    messageType,
		Namespace:      template.WabaId,
		ServiceId:      serviceId,
		AccountId:      service.AccountId,
		RejectedReason: template.Reason,
		Quality:        a.QualityParser(ctx, template.Quality),
	}
}

func (a *GupshupAdapter) QualityParser(ctx context.Context, quality GupshupTemplateQualityEnum) models.WhatsappBusinessTemplateQualityEnum {
	switch quality {
	case GupshupTemplateQualityGreen, GupshupTemplateQualityHigh:
		return models.WhatsappBusinessTemplateQualityHigh
	case GupshupTemplateQualityYellow, GupshupTemplateQualityMedium:
		return models.WhatsappBusinessTemplateQualityMedium
	case GupshupTemplateQualityRed, GupshupTemplateQualityLow:
		return models.WhatsappBusinessTemplateQualityLow
	case GupshupTemplateQualityPending:
		return models.WhatsappBusinessTemplateQualityUnknown
	}

	return models.WhatsappBusinessTemplateQualityUnknown
}

// uploadTemplateMedia envia um arquivo de mídia para o Gupshup e retorna o Id da mídia
func (a *GupshupAdapter) uploadTemplateMedia(ctx context.Context, hsmId uuid.UUID, appId string, appToken string) (string, error) {
	slog.InfoContext(ctx, "Sending media for template", slog.String("hsmId", hsmId.String()))

	file, err := a.fileRepository.FindOne(ctx, repositories.WithQueryStruct(map[string]interface{}{
		"attachedId":   hsmId,
		"attachedType": "hsm.file",
	}))

	if err != nil {
		return "", fmt.Errorf("failed to find file: %w", err)
	}

	stream, err := a.storageService.GetStream(ctx, file)

	if err != nil {
		return "", fmt.Errorf("failed to get stream: %w", err)
	}

	body := &bytes.Buffer{}
	writer := multipart.NewWriter(body)

	err = writer.WriteField("file_type", file.Mimetype)

	if err != nil {
		return "", err
	}

	part, err := writer.CreateFormFile("file", file.Name)

	if err != nil {
		return "", err
	}

	_, err = io.Copy(part, stream)

	if err != nil {
		return "", err
	}

	boundary := writer.Boundary()
	if err := writer.Close(); err != nil {
		fmt.Println("Error closing writer:", err)
	}

	url := fmt.Sprintf("%s/app/%s/upload/media", a.partnerApiUrl, appId)
	req, err := http.NewRequest("POST", url, body)

	if err != nil {
		return "", fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Add("Authorization", appToken)
	req.Header.Add("Content-Type", fmt.Sprintf("multipart/form-data; boundary=%s", boundary))
	req.Header.Add("accept", "application/json")

	client := &http.Client{}

	res, err := client.Do(req)

	if err != nil {
		return "", fmt.Errorf("failed to send request: %w", err)
	}

	defer func() {
		if err := res.Body.Close(); err != nil {
			fmt.Println("Error closing response body:", err)
		}
	}()

	responseBody, err := io.ReadAll(res.Body)

	if err != nil {
		return "", fmt.Errorf("failed to read response body: %w", err)
	}

	if res.StatusCode != 200 {
		return "", fmt.Errorf("failed to upload media: status code %d, response: %s", res.StatusCode, string(responseBody))
	}

	var response *CreateTemplateGupshupResponse

	err = json.Unmarshal(responseBody, &response)

	if err != nil {
		return "", fmt.Errorf("failed to unmarshal response body: %w", err)
	}

	if response.Status != "success" {
		return "", fmt.Errorf("failed to upload media: status code %d, response: %s", res.StatusCode, string(responseBody))
	}

	return response.HandleId.Message, nil
}

// getTemplatePayload prepara os dados do template para envio à API do Gupshup
func (a *GupshupAdapter) getTemplatePayload(ctx context.Context, template *models.WhatsappBusinessTemplate) (*GupshupTemplateRequestPayload, error) {
	if template == nil {
		return nil, fmt.Errorf("template cannot be nil")
	}

	// Inicializar o payload com valores básicos
	payload := &GupshupTemplateRequestPayload{
		EnableSample:                "true",
		AllowTemplateCategoryChange: "true",
	}

	// Campos obrigatórios
	if template.Name == "" {
		return nil, fmt.Errorf("template name is required")
	}
	payload.ElementName = template.Name

	if template.Language == "" {
		return nil, fmt.Errorf("template language is required")
	}
	payload.LanguageCode = template.Language

	if template.Category == "" {
		return nil, fmt.Errorf("template category is required")
	}
	payload.Category = template.Category

	// Extrair componentes do template para processamento
	components := extractTemplateComponents(template)

	// Valores padrão para templateType e vertical
	payload.TemplateType = "TEXT"
	payload.Vertical = "TEXT"

	// Buscar o serviço para obter appId e appToken
	service, err := a.ServiceRepository.FindById(ctx, template.ServiceId)
	if err != nil {
		return nil, fmt.Errorf("failed to find service: %w", err)
	}

	// Processar o header e mídia
	if components.header != nil {
		if components.header.Format != "" && components.header.Format != adapter_types.ComponentFormatText {
			payload.TemplateType = components.header.Format
			payload.Vertical = components.header.Format

			// Para templates com mídia, precisamos enviar o arquivo para o Gupshup
			if components.header.Format != adapter_types.ComponentFormatText {
				// Tentar enviar a mídia para o Gupshup
				mediaHandle, err := a.UploadTemplateMediaFunc(ctx,
					template.Id,
					service.InternalData.Id,
					service.InternalData.AppToken,
				)
				if err != nil {
					slog.ErrorContext(ctx, "Failed to send media", slog.String("error", err.Error()))
					return nil, fmt.Errorf("failed to send media: %w", err)
				}

				// Adicionar o Id da mídia como exemplo
				payload.ExampleMedia = mediaHandle
			}
		} else if components.header.Text != "" {
			payload.Header = components.header.Text
			payload.ExampleHeader = components.header.Text
		}
	}

	if components.body != nil {
		payload.Content = components.body.Text

		if components.body.AddSecurityRecommendation {
			payload.AddSecurityRecommendation = "true"
		}

		example := components.body.Text
		if components.body.Example != nil {
			for i, param := range components.body.Example.BodyText[0] {
				placeholder := fmt.Sprintf("{{%d}}", i+1)
				example = strings.Replace(example, placeholder, param, 1)
			}
		} else if len(components.body.Params) > 0 {
			for i, param := range components.body.Params {
				placeholder := fmt.Sprintf("{{%d}}", i+1)
				example = strings.Replace(example, placeholder, param, 1)
			}
		}
		payload.Example = example
	} else {
		return nil, fmt.Errorf("template body is required")
	}

	if components.footer != nil {
		payload.Footer = components.footer.Text

		if template.Category == "AUTHENTICATION" {
			if components.footer.CodeExpirationMinutes > 0 {
				payload.CodeExpirationMinutes = fmt.Sprintf("%d", components.footer.CodeExpirationMinutes)
			} else {
				payload.CodeExpirationMinutes = "30" // Valor padrão de 30 minutos
			}
		}
	}

	if len(components.buttons) > 0 {
		if len(components.buttons) > 3 {
			return nil, fmt.Errorf("maximum of 3 buttons allowed per template")
		}

		// Usar a struct GupshupButton para todos os tipos de botões
		var buttonsForAPI []*GupshupButton

		for _, button := range components.buttons {
			// Converter o tipo de botão para o enum do Gupshup
			buttonType := GupshupButtonType(button.Type)

			// Criar o botão com os campos comuns
			gupshupButton := &GupshupButton{
				Type: buttonType,
				Text: button.Text,
			}

			// Adicionar campos específicos baseado no tipo
			switch buttonType {
			case GupshupButtonTypePhoneNumber:
				gupshupButton.PhoneNumber = button.PhoneNumber

			case GupshupButtonTypeURL:
				gupshupButton.URL = button.URL
				gupshupButton.Example = button.Example

			case GupshupButtonTypeOTP:
				gupshupButton.OTPType = GupshupOTPType(button.OtpType)

				switch button.OtpType {
				case models.WhatsappBusinessButtonOtpTypeOneTap:
					gupshupButton.AutofillText = button.AutofillText

					if button.PackageName != "" {
						gupshupButton.PackageName = button.PackageName
					}
					if button.SignatureHash != "" {
						gupshupButton.SignatureHash = button.SignatureHash
					}
				default:
					// Se não tiver OtpType definido, usar COPY_CODE como padrão mais seguro
					// COPY_CODE é mais seguro pois não requer campos adicionais
					gupshupButton.OTPType = GupshupOTPTypeCopyCode

					// Se tiver PackageName e SignatureHash, provavelmente é ONE_TAP
					if button.PackageName != "" && button.SignatureHash != "" {
						gupshupButton.OTPType = GupshupOTPTypeOneTap
						gupshupButton.AutofillText = "Autofill" // Valor padrão
						gupshupButton.PackageName = button.PackageName
						gupshupButton.SignatureHash = button.SignatureHash
					}
				}
			}

			buttonsForAPI = append(buttonsForAPI, gupshupButton)
		}

		// Serializar os botões para JSON
		buttonsJSON, err := json.Marshal(buttonsForAPI)
		if err != nil {
			return nil, fmt.Errorf("failed to serialize buttons: %w", err)
		}
		payload.Buttons = string(buttonsJSON)
	}

	return payload, nil
}

// templateComponents agrupa os componentes de um template para facilitar o processamento
type templateComponents struct {
	header  *models.WhatsappBusinessComponent
	body    *models.WhatsappBusinessComponent
	footer  *models.WhatsappBusinessComponent
	buttons []*models.WhatsappBusinessComponentParameterButton
}

// extractTemplateComponents extrai os componentes de um template
func extractTemplateComponents(template *models.WhatsappBusinessTemplate) (components *templateComponents) {
	components = &templateComponents{}

	for _, component := range template.Components {
		switch component.Type {
		case adapter_types.ComponentTypeHeader:
			components.header = component
		case adapter_types.ComponentTypeBody:
			components.body = component
		case adapter_types.ComponentTypeFooter:
			components.footer = component
		case adapter_types.ComponentTypeButtons:
			components.buttons = component.Buttons
		}
	}

	return components
}

// CreateTemplateById envia um template para revisão no Gupshup usando o Id do template
// https://partner.gupshup.io/partner/app/{appId}/templates
func (a *GupshupAdapter) CreateTemplate(ctx context.Context, serviceId uuid.UUID, templateId uuid.UUID) (*models.WhatsappBusinessTemplate, error) {
	slog.InfoContext(ctx, "CreateTemplate called", slog.String("serviceId", serviceId.String()), slog.String("templateId", templateId.String()))

	if templateId == uuid.Nil {
		return nil, fmt.Errorf("templateId is required to create a new WABA template")
	}

	client, err := a.GetClient(ctx, serviceId)
	if err != nil {
		return nil, fmt.Errorf("failed to get client for serviceId %s: %w", serviceId, err)
	}

	service, err := a.ServiceRepository.FindById(ctx, serviceId)
	if err != nil {
		return nil, fmt.Errorf("failed to find service with serviceId %s: %w", serviceId, err)
	}

	template, err := a.templateRepository.FindById(ctx, templateId)
	if err != nil {
		return nil, fmt.Errorf("failed to find template with id %s: %w", templateId, err)
	}

	if template.ArchivedAt != nil {
		return nil, fmt.Errorf("template is archived")
	}

	templateApiUrl, err := a.GetTemplatesApiUrl(ctx, service)
	if err != nil {
		return nil, fmt.Errorf("failed to get template api url: %w", err)
	}

	// Obter o payload estruturado
	payload, err := a.getTemplatePayload(ctx, template)
	if err != nil {
		return nil, fmt.Errorf("failed to prepare template payload: %w", err)
	}

	// Converter o payload para url.Values para envio
	formData := url.Values{}

	// Adicionar todos os campos do payload ao formData
	if payload.ElementName != "" {
		formData.Add("elementName", payload.ElementName)
	}
	if payload.LanguageCode != "" {
		formData.Add("languageCode", payload.LanguageCode)
	}
	if payload.Category != "" {
		formData.Add("category", payload.Category)
	}
	if payload.TemplateType != "" {
		formData.Add("templateType", payload.TemplateType)
	}
	if payload.Vertical != "" {
		formData.Add("vertical", payload.Vertical)
	}
	if payload.Header != "" {
		formData.Add("header", payload.Header)
	}
	if payload.ExampleHeader != "" {
		formData.Add("exampleHeader", payload.ExampleHeader)
	}
	if payload.Content != "" {
		formData.Add("content", payload.Content)
	}
	if payload.Example != "" {
		formData.Add("example", payload.Example)
	}
	if payload.Footer != "" {
		formData.Add("footer", payload.Footer)
	}
	if payload.Buttons != "" {
		formData.Add("buttons", payload.Buttons)
	}
	if payload.ExampleMedia != "" {
		formData.Add("exampleMedia", payload.ExampleMedia)
	}
	if payload.EnableSample != "" {
		formData.Add("enableSample", payload.EnableSample)
	}
	if payload.AllowTemplateCategoryChange != "" {
		formData.Add("allowTemplateCategoryChange", payload.AllowTemplateCategoryChange)
	}
	if payload.AddSecurityRecommendation != "" {
		formData.Add("addSecurityRecommendation", payload.AddSecurityRecommendation)
	}
	if payload.CodeExpirationMinutes != "" {
		formData.Add("codeExpirationMinutes", payload.CodeExpirationMinutes)
	}

	slog.DebugContext(ctx, "Template payload", slog.Any("payload", formData))

	req, err := http.NewRequest("POST", templateApiUrl, strings.NewReader(formData.Encode()))
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Add("Content-Type", "application/x-www-form-urlencoded")
	req.Header.Add("accept", "application/json")
	req.Header.Add("Authorization", service.InternalData.AppToken)

	res, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}

	defer func() {
		if err := res.Body.Close(); err != nil {
			fmt.Println("Error closing response body:", err)
		}
	}()

	body, err := io.ReadAll(res.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	if res.StatusCode == 400 {
		rejectedReason := a.extractRejectedReasonFromResponse(body)

		template.Status = models.WhatsappBusinessTemplateStatusRejected
		template.RejectedReason = rejectedReason

		return template, nil
	}

	if res.StatusCode != 200 && res.StatusCode != 201 {
		return nil, fmt.Errorf("failed to create template: status code %d, response: %s", res.StatusCode, string(body))
	}

	var responseData *GupshupTemplate
	if err := json.Unmarshal(body, &responseData); err != nil {
		return nil, fmt.Errorf("failed to unmarshal response body: %w", err)
	}

	template = a.processTemplate(ctx, responseData, service, serviceId)

	godump.Dump(template)

	return template, nil
}

func (a *GupshupAdapter) extractRejectedReasonFromResponse(body []byte) string {
	var responseError *GupshupCreateTemplateError
	if err := json.Unmarshal(body, &responseError); err != nil {
		return "Failed to unmarshal response body"
	}

	if responseError.Message == "" {
		return "Unknown error"
	}

	return responseError.Message
}

func (a *GupshupAdapter) BuildWebhook(ctx context.Context, serviceId uuid.UUID, payload interface{}) ([]*adapter_types.BuiltWebhook, error) {
	// Primeiro, tenta processar como um webhook de template que é diferente o padrão da meta
	templateWebhook, isTemplateWebhook := a.tryProcessTemplateWebhook(ctx, payload)
	if isTemplateWebhook && templateWebhook != nil {
		return []*adapter_types.BuiltWebhook{templateWebhook}, nil
	}

	// Se não for um webhook de template, delega para o processamento padrão
	return a.BaseWabaAdapter.BuildWebhook(ctx, serviceId, payload)
}

// tryProcessTemplateWebhook tenta processar o payload como um webhook de atualização de template
func (a *GupshupAdapter) tryProcessTemplateWebhook(ctx context.Context, payload interface{}) (*adapter_types.BuiltWebhook, bool) {
	var templateWebhook GupshupTemplateWebhook

	err := common.ToStruct(ctx, payload, &templateWebhook)
	if err != nil {
		return nil, false
	}

	// Verifica se é um webhook de template do tipo "template-event"
	if templateWebhook.Type != GupshupWebhookTypeTemplateEvent || templateWebhook.Payload == nil {
		return nil, false
	}
	// Cria o webhook de template
	webhookTemplate := &adapter_types.WebhookTemplate{
		Id:   templateWebhook.Payload.Id,
		Type: adapter_types.WebhookTemplateType(templateWebhook.Type),
	}

	switch templateWebhook.Payload.Type {
	case GupshupTemplateEventTypeStatusUpdate:
		// Converter o status para a enumeração
		status := base_waba_adapter.ParseStatus(templateWebhook.Payload.Status)
		webhookTemplate.Status = string(status)
		webhookTemplate.RejectedReason = templateWebhook.Payload.RejectedReason
	case GupshupTemplateEventTypeQualityUpdate:
		webhookTemplate.Quality = templateWebhook.Payload.Quality
	default:
		// Tipo de evento desconhecido
		return nil, false
	}

	return &adapter_types.BuiltWebhook{
		Template: webhookTemplate,
	}, true
}
