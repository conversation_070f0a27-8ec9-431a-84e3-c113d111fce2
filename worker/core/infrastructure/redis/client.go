package redis

import (
	"context"

	"github.com/redis/go-redis/v9"
)

// SetupClient creates and returns a new Redis client
func SetupClient() (client *redis.Client, err error) {
	ctx := context.Background()

	client = redis.NewClient(&redis.Options{
		Addr:     "redis:6379",
		Password: "",
		DB:       0, // use default DB
	})

	_, err = client.Ping(ctx).Result()

	if err != nil {
		panic(err)
	}

	return client, err
}
