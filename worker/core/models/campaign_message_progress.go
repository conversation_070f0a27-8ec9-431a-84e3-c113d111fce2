package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/datatypes"
	"gorm.io/gorm"
)

const BLOCKED_BY_MESSAGE_RULE = "BLOCKED_BY_MESSAGE_RULE"

type CampaignMessageProgress struct {
	Id                uuid.UUID        `gorm:"type:uuid;primary_key" json:"id,omitempty"`
	SentAt            *time.Time       `gorm:"type:timestamptz" json:"sentAt,omitempty"`
	Failed            bool             `gorm:"default:false;not null" json:"failed,omitempty"`
	FailReason        string           `gorm:"type:text" json:"failReason,omitempty"`
	Parameters        datatypes.JSON   `gorm:"type:jsonb;serializer:json;default:'{}';" json:"parameters,omitempty"`
	CreatedAt         *time.Time       `json:"createdAt,omitempty"`
	UpdatedAt         *time.Time       `json:"updatedAt,omitempty"`
	CampaignId        uuid.UUID        `gorm:"type:uuid;not null" json:"campaignId,omitempty"`
	ContactId         uuid.UUID        `gorm:"type:uuid;not null" json:"contactId,omitempty"`
	ServiceId         uuid.UUID        `gorm:"type:uuid;not null" json:"serviceId,omitempty"`
	MessageId         uuid.UUID        `gorm:"type:uuid" json:"messageId,omitempty"`
	AccountId         uuid.UUID        `gorm:"type:uuid;not null" json:"accountId,omitempty"`
	CampaignMessageId uuid.UUID        `gorm:"type:uuid" json:"campaignMessageId,omitempty"`
	Account           *Account         `gorm:"foreignKey:AccountId" json:"account,omitempty"`
	Contact           *Contact         `gorm:"foreignKey:ContactId" json:"contact,omitempty"`
	Campaign          *Campaign        `gorm:"foreignKey:CampaignId" json:"campaign,omitempty"`
	CampaignMessage   *CampaignMessage `gorm:"foreignKey:CampaignMessageId" json:"campaignMessage,omitempty"`
	Message           *Message         `gorm:"foreignKey:MessageId" json:"message,omitempty"`
	Service           *Service         `gorm:"foreignKey:ServiceId" json:"service,omitempty"`
}

func (CampaignMessageProgress) TableName() string {
	return "campaign_messages_progress"
}

func (c *CampaignMessageProgress) BeforeCreate(tx *gorm.DB) (err error) {
	c.Id = uuid.New()
	return
}
