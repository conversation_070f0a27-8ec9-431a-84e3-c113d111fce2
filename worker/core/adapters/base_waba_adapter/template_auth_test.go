//go:build unit

package base_waba_adapter

import (
	"testing"
)

func TestGetAuthenticationText(t *testing.T) {
	tests := []struct {
		lang     string
		expected string
	}{
		{"en", "{{1}} is your verification code."},
		{"es", "Tu código de verificación es {{1}}."},
		{"pt_BR", "Seu código de verificação é {{1}}."},
		{"fr", "{{1}} is your verification code."}, // fallback to English
	}

	for _, tt := range tests {
		got := GetAuthenticationText(tt.lang)
		if got != tt.expected {
			t.Errorf("GetAuthenticationText(%q) = %q; want %q", tt.lang, got, tt.expected)
		}
	}
}

func TestGetExpirationWarningText(t *testing.T) {
	tests := []struct {
		lang     string
		expected string
	}{
		{"en", "This code expires in {NUM_MINUTES} minutes."},
		{"es", "Este código caduca en {NUM_MINUTES} minutos."},
		{"pt_BR", "Este código expira em {NUM_MINUTES} minutos."},
		{"fr", "This code expires in {NUM_MINUTES} minutes."}, // fallback to English
	}

	for _, tt := range tests {
		got := GetExpirationWarningText(tt.lang)
		if got != tt.expected {
			t.Errorf("GetExpirationWarningText(%q) = %q; want %q", tt.lang, got, tt.expected)
		}
	}
}
