package test

import (
	"context"
	"fmt"
	"time"

	"digisac-go/worker/config"

	"github.com/docker/go-connections/nat"
	"github.com/testcontainers/testcontainers-go"
	"github.com/testcontainers/testcontainers-go/wait"
)

// PostgresContainer encapsula um contêiner PostgreSQL para testes
type PostgresContainer struct {
	testcontainers.Container
	Host     string
	Port     string
	Database string
	Username string
	Password string
}

// NewPostgresContainer cria um novo contêiner PostgreSQL para testes
func NewPostgresContainer(ctx context.Context, cfg *config.Config) (*PostgresContainer, error) {
	req := testcontainers.ContainerRequest{
		Image:        "postgres:11.9-alpine",
		ExposedPorts: []string{"5432/tcp"},
		Env: map[string]string{
			"POSTGRES_USER":     cfg.DbUsername,
			"POSTGRES_PASSWORD": cfg.DbPassword,
			"POSTGRES_DB":       cfg.DbName,
		},
		WaitingFor: wait.ForLog("database system is ready to accept connections").WithOccurrence(2).WithStartupTimeout(30 * time.Second),
	}

	container, err := testcontainers.GenericContainer(ctx, testcontainers.GenericContainerRequest{
		ContainerRequest: req,
		Started:          true,
	})
	if err != nil {
		return nil, err
	}

	host, err := container.Host(ctx)
	if err != nil {
		return nil, err
	}

	port, err := container.MappedPort(ctx, nat.Port("5432/tcp"))
	if err != nil {
		return nil, err
	}

	return &PostgresContainer{
		Container: container,
		Host:      host,
		Port:      port.Port(),
		Database:  cfg.DbName,
		Username:  cfg.DbUsername,
		Password:  cfg.DbPassword,
	}, nil
}

// DSN retorna a string de conexão para o PostgreSQL
func (c *PostgresContainer) DSN() string {
	return fmt.Sprintf("host=%s user=%s password=%s dbname=%s port=%s sslmode=disable",
		c.Host, c.Username, c.Password, c.Database, c.Port)
}
