package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type Organization struct {
	Id        uuid.UUID       `gorm:"type:uuid;primary_key" json:"id,omitempty"`
	Name      string          `gorm:"type:text" json:"name,omitempty"`
	Document  string          `gorm:"type:text" json:"document,omitempty"`
	AccountId uuid.UUID       `gorm:"type:uuid" json:"accountId,omitempty"`
	Account   *Account        `gorm:"foreignKey:AccountId" json:"account,omitempty"`
	People    []*Person       `gorm:"many2many:people_organizations;foreignKey:Id;joinForeignKey:OrganizationId;References:Id;joinReferences:PersonId" json:"people,omitempty"`
	Users     []*User         `gorm:"many2many:users_organizations;foreignKey:Id;joinForeignKey:OrganizationId;References:Id;joinReferences:UserId" json:"users,omitempty"`
	CreatedAt *time.Time      `json:"createdAt,omitempty"`
	UpdatedAt *time.Time      `json:"updatedAt,omitempty"`
	DeletedAt *gorm.DeletedAt `gorm:"index" json:"deletedAt,omitempty"`
}

func (Organization) TableName() string {
	return "organizations"
}

func (o *Organization) BeforeCreate(tx *gorm.DB) (err error) {
	o.Id = uuid.New()
	return
}
