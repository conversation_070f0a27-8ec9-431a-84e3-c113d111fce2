package main

import (
	"context"
	"digisac-go/worker/config"
	"digisac-go/worker/core/adapters"
	"digisac-go/worker/core/http"
	"digisac-go/worker/core/infrastructure"
	"digisac-go/worker/core/managers/service_manager"
	"digisac-go/worker/core/orm"
	"digisac-go/worker/core/repositories"
	"digisac-go/worker/core/services"
	"digisac-go/worker/core/services/storage/storage_adapter"
	"digisac-go/worker/core/utils"
	"sync"

	"go.uber.org/fx"
)

//go:generate go run github.com/ikateclab/gorm-tracked-updates/cmd/gorm-gen@v0.0.5 -package=./core/models
func main() {

	fx.New(
		config.Module,
		http.Module,
		repositories.Module,
		services.Module,
		service_manager.Module,
		adapters.Module,
		storage_adapter.Module,
		utils.Module,
		infrastructure.Module,
		fx.Provide(
			orm.SetupDb,
		),
		fx.Invoke(startServers),
	).Run()
}

func startServers(lc fx.Lifecycle, api *http.Api) {
	wg := &sync.WaitGroup{}

	lc.Append(fx.Hook{
		OnStart: func(cx context.Context) error {
			wg.Add(2)
			go api.Start(wg)
			return nil
		},
		OnStop: func(ctx context.Context) error {
			go api.Stop()
			wg.Wait()

			return nil
		},
	})
}
