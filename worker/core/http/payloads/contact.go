package payloads

import (
	"time"

	"github.com/google/uuid"
)

type LoadEarlierMessagesPayload struct {
	ContactId uuid.UUID  `json:"contactId" validate:"required"`
	ServiceId uuid.UUID  `json:"serviceId" validate:"required"`
	AccountId uuid.UUID  `json:"accountId" validate:"required"`
	Timestamp *time.Time `json:"timestamp" validate:"required"`
}

type LoadEarlierMessagesRequest struct {
	Payload  *LoadEarlierMessagesPayload `json:"payload" validate:"required"`
	Metadata *Metadata                   `json:"metadata"`
}
