package repositories

import (
	"sync"

	"digisac-go/worker/core/models"

	"gorm.io/gorm"
)

type ServiceRepository interface {
	Repository[models.Service]
}

type serviceRepository struct {
	*GormRepository[models.Service]
}

func NewServiceRepository(db *gorm.DB) ServiceRepository {
	return &serviceRepository{
		GormRepository: NewGormRepository[models.Service](db),
	}
}

var serviceRepositoryInstance ServiceRepository
var onceServiceRepository sync.Once

func GetServiceRepository(db *gorm.DB) ServiceRepository {
	onceServiceRepository.Do(func() {
		serviceRepositoryInstance = NewServiceRepository(db)
	})
	return serviceRepositoryInstance
}
