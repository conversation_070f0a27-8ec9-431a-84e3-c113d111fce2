package models

import (
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type BlockAction string

const (
	BlockActionBlock   BlockAction = "block"
	BlockActionUnblock BlockAction = "unblock"
)

type ContactBlockListControl struct {
	Id                 uuid.UUID         `gorm:"primaryKey" json:"id"`
	Reason             string            `gorm:"type:string;not null" json:"reason"`
	Status             BlockListStatus   `gorm:"type:string;not null;default:'ready'" json:"status"`
	Action             BlockAction       `gorm:"type:string;not null" json:"action"`
	RevertExceptions   bool              `gorm:"type:boolean;default:false" json:"revertExceptions"`
	UpdatedCount       *int              `gorm:"type:integer" json:"updatedCount"`
	ProcessCount       *int              `gorm:"type:integer" json:"processCount"`
	TotalCount         *int              `gorm:"type:integer" json:"totalCount"`
	ContactBlockListId uuid.UUID         `gorm:"type:uuid;not null" json:"contactBlockListId"`
	ContactBlockList   *ContactBlockList `gorm:"foreignKey:ContactBlockListId" json:"contactBlockList"`
	ServiceId          uuid.UUID         `gorm:"type:uuid;not null" json:"serviceId"`
	Service            *Service          `gorm:"foreignKey:ServiceId" json:"service"`
	UserId             uuid.UUID         `gorm:"type:uuid;not null" json:"userId"`
	User               *User             `gorm:"foreignKey:UserId" json:"user"`
	AccountId          uuid.UUID         `gorm:"type:uuid;not null" json:"accountId"`
	Account            *Account          `gorm:"foreignKey:AccountId" json:"account"`
	CreatedAt          time.Time         `gorm:"type:timestamptz;not null;default:now()" json:"createdAt"`
	UpdatedAt          time.Time         `gorm:"type:timestamptz;not null;default:now()" json:"updatedAt"`
	DeletedAt          *time.Time        `gorm:"index" json:"deletedAt"`
}

func (ContactBlockListControl) TableName() string {
	return "contact_block_lists_controls"
}

func (c *ContactBlockListControl) BeforeCreate(tx *gorm.DB) (err error) {
	c.Id = uuid.New()
	return
}
