//go:build integration
// +build integration

package redis_test

import (
	"context"
	"testing"
	"time"

	redisClient "github.com/redis/go-redis/v9"
	"github.com/stretchr/testify/suite"
	redisContainer "github.com/testcontainers/testcontainers-go/modules/redis"
)

type RedisClientSuite struct {
	suite.Suite
	redisContainer *redisContainer.RedisContainer
	redisClient    *redisClient.Client
}

func (suite *RedisClientSuite) SetupSuite() {
	ctx := context.Background()

	// Iniciar um contêiner Redis usando testcontainers
	container, err := redisContainer.Run(ctx, "redis:7-alpine")
	if err != nil {
		suite.T().Fatalf("Falha ao iniciar o contêiner Redis: %s", err)
	}

	// Armazenar o contêiner para limpeza posterior
	suite.redisContainer = container

	// Configurar o cliente Redis
	host, err := container.Host(ctx)
	if err != nil {
		suite.T().Fatalf("Falha ao obter o host do Redis: %s", err)
	}

	port, err := container.MappedPort(ctx, "6379/tcp")
	if err != nil {
		suite.T().Fatalf("Falha ao obter a porta do Redis: %s", err)
	}

	// Criar o cliente Redis
	suite.redisClient = redisClient.NewClient(&redisClient.Options{
		Addr: host + ":" + port.Port(),
	})
}

func (suite *RedisClientSuite) TearDownSuite() {
	// Fechar a conexão com o Redis
	if suite.redisClient != nil {
		suite.redisClient.Close()
	}

	// Parar e remover o contêiner Redis
	if suite.redisContainer != nil {
		ctx := context.Background()
		if err := suite.redisContainer.Terminate(ctx); err != nil {
			suite.T().Logf("Erro ao encerrar o contêiner Redis: %s", err)
		}
	}
}

func (suite *RedisClientSuite) TestSetAndGet() {
	ctx := context.Background()
	key := "test-key"
	value := "test-value"

	// Set
	err := suite.redisClient.Set(ctx, key, value, 1*time.Minute).Err()
	suite.Require().NoError(err)

	// Get
	result, err := suite.redisClient.Get(ctx, key).Result()
	suite.Require().NoError(err)
	suite.Require().Equal(value, result)
}

func (suite *RedisClientSuite) TestExpire() {
	ctx := context.Background()
	key := "expire-key"
	value := "expire-value"

	// Set com expiração curta
	err := suite.redisClient.Set(ctx, key, value, 1*time.Second).Err()
	suite.Require().NoError(err)

	// Verificar se existe
	exists, err := suite.redisClient.Exists(ctx, key).Result()
	suite.Require().NoError(err)
	suite.Require().Equal(int64(1), exists)

	// Esperar a expiração
	time.Sleep(2 * time.Second)

	// Verificar se expirou
	exists, err = suite.redisClient.Exists(ctx, key).Result()
	suite.Require().NoError(err)
	suite.Require().Equal(int64(0), exists)
}

func (suite *RedisClientSuite) TestDelete() {
	ctx := context.Background()
	key := "delete-key"
	value := "delete-value"

	// Set
	err := suite.redisClient.Set(ctx, key, value, 1*time.Minute).Err()
	suite.Require().NoError(err)

	// Delete
	err = suite.redisClient.Del(ctx, key).Err()
	suite.Require().NoError(err)

	// Verificar se foi deletado
	exists, err := suite.redisClient.Exists(ctx, key).Result()
	suite.Require().NoError(err)
	suite.Require().Equal(int64(0), exists)
}

func TestRedisClientSuite(t *testing.T) {
	suite.Run(t, new(RedisClientSuite))
}
