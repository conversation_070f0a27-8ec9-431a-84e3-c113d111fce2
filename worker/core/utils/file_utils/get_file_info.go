package file

import (
	"fmt"
	"io"
	"mime"
	"net/http"
	"path/filepath"
	"strings"
)

type GetFileInfoResponse struct {
	Mimetype  string
	Name      string
	Extension string
}

// Obtém informações da mídia pela url
func GetFileInfo(url string) (*GetFileInfoResponse, error) {
	mimetype, filename, err := getHeadFileInfo(url)

	if err != nil {
		return nil, err
	}

	if mimetype != "" && mimetype != "application/octet-stream" && filename != "" {
		ext, err := GetExtensionFromMimetype(mimetype)
		if err != nil {
			return nil, err
		}
		return &GetFileInfoResponse{
			Mimetype:  mimetype,
			Name:      filename,
			Extension: ext,
		}, nil
	}

	mimetype, filename, err = getGetFileInfo(url)

	if err != nil {
		return nil, err
	}

	ext, err := GetExtensionFromMimetype(mimetype)
	if err != nil {
		return nil, err
	}
	return &GetFileInfoResponse{
		Mimetype:  mimetype,
		Name:      filename,
		Extension: ext,
	}, nil
}
func GetExtensionFromMimetype(mimetype string) (extension string, err error) {
	extension = strings.Split(mimetype, "/")[1]
	return extension, nil
}

// Obtém informações pelo método HEAD
func getHeadFileInfo(url string) (mimetype string, filename string, err error) {
	resp, err := http.Head(url)
	if err != nil {
		return "", "", err
	}
	defer func() {
		if err := resp.Body.Close(); err != nil {
			fmt.Println("Error closing response body:", err)
		}
	}()

	mimetype = resp.Header.Get("Content-Type")
	contentDisposition := resp.Header.Get("Content-Disposition")

	if contentDisposition != "" {
		_, params, err := mime.ParseMediaType(contentDisposition)
		if err == nil {
			filename = params["filename"]
		}
	}

	return mimetype, strings.Split(filename, ".")[0], nil
}

// Obtém informações pelo método GET
func getGetFileInfo(url string) (mimetype string, filename string, err error) {
	resp, err := http.Get(url)
	if err != nil {
		return "", "", err
	}

	defer func() {
		if err := resp.Body.Close(); err != nil {
			fmt.Println("Error closing response body:", err)
		}
	}()

	mimetype = resp.Header.Get("Content-Type")

	if mimetype == "" || mimetype == "application/octet-stream" {
		data, err := io.ReadAll(resp.Body)
		if err != nil {
			return "", "", err
		}

		newMimetype := http.DetectContentType(data)

		if newMimetype != "" {
			mimetype = newMimetype
		}

		if mimetype == "" {
			mimetype = "application/octet-stream"
		}
	}

	contentDisposition := resp.Header.Get("Content-Disposition")

	if contentDisposition != "" {
		_, params, err := mime.ParseMediaType(contentDisposition)

		if err == nil {
			filename = params["filename"]
		}
	}

	if filename == "" {
		filename = filepath.Base(url)
		if filename == "" {
			ext := filepath.Ext(mimetype)
			filename = fmt.Sprintf("downloaded_file%s", ext)
		}
	}

	return mimetype, strings.Split(filename, ".")[0], nil
}
