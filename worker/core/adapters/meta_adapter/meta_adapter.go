package meta_adapter

import (
	"bytes"
	"context"
	"digisac-go/worker/config"
	"digisac-go/worker/core/adapters/adapter_types"
	"digisac-go/worker/core/adapters/base_waba_adapter"
	"digisac-go/worker/core/models"
	"digisac-go/worker/core/repositories"
	"digisac-go/worker/core/services/storage"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"net/url"

	"github.com/google/uuid"
)

// WebhookRequestBody representa o corpo da requisição para criar ou atualizar webhooks
type WebhookRequestBody struct {
	Type       string `json:"type"`
	DriverId   string `json:"driverId"`
	WebhookUrl string `json:"webhookUrl"`
}

// WebhookResponse representa a resposta da API de webhooks
type WebhookResponse struct {
	Id         uuid.UUID `json:"id"`
	Type       string    `json:"type"`
	DriverId   string    `json:"driverId"`
	WebhookUrl string    `json:"webhookUrl"`
}

// MediaResponse representa a resposta da API de mídia
type MediaResponse struct {
	MessagingProduct string `json:"messaging_product"`
	URL              string `json:"url"`
	MimeType         string `json:"mime_type"`
	Sha256           string `json:"sha256"`
	FileSize         int    `json:"file_size"`
	Id               string `json:"id"`
}

// UploadSessionResponse representa a resposta da API de criação de sessão de upload
type UploadSessionResponse struct {
	Id          string `json:"id"`
	FileOffset  int    `json:"file_offset"`
	VideoOffset int    `json:"video_offset"`
}

// UploadFileResponse representa a resposta da API de upload de arquivo
type UploadFileResponse struct {
	H string `json:"h"`
}

type MetaAdapter struct {
	*base_waba_adapter.BaseWabaAdapter
	apiUrl             string
	templateRepository repositories.WhatsappBusinessTemplateRepository
	fileRepository     repositories.FileRepository
	storageService     *storage.StorageService
}

func NewMetaAdapter(
	serviceRepository repositories.ServiceRepository,
	templateRepository repositories.WhatsappBusinessTemplateRepository,
	fileRepository repositories.FileRepository,
	config *config.Config,
	storageService *storage.StorageService,
) *MetaAdapter {
	metaAdapter := &MetaAdapter{
		apiUrl:             "https://graph.facebook.com/v22.0",
		templateRepository: templateRepository,
		fileRepository:     fileRepository,
		storageService:     storageService,
	}

	metaAdapter.BaseWabaAdapter = base_waba_adapter.NewBaseWabaAdapter(
		serviceRepository,
		templateRepository,
		fileRepository,
		storageService,
		config,
		metaAdapter)

	return metaAdapter
}

// GetMessageApiUrl retorna a URL da API para enviar mensagens
func (a *MetaAdapter) GetMessageApiUrl(ctx context.Context, service *models.Service) (string, error) {
	return fmt.Sprintf("%s/%s/messages", a.apiUrl, service.Data.DriverId), nil
}

func (a *MetaAdapter) GetMessageApiHeaders(ctx context.Context, service *models.Service) (map[string]string, error) {
	return map[string]string{
		"Content-Type":  "application/json",
		"Authorization": "Bearer " + service.InternalData.Token,
	}, nil
}

// GetTemplatesApiUrl retorna a URL da API para obter templates
func (a *MetaAdapter) GetTemplatesApiUrl(ctx context.Context, service *models.Service) (string, error) {
	return fmt.Sprintf("%s/%s/message_templates", a.apiUrl, service.Data.BusinessId), nil
}

// DeleteTemplateApiUrl retorna a URL da API para excluir templates
func (a *MetaAdapter) DeleteTemplateApiUrl(ctx context.Context, service *models.Service, templateName string) (string, error) {
	return fmt.Sprintf("%s/%s/message_templates?name=%s", a.apiUrl, service.Data.BusinessId, templateName), nil
}

// DeleteTemplateApiHeaders retorna os headers da API para excluir templates
func (a *MetaAdapter) DeleteTemplateApiHeaders(ctx context.Context, service *models.Service) (map[string]string, error) {
	return map[string]string{
		"Authorization": "Bearer " + service.InternalData.Token,
	}, nil
}

// buildDriverGatewayUrl constrói a URL para a API de webhooks
func (a *MetaAdapter) buildDriverGatewayUrl(ctx context.Context) (string, error) {
	if a.Config.DriversGatewayUrl == "" {
		return "", fmt.Errorf("driversGatewayUrl is empty")
	}

	return a.Config.DriversGatewayUrl + "/webhooks", nil
}

// getExistingWebhook busca webhooks existentes para o driver
func (a *MetaAdapter) getExistingWebhook(
	ctx context.Context,
	client *http.Client,
	driversGatewayUrl, driverId string,
) ([]*WebhookResponse, error) {

	if client == nil {
		return nil, fmt.Errorf("http client is nil")
	}

	if driverId == "" {
		return nil, fmt.Errorf("driver Id is empty")
	}

	if driversGatewayUrl == "" {
		return nil, fmt.Errorf("drivers gateway URL is empty")
	}

	// Montar os parâmetros como "where[driverId]" etc.
	query := url.Values{}
	query.Set("where[driverId]", driverId)
	query.Set("where[type]", "whatsapp")
	query.Set("limit", "1")

	// Construir URL final com parâmetros
	getUrl := fmt.Sprintf("%s?%s", driversGatewayUrl, query.Encode())

	req, err := http.NewRequestWithContext(ctx, http.MethodGet, getUrl, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create GET request: %w", err)
	}

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send GET request: %w", err)
	}

	defer func() {
		if err := resp.Body.Close(); err != nil {
			fmt.Println("Error closing response body:", err)
		}
	}()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("get request failed with status code %d: %s", resp.StatusCode, string(body))
	}

	var webhooks []*WebhookResponse
	if err := json.Unmarshal(body, &webhooks); err != nil {
		return nil, fmt.Errorf("failed to parse response body: %w", err)
	}

	return webhooks, nil
}

// updateWebhook atualiza um webhook existente
func (a *MetaAdapter) updateWebhook(ctx context.Context, client *http.Client, driversGatewayUrl string, webhookId uuid.UUID, driverId string, webhookUrl string, serviceId uuid.UUID) error {
	if client == nil {
		return fmt.Errorf("http client is nil")
	}
	if driversGatewayUrl == "" {
		return fmt.Errorf("drivers gateway URL is empty")
	}
	if webhookId == uuid.Nil {
		return fmt.Errorf("webhook Id is empty")
	}
	if driverId == "" {
		return fmt.Errorf("driver Id is empty")
	}
	if webhookUrl == "" {
		return fmt.Errorf("webhook URL is empty")
	}
	if serviceId == uuid.Nil {
		return fmt.Errorf("service Id is empty")
	}

	reqBody := &WebhookRequestBody{
		Type:       "whatsapp",
		DriverId:   driverId,
		WebhookUrl: webhookUrl,
	}

	jsonBody, err := json.Marshal(reqBody)
	if err != nil {
		return fmt.Errorf("failed to marshal request body: %w", err)
	}

	putUrl := fmt.Sprintf("%s/%s", driversGatewayUrl, webhookId)

	req, err := http.NewRequest("PUT", putUrl, bytes.NewBuffer(jsonBody))
	if err != nil {
		return fmt.Errorf("failed to create PUT request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send PUT request: %w", err)
	}

	defer func() {
		if err := resp.Body.Close(); err != nil {
			fmt.Println("Error closing response body:", err)
		}
	}()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("put request failed with status code %d: %s", resp.StatusCode, string(body))
	}

	slog.InfoContext(ctx, "Webhook updated successfully",
		slog.String("serviceId", serviceId.String()),
		slog.String("webhookId", webhookId.String()),
		slog.String("webhookUrl", webhookUrl))

	return nil
}

// createWebhook cria um novo webhook
func (a *MetaAdapter) createWebhook(ctx context.Context, client *http.Client, driversGatewayUrl, driverId string, webhookUrl string, serviceId uuid.UUID) error {
	if client == nil {
		return fmt.Errorf("http client is nil")
	}
	if driversGatewayUrl == "" {
		return fmt.Errorf("drivers gateway URL is empty")
	}
	if driverId == "" {
		return fmt.Errorf("driver Id is empty")
	}
	if webhookUrl == "" {
		return fmt.Errorf("webhook URL is empty")
	}
	if serviceId == uuid.Nil {
		return fmt.Errorf("service Id is empty")
	}

	reqBody := &WebhookRequestBody{
		Type:       "whatsapp",
		DriverId:   driverId,
		WebhookUrl: webhookUrl,
	}

	jsonBody, err := json.Marshal(reqBody)
	if err != nil {
		return fmt.Errorf("failed to marshal request body: %w", err)
	}

	req, err := http.NewRequest("POST", driversGatewayUrl, bytes.NewBuffer(jsonBody))
	if err != nil {
		return fmt.Errorf("failed to create POST request: %w", err)
	}
	req.Header.Set("Content-Type", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		return fmt.Errorf("failed to send POST request: %w", err)
	}

	defer func() {
		if err := resp.Body.Close(); err != nil {
			fmt.Println("Error closing response body:", err)
		}
	}()

	if resp.StatusCode != http.StatusOK && resp.StatusCode != http.StatusCreated {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("post request failed with status code %d: %s", resp.StatusCode, string(body))
	}

	slog.InfoContext(ctx, "Webhook created successfully",
		slog.String("serviceId", serviceId.String()),
		slog.String("webhookUrl", webhookUrl))

	return nil
}

// SetWebhook configura o webhook para o serviço
func (a *MetaAdapter) SetWebhook(ctx context.Context, serviceId uuid.UUID, url string) (err error) {
	slog.InfoContext(ctx, "SetWebhook called", slog.String("serviceId", serviceId.String()), slog.String("url", url))

	if serviceId == uuid.Nil {
		return fmt.Errorf("service Id is empty")
	}

	service, err := a.ServiceRepository.FindById(ctx, serviceId)
	if err != nil {
		return fmt.Errorf("failed to find service with serviceId %s: %w", serviceId, err)
	}

	driverId := service.Data.DriverId
	if driverId == "" {
		return fmt.Errorf("driver Id not found for service %s", serviceId)
	}

	client, err := a.GetClient(ctx, serviceId)
	if err != nil {
		return fmt.Errorf("failed to get HTTP client: %w", err)
	}

	driversGatewayUrl, err := a.buildDriverGatewayUrl(ctx)

	if err != nil {
		return fmt.Errorf("failed to build driver gateway URL: %w", err)
	}

	webhooks, err := a.getExistingWebhook(ctx, client, driversGatewayUrl, driverId)
	if err != nil {
		return fmt.Errorf("failed to get existing webhooks: %w", err)
	}

	if len(webhooks) > 0 {
		webhookId := webhooks[0].Id
		if webhookId == uuid.Nil {
			return fmt.Errorf("webhook Id not found in response")
		}

		return a.updateWebhook(ctx, client, driversGatewayUrl, webhookId, driverId, url, serviceId)
	}

	return a.createWebhook(ctx, client, driversGatewayUrl, driverId, url, serviceId)
}

func (a *MetaAdapter) DownloadMedia(ctx context.Context, serviceId uuid.UUID, mediaId string, mediaUrl string) (*adapter_types.DownloadMediaResponse, error) {
	client, err := a.GetClient(ctx, serviceId)
	if err != nil {
		return nil, fmt.Errorf("failed to get client for serviceId %s: %w", serviceId, err)
	}

	service, err := a.ServiceRepository.FindById(ctx, serviceId)
	if err != nil {
		return nil, fmt.Errorf("failed to find service with serviceId %s: %w", serviceId, err)
	}

	url := a.apiUrl + "/" + mediaId

	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create request: %w", err)
	}

	req.Header.Add("accept", "application/json")
	req.Header.Add("Authorization", "Bearer "+service.InternalData.Token)
	req.Header.Add("Content-Type", "application/json")

	resp, err := client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to send request: %w", err)
	}

	defer func() {
		if err := resp.Body.Close(); err != nil {
			fmt.Println("Error closing response body:", err)
		}
	}()

	data, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response body: %w", err)
	}

	if resp.StatusCode == 400 {
		return nil, fmt.Errorf("failed to download media: %s", string(data))
	}

	var mediaResponse *MediaResponse
	err = json.Unmarshal(data, &mediaResponse)
	if err != nil {
		return nil, fmt.Errorf("failed to unmarshal response body: %w", err)
	}

	req, err = http.NewRequest("GET", mediaResponse.URL, nil)
	if err != nil {
		return nil, fmt.Errorf("failed to create download request: %w", err)
	}

	req.Header.Add("Accept", "*/*")
	req.Header.Add("Authorization", "Bearer "+service.InternalData.Token)

	resp, err = client.Do(req)
	if err != nil {
		return nil, fmt.Errorf("failed to download media from url %s: %w", mediaResponse.URL, err)
	}

	defer func() {
		if err := resp.Body.Close(); err != nil {
			fmt.Println("Error closing response body:", err)
		}
	}()

	data, err = io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read media data: %w", err)
	}

	response := &adapter_types.DownloadMediaResponse{
		Type:   "stream",
		Stream: bytes.NewReader(data),
	}

	return response, nil
}

// https://developers.facebook.com/docs/graph-api/guides/upload
func (a *MetaAdapter) GetMediaExampleUrl(ctx context.Context, serviceId uuid.UUID, templateId uuid.UUID) (url string, err error) {
	slog.InfoContext(ctx, "GetMediaExampleUrl called",
		slog.String("serviceId", serviceId.String()),
		slog.String("templateId", templateId.String()))

	file, err := a.fileRepository.FindOne(ctx, repositories.WithQueryStruct(map[string]interface{}{
		"attachedId":   templateId,
		"attachedType": "hsm.file",
	}))

	if err != nil {
		slog.ErrorContext(ctx, "Failed to find file",
			slog.String("templateId", templateId.String()),
			slog.String("error", err.Error()))
		return "", fmt.Errorf("failed to find file: %w", err)
	}

	slog.InfoContext(ctx, "File found",
		slog.String("fileId", file.Id.String()),
		slog.String("fileName", file.Name),
		slog.String("mimeType", file.Mimetype))

	service, err := a.ServiceRepository.FindById(ctx, serviceId)
	if err != nil {
		return "", fmt.Errorf("failed to find service with serviceId %s: %w", serviceId, err)
	}

	client, err := a.GetClient(ctx, serviceId)
	if err != nil {
		return "", fmt.Errorf("failed to get HTTP client: %w", err)
	}

	stream, err := a.storageService.GetStream(ctx, file)
	if err != nil {
		return "", fmt.Errorf("failed to get stream: %w", err)
	}

	data, err := io.ReadAll(stream)
	if err != nil {
		return "", fmt.Errorf("failed to read file stream: %w", err)
	}
	fileSize := len(data)

	createSessionURL := fmt.Sprintf("%s/%s/uploads?file_name=%s&file_length=%d&file_type=%s&access_token=%s",
		a.apiUrl,
		a.Config.FacebookAppId,
		file.Name,
		fileSize,
		file.Mimetype,
		service.InternalData.Token,
	)

	slog.InfoContext(ctx, "Creating upload session",
		slog.String("url", createSessionURL),
		slog.Int("fileSize", fileSize))

	sessionReq, err := http.NewRequest("POST", createSessionURL, nil)
	if err != nil {
		slog.ErrorContext(ctx, "Failed to create session request", slog.String("error", err.Error()))
		return "", fmt.Errorf("failed to create session request: %w", err)
	}

	sessionResp, err := client.Do(sessionReq)
	if err != nil {
		return "", fmt.Errorf("failed to send session request: %w", err)
	}
	defer func() {
		if err := sessionResp.Body.Close(); err != nil {
			fmt.Println("Error closing response body:", err)
		}
	}()

	sessionRespBody, err := io.ReadAll(sessionResp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read session response body: %w", err)
	}

	if sessionResp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("session creation failed with status code %d: %s", sessionResp.StatusCode, string(sessionRespBody))
	}

	var sessionResponse *UploadSessionResponse
	if err := json.Unmarshal(sessionRespBody, &sessionResponse); err != nil {
		slog.ErrorContext(ctx, "Failed to unmarshal session response",
			slog.String("response", string(sessionRespBody)),
			slog.String("error", err.Error()))
		return "", fmt.Errorf("failed to unmarshal session response: %w", err)
	}

	slog.InfoContext(ctx, "Session created successfully",
		slog.String("sessionId", sessionResponse.Id),
		slog.Int("fileOffset", sessionResponse.FileOffset))

	uploadURL := fmt.Sprintf("%s/%s", a.apiUrl, sessionResponse.Id)
	slog.InfoContext(ctx, "Uploading file to session", slog.String("uploadURL", uploadURL))

	uploadReq, err := http.NewRequest("POST", uploadURL, bytes.NewReader(data))
	if err != nil {
		return "", fmt.Errorf("failed to create upload request: %w", err)
	}

	uploadReq.Header.Set("Authorization", "OAuth "+service.InternalData.Token)
	uploadReq.Header.Set("file_offset", "0")

	uploadResp, err := client.Do(uploadReq)
	if err != nil {
		return "", fmt.Errorf("failed to send upload request: %w", err)
	}

	defer func() {
		if err := uploadResp.Body.Close(); err != nil {
			fmt.Println("Error closing response body:", err)
		}
	}()

	uploadRespBody, err := io.ReadAll(uploadResp.Body)
	if err != nil {
		return "", fmt.Errorf("failed to read upload response body: %w", err)
	}

	if uploadResp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("upload failed with status code %d: %s", uploadResp.StatusCode, string(uploadRespBody))
	}

	var uploadResponse *UploadFileResponse
	if err := json.Unmarshal(uploadRespBody, &uploadResponse); err != nil {
		slog.ErrorContext(ctx, "Failed to unmarshal upload response",
			slog.String("response", string(uploadRespBody)),
			slog.String("error", err.Error()))
		return "", fmt.Errorf("failed to unmarshal upload response: %w", err)
	}

	if uploadResponse.H == "" {
		slog.ErrorContext(ctx, "Upload response did not contain a file handle",
			slog.String("response", string(uploadRespBody)))
		return "", fmt.Errorf("upload response did not contain a file handle")
	}

	slog.InfoContext(ctx, "Successfully uploaded file to Meta",
		slog.String("serviceId", serviceId.String()),
		slog.String("templateId", templateId.String()),
		slog.String("fileHandle", uploadResponse.H))

	return uploadResponse.H, nil
}
