package grpc

import (
	"context"
	api "digisac-go/common/grpc/api"
	"digisac-go/worker/config"
	"log/slog"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/metadata"
)

// NewWhatsappClient creates a new gRPC client with authentication
func NewWhatsappClient(config *config.Config) api.PublicServiceClient {
	token := config.WhatsappApiToken

	authInterceptor := func(
		ctx context.Context,
		method string,
		req interface{},
		reply interface{},
		cc *grpc.ClientConn,
		invoker grpc.UnaryInvoker,
		opts ...grpc.CallOption,
	) error {
		md := metadata.Pairs("authorization", token)
		ctx = metadata.NewOutgoingContext(ctx, md)

		return invoker(ctx, method, req, reply, cc, opts...)
	}

	conn, err := grpc.NewClient(
		config.WhatsappApiGrpcAddress,
		grpc.WithTransportCredentials(insecure.NewCredentials()),
		grpc.WithUnaryInterceptor(authInterceptor),
	)

	if err != nil {
		slog.DebugContext(context.Background(), "did not connect: %v", slog.String("err", err.Error()))
		panic(err)
	}

	return api.NewPublicServiceClient(conn)
}
