package url

import (
	"regexp"
)

// Regular expression for matching URLs
// This pattern matches URLs with or without protocol
var urlRegex = regexp.MustCompile(`(?:(?:https?|ftp):\/\/)?(?:www\.)?[-a-zA-Z0-9@:%._\+~#=]{1,256}\.[a-zA-Z0-9()]{1,6}\b(?:[-a-zA-Z0-9()@:%_\+.~#?&//=]*)`)

// ExtractURLs extracts all URLs from a given text
//
// Parameters:
//   - text: The text to extract URLs from
//
// Returns:
//   - []string: A slice of extracted URLs
func ExtractURLs(text string) []string {
	if text == "" {
		return []string{}
	}

	// Find all matches of the URL regex in the text
	matches := urlRegex.FindAllString(text, -1)

	// If no matches are found, return an empty slice
	if matches == nil {
		return []string{}
	}

	return matches
}
