package repositories

import (
	"digisac-go/worker/core/models"

	"gorm.io/gorm"
)

type WhatsappBusinessTemplateHistoryRepository interface {
	Repository[models.WhatsappBusinessTemplateHistory]
}

type whatsappBusinessTemplateHistoryRepository struct {
	*GormRepository[models.WhatsappBusinessTemplateHistory]
}

func NewWhatsappBusinessTemplateHistoryRepository(db *gorm.DB) WhatsappBusinessTemplateHistoryRepository {
	return &whatsappBusinessTemplateHistoryRepository{
		GormRepository: NewGormRepository[models.WhatsappBusinessTemplateHistory](db),
	}
}
