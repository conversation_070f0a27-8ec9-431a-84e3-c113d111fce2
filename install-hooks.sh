#!/bin/sh -x

# 📝 Este script configura hooks do Git para padronizar commits usando commitizen.
# Ele é executado automaticamente no ambiente de desenvolvimento, sem necessidade de comandos extras.
# O objetivo é garantir que todos os desenvolvedores sigam os padrões definidos para mensagens de commit.

# 📂 Diretórios importantes
HOOKS_DIR="/app/.git/hooks"          # Pasta dos hooks do Git
BIN_DIR="/usr/local/bin"             # Pasta de binários no container
HOST_BIN_DIR="./commitizen-bin"      # Pasta de binários no host (volume compartilhado)
COMMITIZEN_BIN="$HOST_BIN_DIR/commitizen-go"  # Caminho do binário do commitizen-go

# 🔍 Verifica se o commitizen-go já está instalado no volume compartilhado
if [ ! -x "$BIN_DIR/commitizen-go" ]; then
    echo "⬇️  Instalando commitizen-go no volume compartilhado..."

    # Instala o commitizen-go no diretório de binários do container
    GOBIN="$BIN_DIR" go install github.com/lintingzhen/commitizen-go@latest

    # 🔒 Garante que o binário está acessível e executável
    chmod +x "$BIN_DIR/commitizen-go"

    # Se o diretório no host existir, também garante que o binário está acessível
    if [ -d "$HOST_BIN_DIR" ]; then
        chmod +x "$HOST_BIN_DIR/commitizen-go"
    fi
else
    echo "✅ commitizen-go já está instalado."
fi

echo "🛠️  Criando hook de prepare-commit-msg..."

# 🔍 Verifica se o diretório de hooks do Git existe
if [ ! -d "$HOOKS_DIR" ]; then
    echo "❌ Diretório de hooks do Git não encontrado: $HOOKS_DIR"
    exit 1
fi

# 🔍 Verifica se o binário do commitizen-go existe no host
if [ ! -x "$COMMITIZEN_BIN" ]; then
    echo "❌ Binário do commitizen-go não encontrado ou não executável: $COMMITIZEN_BIN"
    exit 1
fi

# 📝 Cria o hook prepare-commit-msg
cat > "$HOOKS_DIR/prepare-commit-msg" <<EOF
#!/bin/bash
# Executa o commitizen-go e garante que o terminal seja fechado corretamente
if [ -t 1 ]; then
    "$COMMITIZEN_BIN" < /dev/tty
fi
exit 0
EOF

# 🔒 Garante que o hook tem permissão de execução
chmod +x "$HOOKS_DIR/prepare-commit-msg"

echo "✅ Hook prepare-commit-msg instalado com sucesso!"

# Remover qualquer hook pre-commit existente para evitar conflitos
if [ -f "$HOOKS_DIR/pre-commit" ]; then
    echo "�️  Removendo hook pre-commit existente..."
    rm "$HOOKS_DIR/pre-commit"
    echo "✅ Hook pre-commit removido com sucesso!"
fi

echo "✅ Configuração de hooks concluída!"
echo "💡 Formatação, linting e testes serão executados pela pipeline CI/CD"