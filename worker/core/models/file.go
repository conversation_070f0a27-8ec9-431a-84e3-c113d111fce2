package models

import (
	"fmt"
	"time"

	"github.com/google/uuid"
	"gorm.io/gorm"
)

type StorageType string

const (
	S3              StorageType = "s3"
	Oracle          StorageType = "oracle"
	FS              StorageType = "fs"
	OracleFallback  StorageType = "oracleFallback"
	WpLvS3          StorageType = "wpLvS3"
	WpBrowserDataS3 StorageType = "wpBrowserDataS3"
	ExportsS3       StorageType = "exportsS3"
)

type File struct {
	Id             uuid.UUID   `gorm:"type:uuid;primary_key" json:"id,omitempty"`
	Name           string      `gorm:"type:text" json:"name,omitempty"`
	Checksum       string      `gorm:"type:text" json:"checksum,omitempty"`
	Mimetype       string      `gorm:"type:string;not null" json:"mimetype,omitempty"`
	Extension      string      `gorm:"type:string;not null" json:"extension,omitempty"`
	AttachedId     uuid.UUID   `gorm:"type:uuid;default:null" json:"attachedId,omitempty"`
	AttachedType   string      `gorm:"type:string" json:"attachedType,omitempty"`
	Storage        StorageType `gorm:"type:string;default:s3;not null" json:"storage,omitempty"`
	IsEncrypted    bool        `gorm:"default:false;not null" json:"isEncrypted,omitempty"`
	IV             string      `gorm:"type:string" json:"iv,omitempty"`
	AccountId      uuid.UUID   `gorm:"type:uuid" json:"accountId,omitempty"`
	Account        *Account    `gorm:"foreignKey:AccountId" json:"account,omitempty"`
	CreatedAt      *time.Time  `json:"createdAt,omitempty"`
	UpdatedAt      *time.Time  `json:"updatedAt,omitempty"`
	Filename       string      `gorm:"-" json:"filename,omitempty"`
	PublicFilename string      `gorm:"-" json:"publicFilename,omitempty"`
	Filepath       string      `gorm:"-" json:"filepath,omitempty"`
}

func (f *File) TableName() string {
	return "files"
}

func (f *File) AfterCreate(tx *gorm.DB) (err error) {
	return f.AfterFind(tx)
}

func (f *File) AfterFind(tx *gorm.DB) (err error) {
	f.Filename = fmt.Sprintf("%s.%s", f.Id, f.Extension)

	if f.Mimetype == "image/*" {
		f.PublicFilename = fmt.Sprintf("%s.jpeg", f.Id)
	} else if f.Name != "" {
		f.PublicFilename = f.Name
	} else {
		f.PublicFilename = f.Filename
	}

	f.Filepath = fmt.Sprintf("%s/%s", f.AccountId, f.Filename)
	return nil
}

func (f *File) BeforeCreate(tx *gorm.DB) (err error) {
	f.Id = uuid.New()
	return
}
