package adapters

import (
	"digisac-go/worker/core/adapters/dialog360_adapter"
	"digisac-go/worker/core/adapters/gupshup_adapter"
	"digisac-go/worker/core/adapters/meta_adapter"
	"digisac-go/worker/core/adapters/telegram_adapter"
	"digisac-go/worker/core/adapters/whatsapp_adapter"

	"go.uber.org/fx"
)

var Module = fx.Module("adapters",
	fx.Provide(
		meta_adapter.NewMetaAdapter,
		dialog360_adapter.NewDialog360Adapter,
		gupshup_adapter.NewGupshupAdapter,
		telegram_adapter.NewTelegramAdapter,
		whatsapp_adapter.NewWhatsappAdapter,
	),
)
