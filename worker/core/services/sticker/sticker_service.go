package sticker

import (
	"context"
	"fmt"
	"log/slog"
	"time"

	"digisac-go/worker/core/models"
	"digisac-go/worker/core/repositories"

	"github.com/google/uuid"
	gormrepository "github.com/ikateclab/gorm-repository"
)

type StickerService struct {
	stickerUserRepository repositories.StickerUserRepository
}

func NewStickerService(
	stickerUserRepository repositories.StickerUserRepository,
) *StickerService {
	return &StickerService{
		stickerUserRepository: stickerUserRepository,
	}
}

// ProcessStickerUsage records sticker usage by a user
func (s *StickerService) ProcessStickerUsage(ctx context.Context, stickerId uuid.UUID, userId uuid.UUID, accountId uuid.UUID, tx *gormrepository.Tx) error {
	if stickerId == uuid.Nil || userId == uuid.Nil || accountId == uuid.Nil {
		return fmt.Errorf("stickerId, userId, and accountId are required")
	}

	// Find existing sticker user record
	stickerUser, err := s.stickerUserRepository.FindOne(ctx,
		repositories.WithTx(tx),
		repositories.WithQueryStruct(map[string]interface{}{
			"stickerId": stickerId,
			"userId":    userId,
			"accountId": accountId,
		}),
	)

	now := time.Now()

	// If record exists, update lastSendAt
	if err == nil && stickerUser != nil {
		stickerUser.LastSendAt = &now
		err = s.stickerUserRepository.Save(ctx, stickerUser, repositories.WithTx(tx))
		if err != nil {
			slog.ErrorContext(ctx, "Failed to update sticker user record",
				slog.String("stickerId", stickerId.String()),
				slog.String("userId", userId.String()),
				slog.String("error", err.Error()))
			return fmt.Errorf("failed to update sticker user record: %w", err)
		}
		return nil
	}

	// If record doesn't exist, create a new one
	stickerUser = &models.StickerUser{
		StickerId:  stickerId,
		UserId:     userId,
		AccountId:  accountId,
		LastSendAt: &now,
	}

	err = s.stickerUserRepository.Create(ctx, stickerUser, repositories.WithTx(tx))
	if err != nil {
		slog.ErrorContext(ctx, "Failed to create sticker user record",
			slog.String("stickerId", stickerId.String()),
			slog.String("userId", userId.String()),
			slog.String("error", err.Error()))
		return fmt.Errorf("failed to create sticker user record: %w", err)
	}

	return nil
}
