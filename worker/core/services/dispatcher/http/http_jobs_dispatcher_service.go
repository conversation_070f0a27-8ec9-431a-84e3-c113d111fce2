package http

import (
	"bytes"
	"context"
	"digisac-go/worker/config"
	"encoding/json"
	"fmt"
	"net/http"
)

type HttpJobsDispatcherService struct {
	httpClient *http.Client
	config     *config.Config
}

type HttpJobsDispatcherDispatchOptions struct {
	UseWorkersNodeJs bool
}

// NewHttpJobsDispatcherService cria uma nova instância de HttpJobsDispatcherService
func NewHttpJobsDispatcherService(config *config.Config) *HttpJobsDispatcherService {
	return &HttpJobsDispatcherService{
		httpClient: http.DefaultClient,
		config:     config,
	}
}

func (h *HttpJobsDispatcherService) Dispatch(ctx context.Context, jobName string, payload map[string]interface{}, options HttpJobsDispatcherDispatchOptions) (map[string]interface{}, error) {
	url := h.config.WorkersGoUrl + "/run/" + jobName

	if options.UseWorkersNodeJs {
		url = h.config.WorkersNodeUrl + "/run/" + jobName
	}

	payloadJson, err := json.Marshal(payload)

	if err != nil {
		return nil, err
	}

	requestBody := bytes.NewBuffer(payloadJson)

	httpRequest, err := http.NewRequestWithContext(ctx, "POST", url, requestBody)

	if err != nil {
		return nil, err
	}

	httpRequest.Header.Set("Content-Type", "application/json")

	response, err := h.httpClient.Do(httpRequest)

	if err != nil {
		return nil, err
	}

	defer func() {
		if err := response.Body.Close(); err != nil {
			fmt.Println("Error closing response body:", err)
		}
	}()

	if response.StatusCode != 200 {
		return nil, fmt.Errorf("unexpected status code: %d", response.StatusCode)
	}

	var responseBody map[string]interface{}

	err = json.NewDecoder(response.Body).Decode(&responseBody)

	if err != nil {
		return nil, err
	}

	return responseBody, nil
}

func (h *HttpJobsDispatcherService) DispatchToMockDriver(ctx context.Context, jobName string, payload map[string]interface{}) (map[string]interface{}, error) {
	url := h.config.MockDriverUrl + jobName

	payloadJson, err := json.Marshal(payload)

	if err != nil {
		return nil, err
	}

	requestBody := bytes.NewBuffer(payloadJson)

	httpRequest, err := http.NewRequestWithContext(ctx, "POST", url, requestBody)

	if err != nil {
		return nil, err
	}

	httpRequest.Header.Set("Content-Type", "application/json")

	response, err := h.httpClient.Do(httpRequest)

	if err != nil {
		return nil, err
	}

	defer func() {
		if err := response.Body.Close(); err != nil {
			fmt.Println("Error closing response body:", err)
		}
	}()

	if response.StatusCode != 200 {
		return nil, fmt.Errorf("unexpected status code: %d", response.StatusCode)
	}

	var responseBody map[string]interface{}

	err = json.NewDecoder(response.Body).Decode(&responseBody)

	if err != nil {
		return nil, err
	}

	return responseBody, nil
}
