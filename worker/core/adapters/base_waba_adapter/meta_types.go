package base_waba_adapter

// Tipos para a resposta da API Meta/360Dialog

// MetaTemplateStatusEnum representa os possíveis status de um template
type MetaTemplateStatusEnum string

const (
	MetaTemplateStatusApproved MetaTemplateStatusEnum = "APPROVED"
	MetaTemplateStatusPending  MetaTemplateStatusEnum = "PENDING"
	MetaTemplateStatusRejected MetaTemplateStatusEnum = "REJECTED"
	MetaTemplateStatusFailed   MetaTemplateStatusEnum = "FAILED"
	MetaTemplateStatusSending  MetaTemplateStatusEnum = "SENDING"
)

// MetaTemplateCategoryEnum representa as categorias de templates disponíveis
type MetaTemplateCategoryEnum string

const (
	MetaTemplateCategoryAuthentication MetaTemplateCategoryEnum = "AUTHENTICATION"
	MetaTemplateCategoryMarketing      MetaTemplateCategoryEnum = "MARKETING"
	MetaTemplateCategoryUtility        MetaTemplateCategoryEnum = "UTILITY"
)

// MetaTemplateResponse representa a resposta completa da API de templates da Meta/360Dialog
type MetaTemplateResponse struct {
	Data   []*MetaTemplate `json:"data,omitempty"`
	Paging *MetaPaging     `json:"paging,omitempty"`
	// Para 360Dialog
	Count         *int            `json:"count,omitempty"`
	Filters       map[string]any  `json:"filters,omitempty"`
	Limit         *int            `json:"limit,omitempty"`
	Offset        *int            `json:"offset,omitempty"`
	Sort          []string        `json:"sort,omitempty"`
	Total         *int            `json:"total,omitempty"`
	WabaTemplates []*MetaTemplate `json:"waba_templates,omitempty"`
}

// MetaCreateTemplateRequest representa o payload para criação de template na API Meta/360Dialog
type MetaCreateTemplateRequest struct {
	Name                string                  `json:"name"`
	Language            string                  `json:"language"`
	Category            string                  `json:"category"`
	AllowCategoryChange bool                    `json:"allow_category_change"`
	Components          []*MetaComponentRequest `json:"components"`
}

type MetaOTPType string

const (
	MetaOTPTypeCopyCode MetaOTPType = "COPY_CODE"
	MetaOTPTypeOneTap   MetaOTPType = "ONE_TAP"
	MetaOTPTypeZeroTap  MetaOTPType = "ZERO_TAP"
)

// MetaComponentRequest representa um componente no payload de criação de template
type MetaComponentRequest struct {
	Type                      string               `json:"type"`
	Format                    string               `json:"format,omitempty"`
	Text                      string               `json:"text,omitempty"` // Obrigatório para todos os componentes que têm texto
	Example                   *MetaExampleRequest  `json:"example,omitempty"`
	Buttons                   []*MetaButtonRequest `json:"buttons,omitempty"`
	AddSecurityRecommendation bool                 `json:"add_security_recommendation,omitempty"`
	CodeExpirationMinutes     int                  `json:"code_expiration_minutes,omitempty"`
}

// MetaExampleRequest representa exemplos para um componente no payload de criação de template
type MetaExampleRequest struct {
	HeaderText   []string   `json:"header_text,omitempty"`
	BodyText     [][]string `json:"body_text,omitempty"`
	HeaderHandle []string   `json:"header_handle,omitempty"`
}

type MetaButtonTypeRequestEnum string

const (
	MetaButtonTypeRequestEnumOTP MetaButtonTypeRequestEnum = "OTP"
)

// MetaButtonRequest representa um botão no payload de criação de template
type MetaButtonRequest struct {
	Type                 MetaButtonTypeRequestEnum `json:"type"`
	Text                 string                    `json:"text"`
	URL                  string                    `json:"url,omitempty"`
	PhoneNumber          string                    `json:"phone_number,omitempty"`
	Example              []string                  `json:"example,omitempty"`
	OtpType              MetaOTPType               `json:"otp_type,omitempty"`
	AutofillText         string                    `json:"autofill_text,omitempty"`           // Para ONE_TAP e ZERO_TAP
	PackageName          string                    `json:"package_name,omitempty"`            // Para ONE_TAP e ZERO_TAP
	SignatureHash        string                    `json:"signature_hash,omitempty"`          // Para ONE_TAP e ZERO_TAP
	ZeroTapTermsAccepted bool                      `json:"zero_tap_terms_accepted,omitempty"` // Para ZERO_TAP
}

// MetaCreateTemplateResponse representa a resposta da API ao criar um template
type MetaCreateTemplateResponse struct {
	Id             string                   `json:"id"`
	Status         MetaTemplateStatusEnum   `json:"status"`
	Category       MetaTemplateCategoryEnum `json:"category"`
	Name           string                   `json:"name"`
	Language       string                   `json:"language"`
	Namespace      string                   `json:"namespace,omitempty"`
	RejectedReason string                   `json:"rejected_reason,omitempty"`
}

// MetaTemplate representa um template na resposta da API Meta/360Dialog
type MetaTemplate struct {
	Name            string            `json:"name"`
	Language        string            `json:"language"`
	Status          string            `json:"status"`   // Mantido como string para compatibilidade com a API
	Category        string            `json:"category"` // Mantido como string para compatibilidade com a API
	Id              string            `json:"id"`
	Components      []*MetaComponent  `json:"components"`
	Quality         string            `json:"quality,omitempty"`
	RejectedReason  string            `json:"rejected_reason,omitempty"`
	Namespace       string            `json:"namespace,omitempty"`
	ExternalId      string            `json:"external_id,omitempty"`
	CreatedAt       string            `json:"created_at,omitempty"`
	ModifiedAt      string            `json:"modified_at,omitempty"`
	CreatedBy       *MetaUser         `json:"created_by,omitempty"`
	ModifiedBy      *MetaUser         `json:"modified_by,omitempty"`
	PartnerId       string            `json:"partner_id,omitempty"`
	QualityScore    *MetaQualityScore `json:"quality_score,omitempty"`
	UpdatedExternal bool              `json:"updated_external,omitempty"`
	WabaAccountId   string            `json:"waba_account_id,omitempty"`
}

// MetaQualityScore representa a pontuação de qualidade de um template
type MetaQualityScore struct {
	Score   string   `json:"score,omitempty"`
	Reasons []string `json:"reasons,omitempty"`
}

// MetaUser representa um usuário que criou ou modificou um template
type MetaUser struct {
	UserId   string `json:"user_id,omitempty"`
	UserName string `json:"user_name,omitempty"`
}

// MetaComponent representa um componente de template na resposta da API Meta/360Dialog
type MetaComponent struct {
	Type    string        `json:"type"`
	Format  string        `json:"format,omitempty"`
	Text    string        `json:"text,omitempty"`
	Example *MetaExample  `json:"example,omitempty"`
	Buttons []*MetaButton `json:"buttons,omitempty"`
}

// MetaExample representa exemplos para um componente de template
type MetaExample struct {
	HeaderText   []string   `json:"header_text,omitempty"`
	BodyText     [][]string `json:"body_text,omitempty"`
	HeaderHandle []string   `json:"header_handle,omitempty"`
}

// MetaButton representa um botão em um componente de template
type MetaButton struct {
	Type        string `json:"type"`
	Text        string `json:"text"`
	URL         string `json:"url,omitempty"`
	PhoneNumber string `json:"phone_number,omitempty"`
}

// MetaPaging representa informações de paginação na resposta da API Meta
type MetaPaging struct {
	Cursors *MetaCursors `json:"cursors,omitempty"`
}

// MetaCursors representa cursores para paginação
type MetaCursors struct {
	Before string `json:"before,omitempty"`
	After  string `json:"after,omitempty"`
}
