// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.36.6
// 	protoc        v3.21.12
// source: common/grpc/api/proto/whatsapp_public_api.proto

package pb

import (
	common "digisac-go/common/grpc/common"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	reflect "reflect"
	unsafe "unsafe"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

var File_common_grpc_api_proto_whatsapp_public_api_proto protoreflect.FileDescriptor

const file_common_grpc_api_proto_whatsapp_public_api_proto_rawDesc = "" +
	"\n" +
	"/common/grpc/api/proto/whatsapp_public_api.proto\x12\x03api\x1a'common/grpc/common/proto/whatsapp.proto\x1a&common/grpc/common/proto/service.proto\x1a&common/grpc/common/proto/account.proto\x1a\x1bgoogle/protobuf/empty.proto2\xd5\f\n" +
	"\rPublicService\x12F\n" +
	"\vSendMessage\x12\x1a.common.SendMessageRequest\x1a\x1b.common.SendMessageResponse\x12L\n" +
	"\rRevokeMessage\x12\x1c.common.RevokeMessageRequest\x1a\x1d.common.RevokeMessageResponse\x12O\n" +
	"\x0eForwardMessage\x12\x1d.common.ForwardMessageRequest\x1a\x1e.common.ForwardMessageResponse\x12d\n" +
	"\x15SendReactionToMessage\x12$.common.SendReactionToMessageRequest\x1a%.common.SendReactionToMessageResponse\x12j\n" +
	"\x17SendVCardContactMessage\x12&.common.SendVCardContactMessageRequest\x1a'.common.SendVCardContactMessageResponse\x12F\n" +
	"\vCreateGroup\x12\x1a.common.CreateGroupRequest\x1a\x1b.common.CreateGroupResponse\x12O\n" +
	"\x14AddGroupParticipants\x12\x1a.common.CreateGroupRequest\x1a\x1b.common.CreateGroupResponse\x12R\n" +
	"\x17RemoveGroupParticipants\x12\x1a.common.CreateGroupRequest\x1a\x1b.common.CreateGroupResponse\x12v\n" +
	"\x1bLoadEarlierMessagesTillDate\x12*.common.LoadEarlierMessagesTillDateRequest\x1a+.common.LoadEarlierMessagesTillDateResponse\x126\n" +
	"\x05Start\x12\x15.common.ActionPayload\x1a\x16.common.ActionResponse\x125\n" +
	"\x04Stop\x12\x15.common.ActionPayload\x1a\x16.common.ActionResponse\x128\n" +
	"\aRestart\x12\x15.common.ActionPayload\x1a\x16.common.ActionResponse\x127\n" +
	"\x06Logout\x12\x15.common.ActionPayload\x1a\x16.common.ActionResponse\x129\n" +
	"\bTakeover\x12\x15.common.ActionPayload\x1a\x16.common.ActionResponse\x12=\n" +
	"\n" +
	"GetAccount\x12\x16.google.protobuf.Empty\x1a\x17.common.AccountResponse\x12P\n" +
	"\x11SetAccountWebhook\x12#.common.SetAccountWebhookUrlRequest\x1a\x16.google.protobuf.Empty\x12L\n" +
	"\rCreateService\x12\x1c.common.CreateServiceRequest\x1a\x1d.common.CreateServiceResponse\x12C\n" +
	"\n" +
	"GetService\x12\x19.common.GetServiceRequest\x1a\x1a.common.GetServiceResponse\x12I\n" +
	"\fListServices\x12\x1b.common.ListServicesRequest\x1a\x1c.common.ListServicesResponse\x12L\n" +
	"\rUpdateService\x12\x1c.common.UpdateServiceRequest\x1a\x1d.common.UpdateServiceResponse\x12L\n" +
	"\rDeleteService\x12\x1c.common.DeleteServiceRequest\x1a\x1d.common.DeleteServiceResponseB\x1fZ\x1ddigisac-go/common/grpc/api;pbb\x06proto3"

var file_common_grpc_api_proto_whatsapp_public_api_proto_goTypes = []any{
	(*common.SendMessageRequest)(nil),                  // 0: common.SendMessageRequest
	(*common.RevokeMessageRequest)(nil),                // 1: common.RevokeMessageRequest
	(*common.ForwardMessageRequest)(nil),               // 2: common.ForwardMessageRequest
	(*common.SendReactionToMessageRequest)(nil),        // 3: common.SendReactionToMessageRequest
	(*common.SendVCardContactMessageRequest)(nil),      // 4: common.SendVCardContactMessageRequest
	(*common.CreateGroupRequest)(nil),                  // 5: common.CreateGroupRequest
	(*common.LoadEarlierMessagesTillDateRequest)(nil),  // 6: common.LoadEarlierMessagesTillDateRequest
	(*common.ActionPayload)(nil),                       // 7: common.ActionPayload
	(*emptypb.Empty)(nil),                              // 8: google.protobuf.Empty
	(*common.SetAccountWebhookUrlRequest)(nil),         // 9: common.SetAccountWebhookUrlRequest
	(*common.CreateServiceRequest)(nil),                // 10: common.CreateServiceRequest
	(*common.GetServiceRequest)(nil),                   // 11: common.GetServiceRequest
	(*common.ListServicesRequest)(nil),                 // 12: common.ListServicesRequest
	(*common.UpdateServiceRequest)(nil),                // 13: common.UpdateServiceRequest
	(*common.DeleteServiceRequest)(nil),                // 14: common.DeleteServiceRequest
	(*common.SendMessageResponse)(nil),                 // 15: common.SendMessageResponse
	(*common.RevokeMessageResponse)(nil),               // 16: common.RevokeMessageResponse
	(*common.ForwardMessageResponse)(nil),              // 17: common.ForwardMessageResponse
	(*common.SendReactionToMessageResponse)(nil),       // 18: common.SendReactionToMessageResponse
	(*common.SendVCardContactMessageResponse)(nil),     // 19: common.SendVCardContactMessageResponse
	(*common.CreateGroupResponse)(nil),                 // 20: common.CreateGroupResponse
	(*common.LoadEarlierMessagesTillDateResponse)(nil), // 21: common.LoadEarlierMessagesTillDateResponse
	(*common.ActionResponse)(nil),                      // 22: common.ActionResponse
	(*common.AccountResponse)(nil),                     // 23: common.AccountResponse
	(*common.CreateServiceResponse)(nil),               // 24: common.CreateServiceResponse
	(*common.GetServiceResponse)(nil),                  // 25: common.GetServiceResponse
	(*common.ListServicesResponse)(nil),                // 26: common.ListServicesResponse
	(*common.UpdateServiceResponse)(nil),               // 27: common.UpdateServiceResponse
	(*common.DeleteServiceResponse)(nil),               // 28: common.DeleteServiceResponse
}
var file_common_grpc_api_proto_whatsapp_public_api_proto_depIdxs = []int32{
	0,  // 0: api.PublicService.SendMessage:input_type -> common.SendMessageRequest
	1,  // 1: api.PublicService.RevokeMessage:input_type -> common.RevokeMessageRequest
	2,  // 2: api.PublicService.ForwardMessage:input_type -> common.ForwardMessageRequest
	3,  // 3: api.PublicService.SendReactionToMessage:input_type -> common.SendReactionToMessageRequest
	4,  // 4: api.PublicService.SendVCardContactMessage:input_type -> common.SendVCardContactMessageRequest
	5,  // 5: api.PublicService.CreateGroup:input_type -> common.CreateGroupRequest
	5,  // 6: api.PublicService.AddGroupParticipants:input_type -> common.CreateGroupRequest
	5,  // 7: api.PublicService.RemoveGroupParticipants:input_type -> common.CreateGroupRequest
	6,  // 8: api.PublicService.LoadEarlierMessagesTillDate:input_type -> common.LoadEarlierMessagesTillDateRequest
	7,  // 9: api.PublicService.Start:input_type -> common.ActionPayload
	7,  // 10: api.PublicService.Stop:input_type -> common.ActionPayload
	7,  // 11: api.PublicService.Restart:input_type -> common.ActionPayload
	7,  // 12: api.PublicService.Logout:input_type -> common.ActionPayload
	7,  // 13: api.PublicService.Takeover:input_type -> common.ActionPayload
	8,  // 14: api.PublicService.GetAccount:input_type -> google.protobuf.Empty
	9,  // 15: api.PublicService.SetAccountWebhook:input_type -> common.SetAccountWebhookUrlRequest
	10, // 16: api.PublicService.CreateService:input_type -> common.CreateServiceRequest
	11, // 17: api.PublicService.GetService:input_type -> common.GetServiceRequest
	12, // 18: api.PublicService.ListServices:input_type -> common.ListServicesRequest
	13, // 19: api.PublicService.UpdateService:input_type -> common.UpdateServiceRequest
	14, // 20: api.PublicService.DeleteService:input_type -> common.DeleteServiceRequest
	15, // 21: api.PublicService.SendMessage:output_type -> common.SendMessageResponse
	16, // 22: api.PublicService.RevokeMessage:output_type -> common.RevokeMessageResponse
	17, // 23: api.PublicService.ForwardMessage:output_type -> common.ForwardMessageResponse
	18, // 24: api.PublicService.SendReactionToMessage:output_type -> common.SendReactionToMessageResponse
	19, // 25: api.PublicService.SendVCardContactMessage:output_type -> common.SendVCardContactMessageResponse
	20, // 26: api.PublicService.CreateGroup:output_type -> common.CreateGroupResponse
	20, // 27: api.PublicService.AddGroupParticipants:output_type -> common.CreateGroupResponse
	20, // 28: api.PublicService.RemoveGroupParticipants:output_type -> common.CreateGroupResponse
	21, // 29: api.PublicService.LoadEarlierMessagesTillDate:output_type -> common.LoadEarlierMessagesTillDateResponse
	22, // 30: api.PublicService.Start:output_type -> common.ActionResponse
	22, // 31: api.PublicService.Stop:output_type -> common.ActionResponse
	22, // 32: api.PublicService.Restart:output_type -> common.ActionResponse
	22, // 33: api.PublicService.Logout:output_type -> common.ActionResponse
	22, // 34: api.PublicService.Takeover:output_type -> common.ActionResponse
	23, // 35: api.PublicService.GetAccount:output_type -> common.AccountResponse
	8,  // 36: api.PublicService.SetAccountWebhook:output_type -> google.protobuf.Empty
	24, // 37: api.PublicService.CreateService:output_type -> common.CreateServiceResponse
	25, // 38: api.PublicService.GetService:output_type -> common.GetServiceResponse
	26, // 39: api.PublicService.ListServices:output_type -> common.ListServicesResponse
	27, // 40: api.PublicService.UpdateService:output_type -> common.UpdateServiceResponse
	28, // 41: api.PublicService.DeleteService:output_type -> common.DeleteServiceResponse
	21, // [21:42] is the sub-list for method output_type
	0,  // [0:21] is the sub-list for method input_type
	0,  // [0:0] is the sub-list for extension type_name
	0,  // [0:0] is the sub-list for extension extendee
	0,  // [0:0] is the sub-list for field type_name
}

func init() { file_common_grpc_api_proto_whatsapp_public_api_proto_init() }
func file_common_grpc_api_proto_whatsapp_public_api_proto_init() {
	if File_common_grpc_api_proto_whatsapp_public_api_proto != nil {
		return
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: unsafe.Slice(unsafe.StringData(file_common_grpc_api_proto_whatsapp_public_api_proto_rawDesc), len(file_common_grpc_api_proto_whatsapp_public_api_proto_rawDesc)),
			NumEnums:      0,
			NumMessages:   0,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_common_grpc_api_proto_whatsapp_public_api_proto_goTypes,
		DependencyIndexes: file_common_grpc_api_proto_whatsapp_public_api_proto_depIdxs,
	}.Build()
	File_common_grpc_api_proto_whatsapp_public_api_proto = out.File
	file_common_grpc_api_proto_whatsapp_public_api_proto_goTypes = nil
	file_common_grpc_api_proto_whatsapp_public_api_proto_depIdxs = nil
}
