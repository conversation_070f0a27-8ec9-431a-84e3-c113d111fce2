package storage_adapter

import (
	"context"
	"digisac-go/worker/config"
	"errors"
	"fmt"
	"log/slog"
	"strconv"
	"strings"
	"sync"
)

var adapterCache = sync.Map{}

func NewAdapter(config *config.Config) *Adapter {
	return &Adapter{
		config: config,
	}
}

func (a *Adapter) oracleStorageToBucketName(ctx context.Context, storage string) (string, error) {
	number := strings.Split(storage, "oracle")[1]

	buckets := strings.Split(a.config.OracleBucketsNames, ",")

	if number == "" {
		return buckets[0], nil
	}

	index, err := strconv.ParseInt(number, 10, 64)

	if err != nil {
		slog.ErrorContext(ctx, "Failed to parse oracle storage number", slog.String("storage", storage), slog.String("error", err.Error()))
		return "", fmt.Errorf("failed to parse oracle storage number: %w", err)
	}

	if int(index)-1 >= len(buckets) || int(index)-1 < 0 {
		err := errors.New("index out of range for oracle buckets")
		slog.ErrorContext(ctx, "index out of range for oracle buckets",
			slog.Int64("index", index),
			slog.Int("len", len(buckets)),
			slog.String("storage", storage),
			slog.String("error", err.Error()))
		return "", fmt.Errorf("index out of range for oracle buckets: %w", err)
	}

	return buckets[index-1], nil
}

func (a *Adapter) InitAdapter(ctx context.Context, storage string) error {
	if cachedAdapter, ok := adapterCache.Load(storage); ok {
		a.AdapterInterface = cachedAdapter.(AdapterInterface)
		a.Storage = storage

		return nil
	}

	switch storage {
	case "s3":
		a.AdapterInterface = &S3Adapter{
			config: a.config,
		}
	default:
		if strings.Contains(storage, "oracle") {
			bucketName, err := a.oracleStorageToBucketName(ctx, storage)

			if err != nil {
				slog.ErrorContext(ctx, "Failed to get oracle bucket name", slog.String("storage", storage), slog.String("error", err.Error()))
				return fmt.Errorf("failed to get oracle bucket name: %w", err)
			}

			a.AdapterInterface = &OracleAdapter{
				config:     a.config,
				bucketName: bucketName,
			}
		} else {
			err := errors.New("adapter not found for file storage " + storage)
			slog.ErrorContext(ctx, "adapter not found", slog.String("storage", storage), slog.String("error", err.Error()))
			return fmt.Errorf("adapter not found for file storage %s: %w", storage, err)
		}
	}

	err := a.Connect(ctx)

	if err != nil {
		slog.ErrorContext(ctx, "Failed to connect adapter", slog.String("storage", storage), slog.String("error", err.Error()))
		return fmt.Errorf("failed to connect adapter: %w", err)
	}

	adapterCache.Store(storage, a.AdapterInterface)

	return nil
}
