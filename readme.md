# Envs

### Para usar /app/worker/.env é necessário um volume, exemplo: ###
```yml
    volumes:
        - .env-worker-go-back:/app/worker/.env
```
### Para usar direto as envs do SO: ###
```yml
    env_file:
        - .env-worker-go-back
```


# Instruções para Rodar Testes e Visualizar Cobertura

### Gerando Relatório de Cobertura de Testes

Para gerar um arquivo de cobertura e visualizá-lo em formato HTML, execute os seguintes comandos:

```bash
docker exec app-workers-go go test -coverprofile=cover.out ./...
docker exec app-workers-go go tool cover -html=cover.out -o cover.html
```

### Executando Todos os Testes
Para executar todos os testes no projeto utilizando o Docker, siga as instruções abaixo:

```bash
docker exec app-workers-go go test ./worker/...
```

### Executando com Saída Verbosa
Se quiser ver detalhes adicionais dos testes em execução (modo verboso), use o parâmetro -v

```bash
docker exec app-workers-go go test ./... -v
```

### Executando Testes em um Arquivo ou Diretório Específico
Para rodar testes em um diretório ou arquivo específico, use o caminho relativo do arquivo ou pasta:

```bash
docker exec app-workers-go go test ./worker/core/services/message -v
```

### Executando um Teste Específico
Para rodar um teste específico dentro de um arquivo, utilize a flag -run com o nome do teste:

```bash
docker exec app-workers-go go test ./worker/core/utils -run ^TestDistributedLockSuite$ -v
```

### Executando teste por tipo

```bash
go test -tags=unit ./...
go test -tags=integration ./...
go test -tags=e2e ./...

# Executa unit e inegration
go test -tags=unit,integration ./...

```
### Executar formatador de codigo ###
```bash
gofmt -s -w .
```

### Executar lint local para qualidade de codigo ###
```bash
go install github.com/golangci/golangci-lint/cmd/golangci-lint@latest
golangci-lint run --timeout=5m ./consumer/... ./worker/...
```


[![Commitizen-go friendly](https://img.shields.io/badge/commitizen-friendly-brightgreen.svg)](https://github.com/lintingzhen/commitizen-go)