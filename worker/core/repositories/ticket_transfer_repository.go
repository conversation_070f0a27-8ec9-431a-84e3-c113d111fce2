package repositories

import (
	"digisac-go/worker/core/models"

	"gorm.io/gorm"
)

type TicketTransferRepository interface {
	Repository[models.TicketTransfer]
}

type ticketTransferRepository struct {
	*GormRepository[models.TicketTransfer]
}

func NewTicketTransferRepository(db *gorm.DB) TicketTransferRepository {
	return &ticketTransferRepository{
		GormRepository: NewGormRepository[models.TicketTransfer](db),
	}
}
